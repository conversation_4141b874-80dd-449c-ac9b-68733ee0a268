#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度修复hkhsi50数据
==================

彻底修复所有缺失数据：
1. 删除有NULL值的记录
2. 重新获取完整的25年数据
3. 重新计算所有技术指标
4. 确保数据完整性

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import pymysql
from datetime import datetime, timedelta
import numpy as np

# 数据库配置
db_config = {
    'host': '************',
    'user': 'root',
    'password': '',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def backup_table():
    """备份原表"""
    print("💾 备份原表...")
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 创建备份表
        cursor.execute("DROP TABLE IF EXISTS hkhsi50_backup")
        cursor.execute("CREATE TABLE hkhsi50_backup AS SELECT * FROM hkhsi50")
        
        cursor.execute("SELECT COUNT(*) FROM hkhsi50_backup")
        backup_count = cursor.fetchone()[0]
        
        print(f"✅ 备份完成，备份了 {backup_count} 条记录到 hkhsi50_backup")
        
        conn.close()
        return True
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def clean_and_rebuild():
    """清理并重建表"""
    print("\n🧹 清理并重建hkhsi50表...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 删除原表
        cursor.execute("DROP TABLE IF EXISTS hkhsi50")
        
        # 重新创建表
        cursor.execute('''
        CREATE TABLE hkhsi50 (
            date DATE PRIMARY KEY,
            open DECIMAL(15,4),
            high DECIMAL(15,4),
            low DECIMAL(15,4),
            close DECIMAL(15,4),
            volume BIGINT,
            ma20 DECIMAL(15,4),
            ma60 DECIMAL(15,4),
            rsi DECIMAL(15,4),
            money_flow DECIMAL(15,4),
            base_x DECIMAL(15,4),
            x_value DECIMAL(15,4),
            base_y DECIMAL(15,4),
            y_value DECIMAL(15,4),
            e_value DECIMAL(15,4)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ''')
        
        print("✅ 表重建完成")
        
        conn.close()
        return True
    except Exception as e:
        print(f"❌ 重建表失败: {e}")
        return False

def fetch_complete_data():
    """获取完整的25年数据"""
    print("\n📥 获取完整的25年恒生指数数据...")
    
    try:
        # 获取25年数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=25*365)
        
        print(f"   • 获取日期范围: {start_date.date()} 至 {end_date.date()}")
        
        ticker = yf.Ticker("^HSI")
        df = ticker.history(start=start_date, end=end_date)
        
        if df.empty:
            print("❌ 未能获取数据")
            return None
        
        print(f"   • 成功获取 {len(df)} 条原始数据")
        
        # 计算技术指标
        df = calculate_indicators(df)
        
        # 移除有NaN的行
        initial_count = len(df)
        df = df.dropna()
        final_count = len(df)
        
        print(f"   • 移除NaN后剩余 {final_count} 条完整数据")
        print(f"   • 移除了 {initial_count - final_count} 条不完整数据")
        
        return df
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return None

def calculate_indicators(df):
    """计算技术指标"""
    print("🧮 计算技术指标...")
    
    # 计算移动平均线
    df['ma20'] = df['Close'].rolling(window=20).mean()
    df['ma60'] = df['Close'].rolling(window=60).mean()
    
    # 计算RSI
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # 计算资金流
    df['money_flow'] = df['Volume'] * (df['Close'] - df['Open']) / df['Open']
    
    # 计算基础X值（20日资金流向）
    pos_flow = df['money_flow'].where(df['money_flow'] > 0, 0).rolling(window=20).sum()
    neg_flow = (-df['money_flow'].where(df['money_flow'] < 0, 0)).rolling(window=20).sum()
    df['base_x'] = pos_flow / (pos_flow + neg_flow)
    
    # 计算最终X值
    rsi_adj = 0.3 * (df['rsi']/100 - 0.5)
    df['x_value'] = df['base_x'] + rsi_adj
    df['x_value'] = df['x_value'].clip(0.1, 0.9)
    
    # 计算基础Y值和最终Y值
    price_ma20_ratio = df['Close'] / df['ma20']
    df['base_y'] = pd.Series(index=df.index)
    mask = price_ma20_ratio >= 1
    df.loc[mask, 'base_y'] = 0.5 + 0.4 * np.tanh((price_ma20_ratio[mask] - 1) * 3)
    df.loc[~mask, 'base_y'] = 0.5 - 0.4 * np.tanh((1 - price_ma20_ratio[~mask]) * 3)
    
    # 趋势调整
    trend_adj = 0.1 * np.tanh((df['ma20'] / df['ma60'] - 1) * 2)
    
    # 成交量调整
    volume_ma20 = df['Volume'].rolling(window=20).mean()
    vol_adj = 0.05 * np.tanh((df['Volume'] / volume_ma20 - 1))
    
    df['y_value'] = (df['base_y'] + trend_adj + vol_adj).clip(0.1, 0.9)
    
    # 计算E值
    df['e_value'] = 8 * df['x_value'] * df['y_value'] - 3 * df['x_value'] - 3 * df['y_value'] + 1
    
    return df

def insert_complete_data(df):
    """插入完整数据"""
    print(f"\n💾 插入 {len(df)} 条完整数据...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 准备插入SQL
        sql = """
        INSERT INTO hkhsi50 (
            date, open, high, low, close, volume, 
            ma20, ma60, rsi, money_flow, 
            base_x, x_value, base_y, y_value, e_value
        ) VALUES (
            %s, %s, %s, %s, %s, %s, 
            %s, %s, %s, %s, 
            %s, %s, %s, %s, %s
        )
        """
        
        # 准备数据
        df.reset_index(inplace=True)
        df['Date'] = pd.to_datetime(df['Date']).dt.date
        
        data = []
        for _, row in df.iterrows():
            data.append((
                row['Date'], 
                row['Open'], row['High'], row['Low'], row['Close'], row['Volume'],
                row['ma20'], row['ma60'], row['rsi'], row['money_flow'],
                row['base_x'], row['x_value'], row['base_y'], row['y_value'], row['e_value']
            ))
        
        # 批量插入
        batch_size = 1000
        for i in range(0, len(data), batch_size):
            batch = data[i:i+batch_size]
            cursor.executemany(sql, batch)
            conn.commit()
            print(f"   • 已插入 {min(i+batch_size, len(data))}/{len(data)} 条记录")
        
        print("✅ 数据插入完成")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 插入数据失败: {e}")
        return False

def verify_complete_data():
    """验证完整数据"""
    print("\n✅ 验证数据完整性...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查总记录数
        cursor.execute("SELECT COUNT(*) FROM hkhsi50")
        total_count = cursor.fetchone()[0]
        
        # 检查缺失数据
        cursor.execute("""
            SELECT COUNT(*) FROM hkhsi50 
            WHERE close IS NULL OR volume IS NULL 
               OR x_value IS NULL OR y_value IS NULL 
               OR e_value IS NULL
        """)
        null_count = cursor.fetchone()[0]
        
        # 获取日期范围
        cursor.execute("SELECT MIN(date), MAX(date) FROM hkhsi50")
        date_range = cursor.fetchone()
        
        # 获取最新数据
        cursor.execute("""
            SELECT date, close, x_value, y_value, e_value 
            FROM hkhsi50 
            ORDER BY date DESC 
            LIMIT 1
        """)
        latest = cursor.fetchone()
        
        print(f"📊 深度修复后统计:")
        print(f"   • 总记录数: {total_count}")
        print(f"   • 日期范围: {date_range[0]} 至 {date_range[1]}")
        print(f"   • 缺失数据: {null_count} 条")
        
        if latest:
            print(f"\n📈 最新数据:")
            print(f"   • 日期: {latest[0]}")
            print(f"   • 收盘价: {latest[1]:.2f}")
            print(f"   • X值: {latest[2]:.3f}")
            print(f"   • Y值: {latest[3]:.3f}")
            print(f"   • E值: {latest[4]:.3f}")
        
        conn.close()
        
        if null_count == 0:
            print("🎉 数据完全修复！没有任何缺失数据")
            return True
        else:
            print(f"⚠️ 仍有 {null_count} 条缺失数据")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 hkhsi50深度数据修复工具")
    print("="*50)
    print("⚠️  警告：此操作将完全重建hkhsi50表")
    print("⚠️  原数据将备份到hkhsi50_backup表")
    
    # 确认操作
    confirm = input("\n是否继续？(y/N): ").lower().strip()
    if confirm != 'y':
        print("❌ 操作已取消")
        return
    
    # 1. 备份原表
    if not backup_table():
        return
    
    # 2. 清理并重建表
    if not clean_and_rebuild():
        return
    
    # 3. 获取完整数据
    df = fetch_complete_data()
    if df is None:
        return
    
    # 4. 插入完整数据
    if not insert_complete_data(df):
        return
    
    # 5. 验证结果
    verify_complete_data()
    
    print("\n🎉 深度修复完成！")
    print("💡 如果需要恢复原数据，可以使用：")
    print("   DROP TABLE hkhsi50;")
    print("   RENAME TABLE hkhsi50_backup TO hkhsi50;")

if __name__ == "__main__":
    main()
