#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
包含持仓不动逻辑的改进版回测
==========================

新的交易逻辑：
1. 跟踪持仓状态 (多仓/空仓/现金)
2. 根据当前仓位和市场状态智能决策
3. 持仓不动：在合适条件下保持现有仓位
4. 减少不必要的交易频率

策略规则：
- 高值盈利区: 持多仓或买涨
- 控股商控制区: 持仓不动
- 强亏损区: 持空仓或买跌
- 其他区域: 持仓不动或轻仓操作

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
from datetime import datetime

def backtest_with_position_holding():
    """包含持仓不动逻辑的回测"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("🎯 包含持仓不动逻辑的改进版回测")
        print("="*80)
        print("📊 新的交易逻辑:")
        print("   • 跟踪持仓状态 (多仓/空仓/现金)")
        print("   • 智能决策：根据当前仓位和市场状态")
        print("   • 持仓不动：保持现有仓位，减少交易")
        print("   • 只在必要时才进行买卖操作")
        print("="*80)
        
        # 1. 获取所有记录
        cursor.execute("""
            SELECT 交易序号, 开仓日期, close, `控制系数`, `资金流比例`,
                   CASE 
                       WHEN `控制系数` > 0.4 AND `资金流比例` > 0.4 THEN '高值盈利区'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                       ELSE '其他区域'
                   END AS 策略区域
            FROM test 
            ORDER BY 交易序号
        """)
        
        records = cursor.fetchall()
        
        # 2. 初始化回测参数
        initial_capital = 30000
        current_capital = initial_capital
        
        # 持仓状态跟踪
        position_type = 'cash'  # 'cash', 'long', 'short'
        position_shares = 0
        position_entry_price = 0
        position_entry_date = None
        position_entry_zone = None
        
        # 交易记录
        trades = []
        daily_positions = []
        
        # 统计变量
        total_trades = 0
        hold_periods = 0
        position_changes = 0
        
        print(f"\n📊 持仓状态回测详细过程:")
        print("-" * 160)
        print(f"{'序号':<4} {'日期':<12} {'策略区域':<12} {'价格':<8} {'当前仓位':<10} "
              f"{'决策':<12} {'新仓位':<10} {'交易股数':<8} {'资金变化':<10} {'总资金':<10}")
        print("-" * 160)
        
        # 3. 逐日回测
        for i, record in enumerate(records):
            trade_id, date, price, y_val, x_val, zone = record
            price = float(price)
            
            # 确定策略决策
            action, reason = determine_action(position_type, zone, y_val, x_val)
            
            # 执行决策
            capital_change = 0
            new_position_type = position_type
            new_position_shares = position_shares
            new_entry_price = position_entry_price
            trade_shares = 0
            
            if action == 'hold':
                hold_periods += 1
                # 持仓不动，只更新市值
                if position_type != 'cash':
                    # 计算当前持仓市值变化（仅用于显示，不实际结算）
                    if position_type == 'long':
                        unrealized_pnl = (price - position_entry_price) * position_shares
                    else:  # short
                        unrealized_pnl = (position_entry_price - price) * position_shares
                else:
                    unrealized_pnl = 0
            
            elif action == 'buy_long':
                # 买入做多
                if position_type == 'cash':
                    # 从现金买入
                    trade_shares = 100
                    cost = trade_shares * price
                    transaction_cost = cost * 0.0025
                    
                    if current_capital >= cost + transaction_cost:
                        current_capital -= (cost + transaction_cost)
                        capital_change = -(cost + transaction_cost)
                        
                        new_position_type = 'long'
                        new_position_shares = trade_shares
                        new_entry_price = price
                        
                        total_trades += 1
                        position_changes += 1
                
                elif position_type == 'short':
                    # 先平空仓，再买多仓
                    # 平空仓
                    close_cost = position_shares * price
                    close_transaction_cost = close_cost * 0.0025
                    short_pnl = (position_entry_price - price) * position_shares
                    current_capital += close_cost + short_pnl - close_transaction_cost
                    
                    # 买多仓
                    trade_shares = 100
                    open_cost = trade_shares * price
                    open_transaction_cost = open_cost * 0.0025
                    
                    if current_capital >= open_cost + open_transaction_cost:
                        current_capital -= (open_cost + open_transaction_cost)
                        capital_change = short_pnl - close_transaction_cost - open_cost - open_transaction_cost
                        
                        new_position_type = 'long'
                        new_position_shares = trade_shares
                        new_entry_price = price
                        
                        total_trades += 2  # 平仓+开仓
                        position_changes += 1
                
                else:  # position_type == 'long'
                    # 已经是多仓，持仓不动
                    hold_periods += 1
            
            elif action == 'buy_short':
                # 买入做空
                if position_type == 'cash':
                    # 从现金做空
                    trade_shares = 100
                    margin = trade_shares * price
                    transaction_cost = margin * 0.0025
                    
                    if current_capital >= margin + transaction_cost:
                        current_capital -= transaction_cost  # 只扣手续费，保证金不动用
                        capital_change = -transaction_cost
                        
                        new_position_type = 'short'
                        new_position_shares = trade_shares
                        new_entry_price = price
                        
                        total_trades += 1
                        position_changes += 1
                
                elif position_type == 'long':
                    # 先平多仓，再做空
                    # 平多仓
                    close_value = position_shares * price
                    close_transaction_cost = close_value * 0.0025
                    long_pnl = (price - position_entry_price) * position_shares
                    current_capital += close_value + long_pnl - close_transaction_cost
                    
                    # 做空
                    trade_shares = 100
                    margin = trade_shares * price
                    open_transaction_cost = margin * 0.0025
                    current_capital -= open_transaction_cost
                    
                    capital_change = long_pnl - close_transaction_cost - open_transaction_cost
                    
                    new_position_type = 'short'
                    new_position_shares = trade_shares
                    new_entry_price = price
                    
                    total_trades += 2  # 平仓+开仓
                    position_changes += 1
                
                else:  # position_type == 'short'
                    # 已经是空仓，持仓不动
                    hold_periods += 1
            
            elif action == 'close_position':
                # 平仓
                if position_type == 'long':
                    close_value = position_shares * price
                    close_transaction_cost = close_value * 0.0025
                    long_pnl = (price - position_entry_price) * position_shares
                    current_capital += close_value + long_pnl - close_transaction_cost
                    capital_change = long_pnl - close_transaction_cost
                    
                elif position_type == 'short':
                    close_cost = position_shares * price
                    close_transaction_cost = close_cost * 0.0025
                    short_pnl = (position_entry_price - price) * position_shares
                    current_capital += close_cost + short_pnl - close_transaction_cost
                    capital_change = short_pnl - close_transaction_cost
                
                new_position_type = 'cash'
                new_position_shares = 0
                new_entry_price = 0
                
                total_trades += 1
                position_changes += 1
            
            # 记录交易
            if action != 'hold' and capital_change != 0:
                trades.append({
                    'date': date,
                    'trade_id': trade_id,
                    'zone': zone,
                    'action': action,
                    'price': price,
                    'shares': trade_shares,
                    'capital_change': capital_change,
                    'total_capital': current_capital,
                    'position_type': new_position_type,
                    'reason': reason
                })
            
            # 更新持仓状态
            position_type = new_position_type
            position_shares = new_position_shares
            position_entry_price = new_entry_price
            if new_position_type != 'cash':
                position_entry_date = date
                position_entry_zone = zone
            
            # 记录每日状态
            daily_positions.append({
                'date': date,
                'zone': zone,
                'price': price,
                'position_type': position_type,
                'position_shares': position_shares,
                'capital': current_capital,
                'action': action
            })
            
            # 显示前30条记录
            if i < 30:
                position_display = f"{position_type}({position_shares})" if position_type != 'cash' else 'cash'
                new_position_display = f"{new_position_type}({new_position_shares})" if new_position_type != 'cash' else 'cash'
                
                print(f"{trade_id:<4} {str(date):<12} {zone:<12} {price:<8.2f} {position_display:<10} "
                      f"{action:<12} {new_position_display:<10} {trade_shares:<8} {capital_change:<10.0f} {current_capital:<10.0f}")
        
        print("\n" + "="*100)
        
        # 4. 最终平仓（如果有持仓）
        if position_type != 'cash':
            final_price = float(records[-1][2])  # 最后一天的价格
            if position_type == 'long':
                final_value = position_shares * final_price
                final_pnl = (final_price - position_entry_price) * position_shares
                current_capital += final_value + final_pnl
            else:  # short
                final_cost = position_shares * final_price
                final_pnl = (position_entry_price - final_price) * position_shares
                current_capital += final_cost + final_pnl
            
            print(f"📊 最终平仓: {position_type}仓位，盈亏{final_pnl:+.0f}港币")
        
        # 5. 回测结果统计
        print(f"\n📈 持仓不动策略回测结果:")
        print(f"   • 初始资金: {initial_capital:,}港币")
        print(f"   • 最终资金: {current_capital:,.0f}港币")
        print(f"   • 总收益: {current_capital - initial_capital:+,.0f}港币")
        print(f"   • 总收益率: {(current_capital/initial_capital-1)*100:+.2f}%")
        
        print(f"\n📊 交易行为统计:")
        print(f"   • 总记录数: {len(records)}")
        print(f"   • 实际交易次数: {total_trades}")
        print(f"   • 持仓不动次数: {hold_periods}")
        print(f"   • 仓位变化次数: {position_changes}")
        print(f"   • 持仓不动比例: {hold_periods/len(records)*100:.1f}%")
        print(f"   • 交易频率: {total_trades/len(records)*100:.1f}%")
        
        # 6. 按策略区域分析
        print(f"\n📊 按策略区域分析持仓行为:")
        print("-" * 80)
        
        zones = ['高值盈利区', '强亏损区', '其他区域', '控股商控制区']
        
        for zone_name in zones:
            zone_records = [r for r in daily_positions if r['zone'] == zone_name]
            if zone_records:
                zone_count = len(zone_records)
                zone_holds = len([r for r in zone_records if r['action'] == 'hold'])
                zone_trades = zone_count - zone_holds
                
                print(f"• {zone_name}:")
                print(f"  - 出现次数: {zone_count}")
                print(f"  - 持仓不动: {zone_holds}次 ({zone_holds/zone_count*100:.1f}%)")
                print(f"  - 交易操作: {zone_trades}次 ({zone_trades/zone_count*100:.1f}%)")
        
        # 7. 交易记录详情
        if trades:
            print(f"\n📋 实际交易记录 (共{len(trades)}笔):")
            print("-" * 120)
            print(f"{'日期':<12} {'策略区域':<12} {'操作':<12} {'价格':<8} {'股数':<6} {'盈亏':<10} {'原因':<20}")
            print("-" * 120)
            
            for trade in trades[:20]:  # 显示前20笔交易
                print(f"{str(trade['date']):<12} {trade['zone']:<12} {trade['action']:<12} "
                      f"{trade['price']:<8.2f} {trade['shares']:<6} {trade['capital_change']:<10.0f} {trade['reason']:<20}")
        
        connection.close()
        print(f"\n🎉 持仓不动策略回测完成!")
        
    except Exception as e:
        print(f"❌ 持仓不动回测失败: {e}")

def determine_action(current_position, zone, y_val, x_val):
    """
    根据当前仓位和市场状态确定行动
    
    Args:
        current_position: 'cash', 'long', 'short'
        zone: 策略区域
        y_val, x_val: 控制系数和资金流比例
    
    Returns:
        (action, reason): 行动和原因
    """
    
    if zone == '高值盈利区':
        if current_position == 'cash':
            return 'buy_long', '空仓转多仓'
        elif current_position == 'short':
            return 'buy_long', '空仓转多仓'
        else:  # long
            return 'hold', '保持多仓'
    
    elif zone == '控股商控制区':
        return 'hold', '控制区持仓不动'
    
    elif zone == '强亏损区':
        if current_position == 'cash':
            return 'buy_short', '空仓转空仓'
        elif current_position == 'long':
            return 'buy_short', '多仓转空仓'
        else:  # short
            return 'hold', '保持空仓'
    
    else:  # 其他区域
        return 'hold', '其他区域持仓不动'

if __name__ == "__main__":
    backtest_with_position_holding()
