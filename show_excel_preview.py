#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示Excel文件预览
================

显示刚生成的Excel文件内容预览

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import glob
import os

def show_latest_excel():
    """显示最新生成的Excel文件内容"""
    # 查找最新的Excel文件
    excel_files = glob.glob("Cosmoon策略交易记录_*.xlsx")
    if not excel_files:
        print("❌ 未找到Excel文件")
        return
    
    # 获取最新文件
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"📄 显示文件: {latest_file}")
    
    try:
        # 读取交易记录
        df_trades = pd.read_excel(latest_file, sheet_name='交易记录')
        df_summary = pd.read_excel(latest_file, sheet_name='汇总统计')
        
        print(f"\n📊 交易记录预览 (共{len(df_trades)}条):")
        print("="*120)
        
        # 显示前10条记录
        display_columns = ['交易序号', '开仓日期', '平仓日期', '交易方向', '开仓价格', '平仓价格', 
                          '交易股数', '净利润', '收益率%', 'Y值', 'X值', 'E值', '策略区域']
        
        print(df_trades[display_columns].head(10).to_string(index=False))
        
        if len(df_trades) > 10:
            print(f"\n... (显示前10条，共{len(df_trades)}条记录)")
        
        print(f"\n📈 汇总统计:")
        print("="*50)
        for _, row in df_summary.iterrows():
            print(f"{row['项目']}: {row['数值']}")
        
        print(f"\n💡 Excel文件包含以下信息:")
        print(f"   • 详细交易记录：开仓/平仓日期、价格、股数、盈亏")
        print(f"   • Y值、X值、E值：每笔交易的博弈论参数")
        print(f"   • 策略区域：高值盈利区、强亏损区、其他区域等")
        print(f"   • 交易理由：基于博弈论的具体交易逻辑")
        print(f"   • 汇总统计：总收益、胜率、平均盈亏等")
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")

if __name__ == "__main__":
    show_latest_excel()
