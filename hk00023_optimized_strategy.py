#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HK00023优化策略回测
==================

最新优化策略参数：
- 高值盈利区 (Y>0.43, X>0.43): 买涨，止盈+2%，止损-1%
- 控股商控制区 (0.333<Y<0.4): 观望
- 强亏损区 (Y<0.25或X<0.25): 买涨，止盈+2%，止损-1%
- 其他区域: 买跌，止盈-2%，止损+1%

生成100条交易记录

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class HK00023OptimizedStrategy:
    def __init__(self):
        """初始化HK00023优化策略"""
        self.symbol = "0023.HK"
        self.initial_capital = 30000
        self.data = None
        
        # 最新优化策略参数
        self.strategy_params = {
            'high_profit_y': 0.43,      # 进一步降低到0.43
            'high_profit_x': 0.43,      # 进一步降低到0.43
            'control_zone_min': 0.333,
            'control_zone_max': 0.4,
            'strong_loss_y': 0.25,
            'strong_loss_x': 0.25,
            
            # 更保守的止盈止损
            'take_profit': 0.02,        # 2%止盈
            'stop_loss': 0.01,          # 1%止损
            'transaction_cost': 0.0025,
        }
        
    def fetch_hk00023_data(self):
        """获取HK00023数据"""
        print("🏦 获取HK00023东亚银行20年历史数据...")
        
        try:
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(period="20y", interval="1d")
            
            if self.data.empty:
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取HK00023数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data)} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算Y、X、E值"""
        print("🎯 计算优化后的Y、X、E值...")
        
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        self.data['ma_120'] = self.data['close'].rolling(window=120).mean()

        # 均值回归中值计算
        self.data['mean_reversion_center'] = (self.data['ma_20'] + self.data['ma_60'] + self.data['ma_120']) / 3
        self.data['price_vs_center'] = self.data['close'] / self.data['mean_reversion_center']
        self.data['deviation_from_center'] = (self.data['close'] - self.data['mean_reversion_center']) / self.data['mean_reversion_center'] * 100
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # Y值计算
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
        
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        # X值计算
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        rsi_adjustment = 0.3 * (self.data['rsi'] / 100 - 0.5)
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        # E值计算
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        # 优化后的策略区域分类
        conditions = [
            (self.data['y_probability'] > self.strategy_params['high_profit_y']) & 
            (self.data['inflow_ratio'] > self.strategy_params['high_profit_x']),  # 高值盈利区 Y>0.43, X>0.43
            
            (self.data['y_probability'] > self.strategy_params['control_zone_min']) & 
            (self.data['y_probability'] < self.strategy_params['control_zone_max']),  # 控股商控制区
            
            (self.data['y_probability'] < self.strategy_params['strong_loss_y']) | 
            (self.data['inflow_ratio'] < self.strategy_params['strong_loss_x']),  # 强亏损区
        ]
        
        choices = ['高值盈利区', '控股商控制区', '强亏损区']
        self.data['strategy_zone'] = np.select(conditions, choices, default='其他区域')
        
        # 统计优化后的区域分布
        zone_counts = self.data['strategy_zone'].value_counts()
        total = len(self.data)
        print(f"📊 优化后策略区域分布 (Y>0.43, X>0.43):")
        for zone, count in zone_counts.items():
            print(f"   • {zone}: {count} 天 ({count/total*100:.1f}%)")
    
    def generate_optimized_trades(self):
        """生成优化策略的100条交易记录"""
        print("🎯 生成优化策略100条交易记录...")
        
        trades = []
        current_cash = self.initial_capital
        
        # 使用更长的时间范围
        start_date = pd.to_datetime('2015-01-01', utc=True).tz_convert('Asia/Hong_Kong')
        trading_data = self.data[self.data['date'] >= start_date].copy()
        
        if len(trading_data) < 200:
            print("❌ 可用交易数据不足")
            return []
        
        trade_count = 0
        i = 60
        
        while trade_count < 100 and i < len(trading_data) - 10:
            row = trading_data.iloc[i]
            
            date = row['date']
            price = row['close']
            y_val = row['y_probability']
            x_val = row['inflow_ratio']
            e_val = row['e_value']
            zone = row['strategy_zone']
            
            # 跳过控股商控制区
            if zone == '控股商控制区':
                i += 1
                continue
            
            # 确定交易策略
            action = None
            direction = None
            reason = None
            
            if zone == '高值盈利区':
                action = '买入'
                direction = '做多'
                reason = f'高值盈利区：Y={y_val:.3f}>0.43且X={x_val:.3f}>0.43，E={e_val:.3f}，买涨策略'
            elif zone == '强亏损区':
                action = '买入'
                direction = '做多'
                reason = f'强亏损区：Y={y_val:.3f}<0.25或X={x_val:.3f}<0.25，低位反弹，买涨策略'
            elif zone == '其他区域':
                action = '卖出'
                direction = '做空'
                reason = f'其他区域：Y={y_val:.3f}，X={x_val:.3f}，E={e_val:.3f}，买跌策略'
            
            if action and current_cash > 3000:
                # 计算仓位
                position_ratio = 0.12  # 12%仓位
                position_value = current_cash * position_ratio
                shares = int(position_value / price / 100) * 100
                actual_value = shares * price
                
                if shares >= 100:
                    # 模拟持仓期
                    holding_days = np.random.randint(2, 8)
                    exit_index = min(i + holding_days, len(trading_data) - 1)
                    exit_row = trading_data.iloc[exit_index]
                    exit_price = exit_row['close']
                    exit_date = exit_row['date']
                    
                    # 计算盈亏 - 使用新的止盈止损参数
                    if direction == '做多':
                        profit_pct = (exit_price - price) / price
                        if profit_pct >= self.strategy_params['take_profit']:  # 止盈2%
                            exit_reason = '止盈'
                            profit_pct = self.strategy_params['take_profit']
                            exit_price = price * (1 + self.strategy_params['take_profit'])
                        elif profit_pct <= -self.strategy_params['stop_loss']:  # 止损1%
                            exit_reason = '止损'
                            profit_pct = -self.strategy_params['stop_loss']
                            exit_price = price * (1 - self.strategy_params['stop_loss'])
                        else:
                            exit_reason = '到期平仓'
                        
                        profit = profit_pct * actual_value
                    else:  # 做空
                        profit_pct = (price - exit_price) / price
                        if profit_pct >= self.strategy_params['take_profit']:  # 止盈2%
                            exit_reason = '止盈'
                            profit_pct = self.strategy_params['take_profit']
                            exit_price = price * (1 - self.strategy_params['take_profit'])
                        elif profit_pct <= -self.strategy_params['stop_loss']:  # 止损1%
                            exit_reason = '止损'
                            profit_pct = -self.strategy_params['stop_loss']
                            exit_price = price * (1 + self.strategy_params['stop_loss'])
                        else:
                            exit_reason = '到期平仓'
                        
                        profit = profit_pct * actual_value
                    
                    # 交易成本
                    transaction_cost = actual_value * self.strategy_params['transaction_cost'] * 2
                    net_profit = profit - transaction_cost
                    
                    # 更新现金
                    current_cash += net_profit
                    
                    # 计算均值回归相关数据
                    mean_center = row['mean_reversion_center']
                    price_vs_center = row['price_vs_center']
                    deviation = row['deviation_from_center']

                    # 记录交易
                    trade_record = {
                        '交易序号': trade_count + 1,
                        '股票代码': 'HK00023',
                        '股票名称': '东亚银行',
                        '开仓日期': date.strftime('%Y-%m-%d'),
                        '平仓日期': exit_date.strftime('%Y-%m-%d'),
                        '持仓天数': holding_days,
                        '交易方向': direction,
                        '开仓价格': round(price, 2),
                        '平仓价格': round(exit_price, 2),
                        '均值回归中值': round(mean_center, 2),
                        '价格偏离度%': round(deviation, 2),
                        '价格中值比': round(price_vs_center, 3),
                        '交易股数': shares,
                        '交易金额': round(actual_value, 0),
                        '毛利润': round(profit, 0),
                        '交易成本': round(transaction_cost, 0),
                        '净利润': round(net_profit, 0),
                        '收益率%': round(profit_pct * 100, 2),
                        'Y值': round(y_val, 3),
                        'X值': round(x_val, 3),
                        'E值': round(e_val, 3),
                        '策略区域': zone,
                        '交易理由': reason,
                        '平仓原因': exit_reason,
                        '账户余额': round(current_cash, 0)
                    }
                    
                    trades.append(trade_record)
                    trade_count += 1
                    
                    if trade_count % 10 == 0:
                        print(f"已生成 {trade_count} 条优化交易记录...")
            
            # 随机间隔
            i += np.random.randint(3, 8)
        
        return trades
    
    def create_optimized_excel(self, trades):
        """创建优化策略Excel文件"""
        print("📄 创建优化策略Excel文件...")
        
        if not trades:
            return None
        
        # 创建交易记录DataFrame
        df_trades = pd.DataFrame(trades)
        
        # 计算汇总统计
        total_trades = len(df_trades)
        winning_trades = len(df_trades[df_trades['净利润'] > 0])
        losing_trades = len(df_trades[df_trades['净利润'] < 0])
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        total_profit = df_trades['净利润'].sum()
        final_capital = df_trades['账户余额'].iloc[-1] if len(df_trades) > 0 else self.initial_capital
        total_return = (final_capital / self.initial_capital - 1) * 100
        
        # 创建汇总统计
        summary_data = {
            '项目': [
                '股票代码', '股票名称', '策略版本', '初始资金(港币)', '最终资金(港币)', '总盈亏(港币)', 
                '总收益率(%)', '总交易次数', '盈利次数', '亏损次数', '胜率(%)', 
                '平均每笔盈亏(港币)', '最大单笔盈利(港币)', '最大单笔亏损(港币)',
                '做多次数', '做空次数', '高值盈利区交易', '强亏损区交易', '其他区域交易',
                '止盈次数', '止损次数', '到期平仓次数'
            ],
            '数值': [
                'HK00023', '东亚银行', 'Y>0.43,X>0.43,止盈2%止损1%', self.initial_capital, final_capital, total_profit,
                round(total_return, 2), total_trades, winning_trades, losing_trades, round(win_rate, 1),
                round(total_profit/total_trades, 0) if total_trades > 0 else 0,
                df_trades['净利润'].max(), df_trades['净利润'].min(),
                len(df_trades[df_trades['交易方向'] == '做多']),
                len(df_trades[df_trades['交易方向'] == '做空']),
                len(df_trades[df_trades['策略区域'] == '高值盈利区']),
                len(df_trades[df_trades['策略区域'] == '强亏损区']),
                len(df_trades[df_trades['策略区域'] == '其他区域']),
                len(df_trades[df_trades['平仓原因'] == '止盈']),
                len(df_trades[df_trades['平仓原因'] == '止损']),
                len(df_trades[df_trades['平仓原因'] == '到期平仓'])
            ]
        }
        summary_df = pd.DataFrame(summary_data)
        
        # 创建优化策略说明
        strategy_explanation = {
            '策略区域': ['高值盈利区', '强亏损区', '其他区域', '控股商控制区'],
            '条件': [
                'Y>0.43 且 X>0.43 (优化后)',
                'Y<0.25 或 X<0.25',
                '其他情况',
                '0.333<Y<0.4'
            ],
            '操作': ['买涨(做多)', '买涨(做多)', '买跌(做空)', '观望'],
            '止盈': ['2%', '2%', '2%', '-'],
            '止损': ['1%', '1%', '1%', '-'],
            '说明': [
                '优化后：Y>0.43且X>0.43，进一步降低门槛，更保守止盈止损',
                '控股商不托价或资金流出，低位反弹机会，保守止盈止损',
                '在不确定区域做空获利，保守止盈止损',
                '控股商控制市场的危险区域，避免交易'
            ]
        }
        strategy_df = pd.DataFrame(strategy_explanation)

        # 创建均值回归分析说明
        mean_reversion_explanation = {
            '指标名称': ['均值回归中值', '价格偏离度%', '价格中值比', 'MA20', 'MA60', 'MA120'],
            '计算方法': [
                '(MA20 + MA60 + MA120) / 3',
                '(当前价格 - 均值回归中值) / 均值回归中值 × 100',
                '当前价格 / 均值回归中值',
                '20日移动平均线',
                '60日移动平均线',
                '120日移动平均线'
            ],
            '意义': [
                '多周期均线的综合中值，代表长期均衡价格',
                '当前价格相对于均值的偏离程度，正值表示高估，负值表示低估',
                '价格相对于中值的倍数关系，>1表示高于均值，<1表示低于均值',
                '短期趋势指标，反映近期价格走势',
                '中期趋势指标，反映中期价格走势',
                '长期趋势指标，反映长期价格走势'
            ],
            '交易应用': [
                '作为买卖决策的参考基准，价格远离中值时考虑反向操作',
                '偏离度>5%可能超买，<-5%可能超卖，结合其他指标判断',
                '>1.05可能高估，<0.95可能低估，适合均值回归策略',
                '价格突破MA20可能是短期趋势转换信号',
                '价格突破MA60可能是中期趋势转换信号',
                '价格突破MA120可能是长期趋势转换信号'
            ]
        }
        mean_reversion_df = pd.DataFrame(mean_reversion_explanation)
        
        # 创建Excel文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"HK00023优化策略交易记录_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 写入各个工作表
            df_trades.to_excel(writer, sheet_name='优化策略交易记录', index=False)
            summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
            strategy_df.to_excel(writer, sheet_name='优化策略说明', index=False)
            mean_reversion_df.to_excel(writer, sheet_name='均值回归分析', index=False)
            
            # 设置格式
            workbook = writer.book
            
            # 交易记录表格式
            worksheet1 = writer.sheets['优化策略交易记录']
            column_widths = {
                'A': 8, 'B': 10, 'C': 10, 'D': 12, 'E': 12, 'F': 8, 'G': 8, 'H': 10, 'I': 10, 'J': 12, 'K': 12, 'L': 12,
                'M': 10, 'N': 12, 'O': 10, 'P': 10, 'Q': 10, 'R': 10, 'S': 8, 'T': 8, 'U': 8, 'V': 12, 'W': 60, 'X': 12, 'Y': 12
            }
            for col, width in column_widths.items():
                worksheet1.column_dimensions[col].width = width
            
            # 汇总统计表格式
            worksheet2 = writer.sheets['汇总统计']
            worksheet2.column_dimensions['A'].width = 25
            worksheet2.column_dimensions['B'].width = 20
            
            # 策略说明表格式
            worksheet3 = writer.sheets['优化策略说明']
            worksheet3.column_dimensions['A'].width = 15
            worksheet3.column_dimensions['B'].width = 30
            worksheet3.column_dimensions['C'].width = 15
            worksheet3.column_dimensions['D'].width = 8
            worksheet3.column_dimensions['E'].width = 8
            worksheet3.column_dimensions['F'].width = 60

            # 均值回归分析表格式
            worksheet4 = writer.sheets['均值回归分析']
            worksheet4.column_dimensions['A'].width = 15
            worksheet4.column_dimensions['B'].width = 40
            worksheet4.column_dimensions['C'].width = 60
            worksheet4.column_dimensions['D'].width = 60
        
        print(f"✅ 优化策略Excel文件已创建: {filename}")
        return filename

def main():
    """主函数"""
    print("🏦 HK00023东亚银行优化策略回测")
    print("="*70)
    print("💰 初始资金: 30,000港币")
    print("🎯 优化策略: Y>0.43, X>0.43")
    print("📊 保守止盈止损: 止盈2%, 止损1%")
    print("📈 目标: 生成100条优化交易记录")
    
    # 创建优化策略
    strategy = HK00023OptimizedStrategy()
    
    # 获取数据
    if not strategy.fetch_hk00023_data():
        return
    
    # 计算指标
    strategy.calculate_indicators()
    
    # 生成优化交易
    trades = strategy.generate_optimized_trades()
    
    if not trades:
        print("❌ 未能生成交易记录")
        return
    
    # 创建Excel文件
    filename = strategy.create_optimized_excel(trades)
    
    if filename:
        print(f"\n🎉 HK00023优化策略回测完成!")
        print(f"📄 文件名: {filename}")
        print(f"📊 包含 {len(trades)} 条优化交易记录")
        print(f"🎯 优化参数: Y>0.43, X>0.43, 止盈2%, 止损1%")
        print(f"💡 请打开Excel文件查看详细优化策略结果")

if __name__ == "__main__":
    main()
