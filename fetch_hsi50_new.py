#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取恒生50指数数据
=================

从Yahoo Finance获取恒生50指数数据并存入SQLite数据库
数据库：finance.db
表名：hkhsi50

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta

def check_database():
    """检查数据库中的表"""
    conn = sqlite3.connect('finance.db')
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print("现有表:", [table[0] for table in tables])
    conn.close()
    return [table[0] for table in tables]

def create_table():
    """创建数据表"""
    print("创建数据表...")
    conn = sqlite3.connect('finance.db')
    cursor = conn.cursor()
    
    # 删除已存在的表
    cursor.execute("DROP TABLE IF EXISTS hkhsi50")
    
    # 创建新表
    cursor.execute('''
    CREATE TABLE hkhsi50 (
        date TEXT PRIMARY KEY,
        open REAL,
        high REAL,
        low REAL,
        close REAL,
        volume REAL,
        ma20 REAL,
        ma60 REAL,
        rsi REAL,
        money_flow REAL,
        base_x REAL,
        x_value REAL,
        base_y REAL,
        y_value REAL,
        e_value REAL
    )
    ''')
    
    conn.commit()
    conn.close()
    print("✅ 数据表创建完成")

def calculate_indicators(df):
    """计算技术指标"""
    print("计算技术指标...")
    
    # 计算MA
    df['ma20'] = df['Close'].rolling(window=20).mean()
    df['ma60'] = df['Close'].rolling(window=60).mean()
    
    # 计算RSI
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # 计算资金流
    df['money_flow'] = df['Volume'] * (df['Close'] - df['Open']) / df['Open']
    
    # 基础X值计算
    pos_flow = df['money_flow'].where(df['money_flow'] > 0, 0).rolling(window=20).sum()
    neg_flow = (-df['money_flow'].where(df['money_flow'] < 0, 0)).rolling(window=20).sum()
    df['base_x'] = pos_flow / (pos_flow + neg_flow)
    df['base_x'] = df['base_x'].fillna(0.5)
    
    # X值计算
    rsi_adj = 0.3 * (df['rsi']/100 - 0.5)
    df['x_value'] = df['base_x'] + rsi_adj
    df['x_value'] = df['x_value'].clip(0.1, 0.9)
    
    # Y值计算
    price_ma20_ratio = df['Close'] / df['ma20']
    df['base_y'] = pd.Series(index=df.index)
    mask = price_ma20_ratio >= 1
    df.loc[mask, 'base_y'] = 0.5 + 0.4 * np.tanh((price_ma20_ratio[mask] - 1) * 3)
    df.loc[~mask, 'base_y'] = 0.5 - 0.4 * np.tanh((1 - price_ma20_ratio[~mask]) * 3)
    
    trend_adj = 0.1 * np.tanh((df['ma20'] / df['ma60'] - 1) * 2)
    volume_ma20 = df['Volume'].rolling(window=20).mean()
    vol_adj = 0.05 * np.tanh((df['Volume'] / volume_ma20 - 1))
    
    df['y_value'] = (df['base_y'] + trend_adj + vol_adj).clip(0.1, 0.9)
    
    # E值计算
    df['e_value'] = 8 * df['x_value'] * df['y_value'] - 3 * df['x_value'] - 3 * df['y_value'] + 1
    
    # 处理NaN值
    df = df.fillna(method='ffill').fillna(method='bfill')
    
    print("✅ 指标计算完成")
    return df

def fetch_data():
    """获取恒生指数数据"""
    print("\n📊 开始获取恒生50指数数据...")
    
    # 检查数据库现状
    print("\n当前数据库状态：")
    check_database()
    
    # 创建表
    create_table()
    
    try:
        # 获取数据
        symbol = "^HSI"
        print(f"\n从Yahoo Finance获取 {symbol} 数据...")
        
        ticker = yf.Ticker(symbol)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=5*365)
        
        df = ticker.history(start=start_date, end=end_date)
        
        if df.empty:
            print("❌ 未能获取到数据")
            return
            
        print(f"√ 成功获取了 {len(df)} 条原始数据")
        
        # 计算指标
        df = calculate_indicators(df)
        
        # 保存到数据库
        print("\n保存数据到数据库...")
        conn = sqlite3.connect('finance.db')
        
        # 准备数据
        df.reset_index(inplace=True)
        df['Date'] = df['Date'].dt.strftime('%Y-%m-%d')
        
        # 重命名列
        columns = {
            'Date': 'date',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume',
            'ma20': 'ma20',
            'ma60': 'ma60',
            'rsi': 'rsi',
            'money_flow': 'money_flow',
            'base_x': 'base_x',
            'x_value': 'x_value',
            'base_y': 'base_y',
            'y_value': 'y_value',
            'e_value': 'e_value'
        }
        
        df = df.rename(columns=columns)
        df[columns.values()].to_sql('hkhsi50', conn, if_exists='replace', index=False)
        
        conn.commit()
        conn.close()
        
        print("✅ 数据已成功保存到数据库")
        
        # 验证数据
        print("\n数据验证：")
        conn = sqlite3.connect('finance.db')
        count = conn.execute("SELECT COUNT(*) FROM hkhsi50").fetchone()[0]
        print(f"• 数据库中的记录数：{count}")
        
        latest = conn.execute("""
            SELECT date, close, x_value, y_value, e_value 
            FROM hkhsi50 
            ORDER BY date DESC 
            LIMIT 1
        """).fetchone()
        
        print("\n最新数据：")
        print(f"• 日期：{latest[0]}")
        print(f"• 收盘价：{latest[1]:.2f}")
        print(f"• X值：{latest[2]:.3f}")
        print(f"• Y值：{latest[3]:.3f}")
        print(f"• E值：{latest[4]:.3f}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 出现错误：{str(e)}")

if __name__ == "__main__":
    print("📈 恒生50指数数据获取工具")
    print("="*50)
    fetch_data()
