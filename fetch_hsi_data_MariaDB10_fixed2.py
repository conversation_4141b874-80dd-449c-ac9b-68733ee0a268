#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取恒生50指数数据 - 简化版本
===========================

从Yahoo Finance获取恒生50指数数据并存入MariaDB数据库
只保留必要字段：Date, Open, High, Low, Close, Volume, MoneyFlowRatio, FullY, E

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import pymysql
from datetime import datetime, timedelta
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 数据库配置
db_config = {
    'host': '************',
    'user': 'root',
    'password': '',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def check_data_quality(df):
    """检查数据质量"""
    print("🔍 检查数据质量...")
    
    if df.empty:
        print("❌ 数据为空")
        return False
    
    # 检查基础数据
    required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ 缺少必要列: {missing_columns}")
        return False
    
    # 检查空值
    null_counts = df[required_columns].isnull().sum()
    total_nulls = null_counts.sum()
    
    print(f"📊 数据质量报告:")
    print(f"   • 总记录数: {len(df)}")
    print(f"   • 日期范围: {df.index.min().date()} 至 {df.index.max().date()}")
    
    for col in required_columns:
        null_count = null_counts[col]
        null_pct = null_count / len(df) * 100
        print(f"   • {col}: {null_count} 空值 ({null_pct:.1f}%)")
    
    if total_nulls == 0:
        print("✅ 数据质量良好")
        return True
    elif total_nulls < len(df) * 0.05:
        print("⚠️  数据质量一般，但可以使用")
        return True
    else:
        print("❌ 数据质量较差")
        return False

def clean_data(df):
    """清理数据"""
    print("🧹 清理数据...")
    
    original_count = len(df)
    
    # 移除价格数据为空或非正数的行
    price_columns = ['Open', 'High', 'Low', 'Close']
    for col in price_columns:
        df = df[df[col] > 0]
        df = df[df[col].notna()]
    
    # 移除成交量为负数的行
    df = df[df['Volume'] >= 0]
    
    # 修复OHLC逻辑错误
    df['High'] = df[['High', 'Open', 'Close']].max(axis=1)
    df['Low'] = df[['Low', 'Open', 'Close']].min(axis=1)
    
    cleaned_count = len(df)
    removed_count = original_count - cleaned_count
    
    print(f"✅ 数据清理完成:")
    print(f"   • 原始记录: {original_count}")
    print(f"   • 清理后记录: {cleaned_count}")
    print(f"   • 移除记录: {removed_count}")
    
    return df

def calculate_simplified_indicators(df):
    """计算简化的技术指标"""
    print("🧮 计算技术指标...")
    
    # 确保数据按日期排序
    df = df.sort_index()
    
    # 计算移动平均线
    df['ma20'] = df['Close'].rolling(window=20, min_periods=10).mean()
    df['ma60'] = df['Close'].rolling(window=60, min_periods=30).mean()
    
    # 计算RSI
    delta = df['Close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    alpha = 1/14
    avg_gain = gain.ewm(alpha=alpha, adjust=False).mean()
    avg_loss = loss.ewm(alpha=alpha, adjust=False).mean()
    
    rs = avg_gain / avg_loss
    df['rsi'] = 100 - (100 / (1 + rs))
    df['rsi'] = df['rsi'].clip(0, 100)
    
    # 计算资金流
    price_change = (df['Close'] - df['Open']) / df['Open']
    price_change = price_change.fillna(0)
    money_flow = df['Volume'] * price_change
    
    # 计算MoneyFlowRatio (X值)
    def safe_flow_ratio(flows):
        flows = flows.dropna()
        if len(flows) == 0:
            return 0.5
        
        pos_flow = flows[flows > 0].sum()
        neg_flow = abs(flows[flows < 0].sum())
        total_flow = pos_flow + neg_flow
        
        if total_flow == 0:
            return 0.5
        
        return pos_flow / total_flow
    
    base_x = money_flow.rolling(window=20, min_periods=10).apply(safe_flow_ratio, raw=False)
    rsi_adj = 0.3 * (df['rsi']/100 - 0.5)
    df['MoneyFlowRatio'] = (base_x + rsi_adj).clip(0.1, 0.9)
    
    # 计算Midprice (中位价格)
    df['Midprice'] = (df['High'] + df['Low']) / 2

    # 计算Controller (控股商标识)
    # 基于收盘价与中位价格的历史中位数比较
    historical_median = df['Midprice'].median()
    df['Controller'] = np.where(df['Close'] > historical_median, 1, 0)

    # 计算Full_Y (控制系数) - 初始值，存储过程会重新计算
    price_ma20_ratio = df['Close'] / df['ma20']
    price_ma20_ratio = price_ma20_ratio.fillna(1)

    # 基础Y值
    mask = price_ma20_ratio >= 1
    base_y = np.where(
        mask,
        0.5 + 0.4 * np.tanh((price_ma20_ratio - 1) * 3),
        0.5 - 0.4 * np.tanh((1 - price_ma20_ratio) * 3)
    )

    # 趋势调整
    ma_trend = df['ma20'] / df['ma60']
    ma_trend = ma_trend.fillna(1)
    trend_adj = 0.1 * np.tanh((ma_trend - 1) * 2)

    # 成交量调整
    volume_ma20 = df['Volume'].rolling(window=20, min_periods=10).mean()
    volume_ratio = df['Volume'] / volume_ma20
    volume_ratio = volume_ratio.fillna(1)
    vol_adj = 0.05 * np.tanh((volume_ratio - 1))

    df['Full_Y'] = (base_y + trend_adj + vol_adj).clip(0.1, 0.9)
    
    # 计算E值
    df['E'] = 8 * df['MoneyFlowRatio'] * df['Full_Y'] - 3 * df['MoneyFlowRatio'] - 3 * df['Full_Y'] + 1

    # 处理NaN值
    df['MoneyFlowRatio'] = df['MoneyFlowRatio'].fillna(0.5)
    df['Midprice'] = df['Midprice'].fillna(df['Close'])
    df['Full_Y'] = df['Full_Y'].fillna(0.5)
    df['Controller'] = df['Controller'].fillna(0)
    df['E'] = df['E'].fillna(0)
    
    # 替换任何剩余的 NaN 为 None（MySQL 兼容）
    df = df.replace({np.nan: None, np.inf: None, -np.inf: None})
    
    print(f"📊 技术指标计算完成:")
    print(f"   • MoneyFlowRatio: {df['MoneyFlowRatio'].isnull().sum()} 空值")
    print(f"   • Midprice: {df['Midprice'].isnull().sum()} 空值")
    print(f"   • Full_Y: {df['Full_Y'].isnull().sum()} 空值")
    print(f"   • Controller: {df['Controller'].isnull().sum()} 空值")
    print(f"   • E: {df['E'].isnull().sum()} 空值")
    
    return df

def create_simplified_database():
    """创建与存储过程兼容的数据库表结构"""
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()

        # 删除旧表（如果需要重新开始）
        cursor.execute('DROP TABLE IF EXISTS hkhsi50')

        # 创建与存储过程兼容的数据表
        cursor.execute('''
        CREATE TABLE hkhsi50 (
            Date DATE PRIMARY KEY,
            Open DECIMAL(15,4) NOT NULL,
            High DECIMAL(15,4) NOT NULL,
            Low DECIMAL(15,4) NOT NULL,
            Close DECIMAL(15,4) NOT NULL,
            Volume BIGINT NOT NULL DEFAULT 0,
            MoneyFlowRatio DECIMAL(15,4) COMMENT "资金流比例",
            Midprice DECIMAL(15,4) COMMENT "中位价格",
            Full_Y DECIMAL(15,4) COMMENT "控制系数",
            Controller INT COMMENT "控股商标识: 0=散户, 1=控股商",
            E DECIMAL(15,4) COMMENT "策略指标",
            i INT AUTO_INCREMENT UNIQUE COMMENT "行号",
            INDEX idx_date (Date),
            INDEX idx_close (Close),
            INDEX idx_moneyflowratio (MoneyFlowRatio),
            INDEX idx_fully (Full_Y),
            INDEX idx_controller (Controller),
            INDEX idx_e (E),
            INDEX idx_i (i)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ''')

        conn.commit()
        print("✅ 与存储过程兼容的数据库表创建完成")

        conn.close()
        return True

    except Exception as e:
        print(f"❌ 数据库操作出错: {str(e)}")
        return False

def insert_simplified_data(df):
    """插入简化的数据"""
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 准备插入SQL
        sql = """
        INSERT INTO hkhsi50 (
            Date, Open, High, Low, Close, Volume,
            MoneyFlowRatio, Midprice, Full_Y, Controller, E
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """
        
        # 转换数据格式
        df = df.reset_index()
        df['Date'] = pd.to_datetime(df['Date']).dt.date
        
        # 批量插入
        success_count = 0
        error_count = 0
        
        data_list = []
        for _, row in df.iterrows():
            try:
                data = (
                    row['Date'],
                    float(row['Open']), float(row['High']), float(row['Low']),
                    float(row['Close']), int(row['Volume']),
                    float(row['MoneyFlowRatio']) if row['MoneyFlowRatio'] is not None else None,
                    float(row['Midprice']) if row['Midprice'] is not None else None,
                    float(row['Full_Y']) if row['Full_Y'] is not None else None,
                    int(row['Controller']) if row['Controller'] is not None else None,
                    float(row['E']) if row['E'] is not None else None
                )
                data_list.append(data)
                success_count += 1
                
            except Exception as e:
                error_count += 1
                print(f"⚠️  准备第 {success_count + error_count} 条记录失败: {e}")
                continue
        
        # 批量插入
        if data_list:
            cursor.executemany(sql, data_list)
            conn.commit()
        
        print(f"📊 数据插入完成:")
        print(f"   • 成功插入: {success_count} 条")
        print(f"   • 插入失败: {error_count} 条")
        print(f"   • 成功率: {success_count/(success_count+error_count)*100:.1f}%")
        
        conn.close()
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 数据库插入出错: {str(e)}")
        return False

def fetch_hsi50_simplified():
    """获取恒生50指数数据（简化版本）"""
    print("📊 开始获取恒生50指数数据（简化版本）...")
    
    # 创建数据库和表
    if not create_simplified_database():
        return
    
    try:
        # 获取恒生指数数据
        symbol = "^HSI"
        ticker = yf.Ticker(symbol)
        
        # 获取从1990年到现在的数据
        end_date = datetime.now()
        start_date = datetime(1990, 1, 1)
        
        print(f"📅 获取日期范围: {start_date.date()} 至 {end_date.date()}")
        
        # 获取数据
        print(f"🔄 获取从1990年至今的完整数据...")
        df = ticker.history(start=start_date, end=end_date)
        
        if df.empty:
            print("❌ 未能获取到数据")
            return
        
        years_span = (end_date - start_date).days / 365.25
        print(f"✅ 成功获取 {years_span:.1f} 年数据，共 {len(df)} 条记录")
        
        # 检查数据质量
        if not check_data_quality(df):
            print("⚠️  数据质量检查未通过，尝试清理数据...")
        
        # 清理数据
        df = clean_data(df)
        
        if df.empty:
            print("❌ 数据清理后为空，无法继续")
            return
        
        # 计算简化的技术指标
        df = calculate_simplified_indicators(df)
        
        # 将数据写入数据库
        if insert_simplified_data(df):
            print(f"\n🎉 数据获取和存储成功完成！")
            print("📊 数据摘要：")
            print(f"   • 时间范围：{df.index.min().date()} 至 {df.index.max().date()}")
            print(f"   • 总记录数：{len(df)}")
            
            # 显示最新数据
            latest = df.iloc[-1]
            print(f"\n📈 最新数据 ({latest.name.date()}):")
            print(f"   • 收盘价：{latest['Close']:.2f}")
            print(f"   • MoneyFlowRatio：{latest['MoneyFlowRatio']:.3f}")
            print(f"   • Midprice：{latest['Midprice']:.2f}")
            print(f"   • Full_Y：{latest['Full_Y']:.3f}")
            print(f"   • Controller：{latest['Controller']}")
            print(f"   • E值：{latest['E']:.3f}")

            # 显示数据分布
            print(f"\n📊 数据分布:")
            print(f"   • MoneyFlowRatio范围: {df['MoneyFlowRatio'].min():.3f} - {df['MoneyFlowRatio'].max():.3f}")
            print(f"   • Full_Y范围: {df['Full_Y'].min():.3f} - {df['Full_Y'].max():.3f}")
            print(f"   • Controller分布: 散户={sum(df['Controller']==0)}, 控股商={sum(df['Controller']==1)}")
            print(f"   • E值范围: {df['E'].min():.3f} - {df['E'].max():.3f}")

            # 显示表结构
            print(f"\n📋 数据表字段:")
            print(f"   • Date (主键)")
            print(f"   • Open, High, Low, Close, Volume")
            print(f"   • MoneyFlowRatio (资金流比例)")
            print(f"   • Midprice (中位价格)")
            print(f"   • Full_Y (控制系数)")
            print(f"   • Controller (控股商标识)")
            print(f"   • E (策略指标)")
            print(f"   • i (行号索引)")

            # 提示存储过程使用
            print(f"\n🔧 存储过程集成:")
            print(f"   • 可使用存储过程重新计算Full_Y值")
            print(f"   • 调用: CALL sp_stock_analysis_with_row_coefficients_v2('hkhsi50')")
            print(f"   • 功能: 基于Controller字段计算行级控制系数")
        else:
            print("❌ 数据存储失败")
        
    except Exception as e:
        print(f"❌ 出现错误：{str(e)}")

if __name__ == "__main__":
    print("📈 恒生50指数数据获取工具 - 简化版本 (1990-至今)")
    print("="*70)
    print("📋 只保留必要字段:")
    print("   • Date, Open, High, Low, Close, Volume")
    print("   • MoneyFlowRatio (资金流比例)")
    print("   • FullY (控制系数)")
    print("   • E (策略指标)")
    print("📅 数据范围: 1990年1月1日 至 现在")
    print("="*70)
    
    fetch_hsi50_simplified()
