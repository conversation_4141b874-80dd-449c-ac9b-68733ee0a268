"""
启动MT5交易监控 - 简单版本
监控所有MT5交易活动并记录日志
"""

from mt5_trade_monitor import MT5TradeMonitor
from trading_data_manager import TradingDataManager
import time

def main():
    """主函数"""
    print("🚀 启动MT5交易监控系统")
    print("="*40)
    
    # 数据库配置（根据您的实际配置修改）
    db_config = {
        'host': 'localhost',
        'database': 'game_theory_trading',
        'user': 'your_username',
        'password': 'your_password',
        'charset': 'utf8mb4'
    }
    
    # 尝试连接数据库
    data_manager = None
    try:
        data_manager = TradingDataManager(db_config)
        print("✅ 数据库连接成功 - 交易记录将保存到数据库")
    except Exception as e:
        print(f"⚠️ 数据库连接失败: {e}")
        print("📝 交易记录将仅保存到日志文件")
    
    try:
        # 创建交易监控器
        monitor = MT5TradeMonitor(data_manager)
        
        print("\n📊 当前MT5状态:")
        monitor.print_summary()
        
        print("\n🔍 开始监控MT5交易活动...")
        print("💡 现在您可以在MT5中进行任何交易操作")
        print("📝 所有交易都会被记录到日志和数据库中")
        print("🛑 按 Ctrl+C 停止监控\n")
        
        # 开始监控（每3秒检查一次）
        monitor.start_monitoring(interval=3)
        
    except KeyboardInterrupt:
        print("\n🛑 用户停止监控")
    except Exception as e:
        print(f"\n❌ 监控失败: {e}")
    finally:
        if data_manager:
            data_manager.close()
        print("✅ 监控系统已关闭")

if __name__ == "__main__":
    main()
