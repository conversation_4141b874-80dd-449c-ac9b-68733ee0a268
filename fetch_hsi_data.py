#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取恒生指数历史数据
=================

功能：
1. 使用yfinance获取恒生指数25年历史数据
2. 计算资金流比例（X值）
3. 将数据存入SQLite数据库

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class HSIDataFetcher:
    def __init__(self):
        """初始化恒生指数数据获取器"""
        self.symbol = "^HSI"  # 恒生指数的Yahoo Finance代码
        self.db_name = "hsi_25years.db"
        self.data = None
        
    def fetch_hsi_data(self):
        """获取恒生指数数据"""
        print("📈 获取恒生指数25年历史数据...")
        
        try:
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(period="25y", interval="1d")
            
            if self.data.empty:
                print("❌ 数据获取失败：返回空数据")
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取恒生指数数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data):,} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标和资金流比例"""
        print("🔄 计算技术指标和资金流比例...")
        
        # 计算移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # 计算资金流比例（X值）
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        # 基础X值（资金流比例）
        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        
        # RSI调整
        rsi_adjustment = 0.3 * (self.data['rsi'] / 100 - 0.5)
        
        # 最终X值（资金流比例）
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        # 计算日内波动率
        self.data['volatility'] = (self.data['high'] - self.data['low']) / self.data['low'] * 100
        
        print("✅ 指标计算完成")
    
    def create_database(self):
        """创建SQLite数据库"""
        print("💾 创建数据库...")
        
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            # 创建恒生指数数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS hsi_data (
                    date TEXT PRIMARY KEY,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume REAL,
                    ma_20 REAL,
                    ma_60 REAL,
                    rsi REAL,
                    volume_ratio REAL,
                    inflow_ratio REAL,
                    volatility REAL
                )
            ''')
            
            # 创建资金流统计表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS money_flow_stats (
                    date TEXT PRIMARY KEY,
                    strong_inflow INTEGER,  -- 强资金流入天数（X > 0.7）
                    strong_outflow INTEGER, -- 强资金流出天数（X < 0.3）
                    neutral_flow INTEGER,   -- 中性资金流天数（0.3 <= X <= 0.7）
                    avg_inflow_ratio REAL   -- 平均资金流比例
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ 数据库创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 数据库创建失败: {e}")
            return False
    
    def save_to_database(self):
        """保存数据到数据库"""
        print("💾 保存数据到数据库...")
        
        try:
            conn = sqlite3.connect(self.db_name)
            
            # 保存主要数据
            data_to_save = self.data[[
                'date', 'open', 'high', 'low', 'close', 'volume',
                'ma_20', 'ma_60', 'rsi', 'volume_ratio', 'inflow_ratio', 'volatility'
            ]].copy()
            
            # 转换日期格式
            data_to_save['date'] = data_to_save['date'].dt.strftime('%Y-%m-%d')
            
            # 保存到数据库
            data_to_save.to_sql('hsi_data', conn, if_exists='replace', index=False)
            
            # 计算并保存资金流统计
            monthly_stats = []
            for date in pd.date_range(self.data['date'].min(), self.data['date'].max(), freq='M'):
                month_data = self.data[self.data['date'].dt.to_period('M') == date.to_period('M')]
                if not month_data.empty:
                    stats = {
                        'date': date.strftime('%Y-%m'),
                        'strong_inflow': len(month_data[month_data['inflow_ratio'] > 0.7]),
                        'strong_outflow': len(month_data[month_data['inflow_ratio'] < 0.3]),
                        'neutral_flow': len(month_data[(month_data['inflow_ratio'] >= 0.3) & 
                                                     (month_data['inflow_ratio'] <= 0.7)]),
                        'avg_inflow_ratio': month_data['inflow_ratio'].mean()
                    }
                    monthly_stats.append(stats)
            
            # 保存月度统计
            stats_df = pd.DataFrame(monthly_stats)
            stats_df.to_sql('money_flow_stats', conn, if_exists='replace', index=False)
            
            conn.close()
            print(f"✅ 数据保存成功：{len(self.data):,}条记录")
            
            # 打印资金流统计概要
            print("\n📊 资金流统计概要:")
            print(f"   • 强资金流入(X>0.7): {len(self.data[self.data['inflow_ratio'] > 0.7]):,}天")
            print(f"   • 强资金流出(X<0.3): {len(self.data[self.data['inflow_ratio'] < 0.3]):,}天")
            print(f"   • 中性资金流: {len(self.data[(self.data['inflow_ratio'] >= 0.3) & (self.data['inflow_ratio'] <= 0.7)]):,}天")
            print(f"   • 平均资金流比例: {self.data['inflow_ratio'].mean():.3f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据保存失败: {e}")
            return False

def main():
    """主函数"""
    print("📊 恒生指数数据获取工具")
    print("=" * 50)
    
    fetcher = HSIDataFetcher()
    
    # 获取数据
    if not fetcher.fetch_hsi_data():
        return
    
    # 计算指标
    fetcher.calculate_indicators()
    
    # 创建数据库
    if not fetcher.create_database():
        return
    
    # 保存数据
    if not fetcher.save_to_database():
        return
    
    print("\n🎉 任务完成！")
    print(f"📁 数据库文件: {fetcher.db_name}")
    print("💡 可以使用SQLite工具查看数据")

if __name__ == "__main__":
    main()
