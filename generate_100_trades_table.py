#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成100条真实资金流交易记录表
============================

从最新的真实资金流策略Excel文件中提取前100条交易记录，
生成完整的真实资金流数据表格

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import glob
import os
from datetime import datetime

def generate_100_trades_table():
    """生成100条交易记录表"""
    print("📊 生成100条真实资金流交易记录表")
    print("="*60)
    
    # 查找最新的真实资金流策略文件
    excel_files = glob.glob("HK00023真实资金流策略_*.xlsx")
    if not excel_files:
        print("❌ 未找到真实资金流策略文件")
        return
    
    # 获取最新文件
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"📄 使用文件: {latest_file}")
    
    try:
        # 读取交易记录
        df_trades = pd.read_excel(latest_file, sheet_name='真实资金流交易记录')
        
        print(f"📊 总交易记录数: {len(df_trades)}")
        
        # 取前100条记录
        df_100 = df_trades.head(100).copy()
        
        print(f"📋 提取前100条交易记录")
        
        # 选择要显示的列
        display_columns = [
            '交易序号', '开仓日期', '平仓日期', '持仓天数', '交易方向', 
            '开仓价格', '平仓价格', '均值回归中值', '价格偏离度%', '价格中值比',
            '交易股数', '交易金额', '毛利润', '交易成本', '净利润', '收益率%',
            'Y值', 'X值(真实)', 'E值', '策略区域', '交易理由', '平仓原因',
            '账户余额', '真实流入', '真实流出', '资金流强度', 'RSI'
        ]
        
        # 确保所有列都存在
        available_columns = [col for col in display_columns if col in df_100.columns]
        df_display = df_100[available_columns]
        
        # 创建详细的100条交易记录Excel文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"HK00023前100条真实资金流交易_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 写入100条交易记录
            df_display.to_excel(writer, sheet_name='前100条交易记录', index=False)
            
            # 创建汇总统计
            summary_data = {
                '统计项目': [
                    '交易记录数', '数据来源', '时间范围', '初始资金(港币)', 
                    '盈利次数', '亏损次数', '胜率(%)', '总盈亏(港币)',
                    '最大盈利(港币)', '最大亏损(港币)', '平均盈亏(港币)',
                    '做多次数', '做空次数', '高值盈利区次数', '强亏损区次数', '其他区域次数'
                ],
                '数值': [
                    100, '20年真实资金流数据库', 
                    f"{df_display['开仓日期'].min()} 至 {df_display['开仓日期'].max()}",
                    30000,
                    len(df_display[df_display['净利润'] > 0]),
                    len(df_display[df_display['净利润'] < 0]),
                    round(len(df_display[df_display['净利润'] > 0]) / 100 * 100, 1),
                    df_display['净利润'].sum(),
                    df_display['净利润'].max(),
                    df_display['净利润'].min(),
                    round(df_display['净利润'].mean(), 0),
                    len(df_display[df_display['交易方向'] == '做多']),
                    len(df_display[df_display['交易方向'] == '做空']),
                    len(df_display[df_display['策略区域'] == '高值盈利区']),
                    len(df_display[df_display['策略区域'] == '强亏损区']),
                    len(df_display[df_display['策略区域'] == '其他区域'])
                ]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='前100条汇总', index=False)
            
            # 创建真实资金流分析
            flow_analysis = []
            for _, trade in df_display.iterrows():
                flow_analysis.append({
                    '交易序号': trade['交易序号'],
                    '开仓日期': trade['开仓日期'],
                    '策略区域': trade['策略区域'],
                    '交易方向': trade['交易方向'],
                    'Y值': round(trade['Y值'], 3),
                    'X值(真实)': round(trade['X值(真实)'], 3),
                    'E值': round(trade['E值'], 3),
                    '真实流入': f"{trade['真实流入']:,.0f}",
                    '真实流出': f"{trade['真实流出']:,.0f}",
                    '资金流强度': round(trade['资金流强度'], 2),
                    '净利润': trade['净利润'],
                    '收益率%': trade['收益率%'],
                    '平仓原因': trade['平仓原因'],
                    '交易理由': trade['交易理由'][:50] + '...' if len(str(trade['交易理由'])) > 50 else trade['交易理由']
                })
            
            flow_df = pd.DataFrame(flow_analysis)
            flow_df.to_excel(writer, sheet_name='前100条资金流分析', index=False)
            
            # 设置列宽
            workbook = writer.book
            
            # 交易记录表格式
            worksheet1 = writer.sheets['前100条交易记录']
            column_widths = {
                'A': 8, 'B': 12, 'C': 12, 'D': 8, 'E': 8, 'F': 10, 'G': 10, 'H': 12, 'I': 12, 'J': 12,
                'K': 10, 'L': 12, 'M': 10, 'N': 10, 'O': 10, 'P': 10, 'Q': 8, 'R': 10, 'S': 8, 'T': 15,
                'U': 80, 'V': 12, 'W': 12, 'X': 12, 'Y': 12, 'Z': 12, 'AA': 8
            }
            for col, width in column_widths.items():
                worksheet1.column_dimensions[col].width = width
            
            # 汇总表格式
            worksheet2 = writer.sheets['前100条汇总']
            worksheet2.column_dimensions['A'].width = 20
            worksheet2.column_dimensions['B'].width = 30
            
            # 资金流分析表格式
            worksheet3 = writer.sheets['前100条资金流分析']
            worksheet3.column_dimensions['A'].width = 8
            worksheet3.column_dimensions['B'].width = 12
            worksheet3.column_dimensions['C'].width = 15
            worksheet3.column_dimensions['D'].width = 8
            worksheet3.column_dimensions['E'].width = 8
            worksheet3.column_dimensions['F'].width = 10
            worksheet3.column_dimensions['G'].width = 8
            worksheet3.column_dimensions['H'].width = 12
            worksheet3.column_dimensions['I'].width = 12
            worksheet3.column_dimensions['J'].width = 10
            worksheet3.column_dimensions['K'].width = 10
            worksheet3.column_dimensions['L'].width = 10
            worksheet3.column_dimensions['M'].width = 12
            worksheet3.column_dimensions['N'].width = 60
        
        print(f"✅ 100条交易记录Excel文件已创建: {filename}")
        
        # 显示前20条记录的摘要
        print(f"\n📋 前20条交易记录摘要:")
        print("="*120)
        
        summary_columns = ['交易序号', '开仓日期', '交易方向', '策略区域', 'X值(真实)', 
                          '真实流入', '真实流出', '净利润', '平仓原因']
        
        display_df = df_display[summary_columns].head(20).copy()
        
        # 格式化数值显示
        display_df['X值(真实)'] = display_df['X值(真实)'].apply(lambda x: f"{x:.3f}")
        display_df['真实流入'] = display_df['真实流入'].apply(lambda x: f"{x:,.0f}")
        display_df['真实流出'] = display_df['真实流出'].apply(lambda x: f"{x:,.0f}")
        
        print(display_df.to_string(index=False))
        
        # 统计分析
        print(f"\n📊 前100条交易统计分析:")
        print("="*60)
        
        total_profit = df_display['净利润'].sum()
        win_rate = len(df_display[df_display['净利润'] > 0]) / 100 * 100
        avg_profit = df_display['净利润'].mean()
        
        print(f"• 总盈亏: {total_profit:+,.0f}港币")
        print(f"• 胜率: {win_rate:.1f}%")
        print(f"• 平均盈亏: {avg_profit:+.0f}港币")
        print(f"• 最大盈利: {df_display['净利润'].max():+.0f}港币")
        print(f"• 最大亏损: {df_display['净利润'].min():+.0f}港币")
        
        # 按策略区域分析
        print(f"\n按策略区域分析:")
        for zone in df_display['策略区域'].unique():
            zone_data = df_display[df_display['策略区域'] == zone]
            zone_count = len(zone_data)
            zone_profit = zone_data['净利润'].sum()
            zone_win_rate = len(zone_data[zone_data['净利润'] > 0]) / zone_count * 100
            zone_avg_x = zone_data['X值(真实)'].mean()
            
            print(f"• {zone}: {zone_count}次, 盈亏{zone_profit:+,.0f}港币, "
                  f"胜率{zone_win_rate:.1f}%, 平均X值{zone_avg_x:.3f}")
        
        # 按真实资金流分析
        print(f"\n按真实资金流分析:")
        strong_inflow = df_display[df_display['X值(真实)'] > 0.7]
        medium_inflow = df_display[(df_display['X值(真实)'] > 0.5) & (df_display['X值(真实)'] <= 0.7)]
        medium_outflow = df_display[(df_display['X值(真实)'] >= 0.3) & (df_display['X值(真实)'] <= 0.5)]
        strong_outflow = df_display[df_display['X值(真实)'] < 0.3]
        
        for name, data in [('强流入(X>0.7)', strong_inflow), ('中等流入(0.5<X≤0.7)', medium_inflow),
                          ('中等流出(0.3≤X≤0.5)', medium_outflow), ('强流出(X<0.3)', strong_outflow)]:
            if len(data) > 0:
                count = len(data)
                profit = data['净利润'].sum()
                win_rate = len(data[data['净利润'] > 0]) / count * 100
                avg_x = data['X值(真实)'].mean()
                
                print(f"• {name}: {count}次, 盈亏{profit:+,.0f}港币, "
                      f"胜率{win_rate:.1f}%, 平均X值{avg_x:.3f}")
        
        return filename
        
    except Exception as e:
        print(f"❌ 生成100条交易记录失败: {e}")
        return None

def main():
    """主函数"""
    print("🏦 HK00023前100条真实资金流交易记录生成器")
    print("="*60)
    
    filename = generate_100_trades_table()
    
    if filename:
        print(f"\n🎉 前100条真实资金流交易记录生成完成!")
        print(f"📄 文件名: {filename}")
        print(f"📊 包含3个工作表:")
        print(f"   • 前100条交易记录: 完整的交易数据")
        print(f"   • 前100条汇总: 统计分析")
        print(f"   • 前100条资金流分析: 真实资金流详细分析")
        print(f"💡 请打开Excel文件查看完整的100条真实资金流交易记录")

if __name__ == "__main__":
    main()
