# Cosmoon的博弈论投资策略备忘录

## 📋 重要提醒
**请在每次对话开始时，让AI阅读这个文件！**

## 🎯 核心理论基础

### 哲学指导思想：
- 万物皆数，凡物、事件，都可以用一太极来看
- 从象、数、理去看待事物
- 掌握的真理：复利、均值回归、计算、凯利公式、李永乐老师的男人与美女模型

### 均值回归理论（公式2）：
```
y = m + bx
其中：
- y为均值（价值）
- m为常数
- b为斜率
- x为天数
- 价格会在价值上下震荡，但总有相交的时候

线性回归公式：
b = Σ(xi - x̄)(yi - ȳ) / Σ(xi - x̄)²
```

### 博弈论模型（李永乐模型改进）：
```
设定：
- 股民买升的概率：X，则买跌概率：1-X
- 控股商托高股价的概率：Y，则压低股价概率：1-Y

四种情况的收益：
- 股民买升，控股商托价：a = 3
- 股民买跌，控股商托价：b = -2
- 股民买升，控股商压价：c = -2
- 股民买跌，控股商压价：d = 1

期望收益公式（公式1）：
E = (a-b-c+d)xy + (b-d)x + (c-d)y + d
E = 8xy - 3x - 3y + 1
```

### Y值计算方法：
```
基于均值回归和博弈论结合：
Ydays = 6.797520 + 0.002521 × Xdays

具体计算：
- 收市价大于均值y的，认为控股商托价
- 通过两条公式的交点计算Y值
- Y值范围判断：Y < 0.333 或 Y > 0.4 时有机会赚钱
```

### X值计算方法：
```
资金流入比例，通过以下计算：
当E = 0.005043时：
- 当x=0时：y = 0.33165233
- 当x=1时：y = 0.4010086
- 当y=0.539925时：x = 0.47987338
```

### 🎯 Cosmoon博弈论策略核心：

#### 🎯 Cosmoon博弈论策略完整版：

#### 1. 高值盈利区 (Y > 0.5 且 X > 0.5)：
```
策略：买涨 (做多)
- E > 0，强盈利区域
- 使用凯利公式 1:2 赔率
- 止盈：close price × (1 + 4%)
- 止损：close price × (1 - 2%)
- 仓位：按凯利公式计算
```

#### 2. 控股商控制区 (0.333 < Y < 0.4)：
```
策略：观望
- 无论X值如何，此区间 E ≤ 0
- 不进行任何交易，等待信号
```

#### 3. 强亏损区 (Y < 0.25 或 X < 0.25)：
```
策略：买涨 (做多) - 反向思维
- E > 0 (低位反弹机会)
- 使用凯利公式 1:2 赔率
- 止盈：close price × (1 + 4%)
- 止损：close price × (1 - 2%)
- 仓位：按凯利公式计算
```

#### 4. 其他区域：
```
策略：买跌 (做空)
- 使用凯利公式 1:2 赔率
- 止盈：close price × (1 - 4%)
- 止损：close price × (1 + 2%)
- 仓位：按凯利公式计算
```

### 仓位管理：
```
基于凯利公式和博弈论期望值：
- 当E > 0时，按照凯利公式计算仓位
- 风险控制：避免在0.333 < Y < 0.4区间交易
- 复利效应：每次盈利后增加下次投资本金
```

### 复利计算：
```
真正的复利计算：
- 每次交易后，盈利加入本金
- 下次投资金额 = 当前总资金 × 仓位比例
- 长期复利效应显著
```

## 📊 关键数值和区间

### 重要阈值：
- Y值盈利区间：Y < 0.333 或 Y > 0.4
- Y值亏损区间：0.333 ≤ Y ≤ 0.4 （避免交易）
- X值关键点：X > 0.4为买入信号
- 交点计算：Edeta = 0.005043

### 具体计算示例：
```
Y1131 = 6.797520 + 0.002521 × 1131 = 9.648771
Y1133 = 6.797520 + 0.002521 × 1133 = 9.653813
Y1133 - Y1131 = Edeta = 0.005043 > 0
控股商托价概率：0.539925
```

## 📊 回测参数

### 首选设置：
- 初始资金：8000港币
- 回测期间：20年历史数据
- 数据源：数据库中的恒生指数数据
- 复利：必须启用
- Y值计算：基于均值回归 + 博弈论
- X值计算：资金流入比例

### 核心公式：
```
均值回归：y = m + bx
博弈论期望：E = 8xy - 3x - 3y + 1
Y值计算：Ydays = 6.797520 + 0.002521 × Xdays
```

## 🔧 技术细节

### 数据库配置：
- 主机：192.168.1.10
- 数据库：finance
- 表：hk00023 或 hk2800

### 特殊要求：
```
[请在这里添加任何特殊要求或注意事项]
```

## 📝 更新日志
- 2025-07-13：创建初始备忘录
- [请在每次修改时添加日期和更改内容]

---
**重要提醒：每次与AI对话时，请先让AI阅读这个文件！**
