#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正test表的profit和loss价格计算
==============================

正确理解：
- profit价格 = 止损价格 (达到亏损限度时平仓)
- loss价格 = 止盈价格 (达到盈利目标时平仓)

买跌策略：
- profit价格 = close × (1 + 止损%) (价格上涨时止损)
- loss价格 = close × (1 - 止盈%) (价格下跌时止盈)

买涨策略：
- profit价格 = close × (1 - 止损%) (价格下跌时止损)
- loss价格 = close × (1 + 止盈%) (价格上涨时止盈)

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

def correct_profit_loss_calculation():
    """修正profit和loss价格计算"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("📊 修正test表的profit和loss价格计算")
        print("="*60)
        print("🔍 正确理解:")
        print("   • profit价格 = 止损价格 (达到亏损限度时平仓)")
        print("   • loss价格 = 止盈价格 (达到盈利目标时平仓)")
        print("="*60)
        
        # 1. 修正高值盈利区买涨的profit和loss价格
        print("\n1️⃣ 修正高值盈利区买涨的profit和loss价格...")
        print("   买涨: profit价格=止损价(close×0.99), loss价格=止盈价(close×1.02)")
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 0.99,
                `loss价格` = close * 1.02
            WHERE `控制系数` > 0.43 AND `资金流比例` > 0.43 AND 交易方向 = '买涨'
        """)
        high_profit_rows = cursor.rowcount
        print(f"✅ 更新了 {high_profit_rows} 条高值盈利区买涨记录")
        
        # 2. 修正强亏损区买跌的profit和loss价格
        print("\n2️⃣ 修正强亏损区买跌的profit和loss价格...")
        print("   买跌: profit价格=止损价(close×1.01), loss价格=止盈价(close×0.98)")
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 1.01,
                `loss价格` = close * 0.98
            WHERE (`控制系数` < 0.25 OR `资金流比例` < 0.25) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4)
              AND 交易方向 = '买跌'
        """)
        strong_loss_rows = cursor.rowcount
        print(f"✅ 更新了 {strong_loss_rows} 条强亏损区买跌记录")
        
        # 3. 修正其他区域买跌的profit和loss价格
        print("\n3️⃣ 修正其他区域买跌的profit和loss价格...")
        print("   买跌: profit价格=止损价(close×1.02), loss价格=止盈价(close×0.99)")
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 1.02,
                `loss价格` = close * 0.99
            WHERE NOT (`控制系数` > 0.43 AND `资金流比例` > 0.43) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
              AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)
              AND 交易方向 = '买跌'
        """)
        other_rows = cursor.rowcount
        print(f"✅ 更新了 {other_rows} 条其他区域买跌记录")
        
        # 4. 控股商控制区观望保持不变
        print("\n4️⃣ 控股商控制区观望保持profit和loss价格为0...")
        cursor.execute("""
            SELECT COUNT(*) FROM test 
            WHERE `控制系数` > 0.333 AND `控制系数` < 0.4 AND 交易方向 = '观望'
        """)
        control_count = cursor.fetchone()[0]
        print(f"ℹ️ 控股商控制区观望记录: {control_count} 条 (profit和loss价格保持为0)")
        
        # 提交事务
        connection.commit()
        
        # 5. 验证修正结果
        print("\n5️⃣ 验证修正后的profit和loss价格...")
        cursor.execute("""
            SELECT 交易序号, 开仓日期, close, 平仓价格, 交易方向, `profit价格`, `loss价格`,
                   CASE 
                       WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                       ELSE '其他区域'
                   END AS 策略区域
            FROM test 
            ORDER BY 交易序号 
            LIMIT 10
        """)
        
        results = cursor.fetchall()
        
        print(f"\n📊 修正后的profit和loss价格验证 (前10条记录):")
        print("-" * 120)
        print(f"{'序号':<4} {'策略区域':<12} {'方向':<6} {'开仓价':<8} {'平仓价':<8} "
              f"{'profit价格':<10} {'loss价格':<9} {'说明':<20}")
        print("-" * 120)
        
        for record in results:
            trade_id, date, open_price, close_price, direction, profit_price, loss_price, zone = record
            
            if direction == '观望':
                explanation = '观望无交易'
            elif direction == '买涨':
                explanation = f'止损{float(profit_price):.2f}/止盈{float(loss_price):.2f}'
            else:  # 买跌
                explanation = f'止损{float(profit_price):.2f}/止盈{float(loss_price):.2f}'
            
            print(f"{trade_id:<4} {zone:<12} {direction:<6} {float(open_price):<8.2f} {float(close_price):<8.2f} "
                  f"{float(profit_price):<10.2f} {float(loss_price):<9.2f} {explanation:<20}")
        
        # 6. 验证第一条记录的计算
        print("\n6️⃣ 验证第一条记录的详细计算...")
        cursor.execute("""
            SELECT 交易序号, close, 平仓价格, 交易方向, `profit价格`, `loss价格`,
                   `控制系数`, `资金流比例`
            FROM test 
            WHERE 交易序号 = 1
        """)
        
        first_record = cursor.fetchone()
        trade_id, open_price, close_price, direction, profit_price, loss_price, y_val, x_val = first_record
        
        print(f"📋 交易1详细验证:")
        print(f"   • 开仓价: {float(open_price):.2f}港币")
        print(f"   • 平仓价: {float(close_price):.2f}港币")
        print(f"   • Y值: {float(y_val):.3f}, X值: {float(x_val):.3f}")
        print(f"   • 策略: 强亏损区买跌")
        print(f"   • profit价格(止损): {float(open_price):.2f} × 1.01 = {float(profit_price):.2f}港币")
        print(f"   • loss价格(止盈): {float(open_price):.2f} × 0.98 = {float(loss_price):.2f}港币")
        print(f"   • 实际平仓: {float(close_price):.2f}港币")
        
        if float(close_price) >= float(profit_price):
            print(f"   • 结果: 触发止损 (平仓价{float(close_price):.2f} >= 止损价{float(profit_price):.2f})")
        elif float(close_price) <= float(loss_price):
            print(f"   • 结果: 触发止盈 (平仓价{float(close_price):.2f} <= 止盈价{float(loss_price):.2f})")
        else:
            print(f"   • 结果: 到期平仓 (止盈价{float(loss_price):.2f} < 平仓价{float(close_price):.2f} < 止损价{float(profit_price):.2f})")
        
        # 7. 统计修正后的结果
        print("\n7️⃣ 统计修正后的profit和loss价格...")
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区'
                    WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                    WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                    ELSE '其他区域'
                END AS 策略区域,
                交易方向,
                COUNT(*) as 总次数,
                AVG(`profit价格`) as 平均profit价格,
                AVG(`loss价格`) as 平均loss价格
            FROM test 
            GROUP BY 策略区域, 交易方向
            ORDER BY 策略区域, 交易方向
        """)
        
        summary_results = cursor.fetchall()
        
        print(f"\n📈 修正后按策略区域统计:")
        print("-" * 80)
        print(f"{'策略区域':<12} {'方向':<6} {'次数':<6} {'平均profit价格':<12} {'平均loss价格':<12}")
        print("-" * 80)
        
        for record in summary_results:
            zone, direction, count, avg_profit, avg_loss = record
            print(f"{zone:<12} {direction:<6} {count:<6} {float(avg_profit):<12.2f} {float(avg_loss):<12.2f}")
        
        connection.close()
        print(f"\n🎉 profit和loss价格修正完成!")
        print(f"📊 修正记录: {high_profit_rows + strong_loss_rows + other_rows} 条")
        print("💡 现在profit价格=止损价格, loss价格=止盈价格")
        
    except Exception as e:
        print(f"❌ 修正profit和loss价格失败: {e}")

if __name__ == "__main__":
    correct_profit_loss_calculation()
