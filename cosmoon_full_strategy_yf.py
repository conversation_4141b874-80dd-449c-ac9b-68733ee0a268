#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cosmoon完整策略 - 包含充分的买跌执行
==================================

确保买跌策略得到充分执行的完整版本：
1. 高值盈利区 (Y>0.5, X>0.5): 买涨，止盈+4%，止损-2%
2. 控股商控制区 (0.333<Y<0.4): 观望
3. 强亏损区 (Y<0.25或X<0.25): 买涨，止盈+4%，止损-2%
4. 其他区域: 买跌，止盈-4%，止损+2%

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class CosmoonFullStrategy:
    def __init__(self):
        """初始化Cosmoon完整策略"""
        self.symbol = "0023.HK"
        self.data = None
        
        # 策略参数
        self.strategy_params = {
            'initial_capital': 8000,
            'kelly_win_rate': 0.6,
            'kelly_win_ratio': 2,
            'kelly_loss_ratio': 1,
            'max_position_ratio': 0.2,  # 单次最大仓位20%
            'max_total_positions': 3,   # 最多同时持有3个仓位
            
            # 止盈止损
            'long_take_profit': 0.04,   # 做多止盈4%
            'long_stop_loss': 0.02,     # 做多止损2%
            'short_take_profit': 0.04,  # 做空止盈4%
            'short_stop_loss': 0.02,    # 做空止损2%
        }
        
        self.trades = []
        self.daily_portfolio = []
        self.current_positions = []
    
    def fetch_and_prepare_data(self):
        """获取并准备数据"""
        print(f"📊 获取 {self.symbol} 数据并计算Y、X值...")
        
        # 获取数据
        ticker = yf.Ticker(self.symbol)
        self.data = ticker.history(period="20y", interval="1d")
        
        if self.data.empty:
            print("❌ 数据获取失败")
            return False
        
        self.data.reset_index(inplace=True)
        self.data.columns = [col.lower() for col in self.data.columns]
        
        # 计算技术指标
        self._calculate_technical_indicators()
        
        # 计算Y和X值
        self._calculate_y_values()
        self._calculate_x_values()
        
        # 计算E值和策略区域
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        self._classify_strategy_zones()
        
        print(f"✅ 数据准备完成: {len(self.data)} 条记录")
        return True
    
    def _calculate_technical_indicators(self):
        """计算技术指标"""
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量比率
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
    
    def _calculate_y_values(self):
        """计算Y值"""
        # 基于价格相对于移动平均线的位置
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        price_vs_ma60 = self.data['close'] / self.data['ma_60']
        
        # 基础Y值
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
        
        # 趋势调整
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        
        # 成交量调整
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
    
    def _calculate_x_values(self):
        """计算X值"""
        # 基于价格变化和成交量
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        # 滚动资金流入比例
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        
        # RSI调整
        rsi_normalized = self.data['rsi'] / 100
        rsi_adjustment = 0.3 * (rsi_normalized - 0.5)
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
    
    def _classify_strategy_zones(self):
        """分类策略区域"""
        conditions = [
            (self.data['y_probability'] > 0.5) & (self.data['inflow_ratio'] > 0.5),  # 高值盈利区
            (self.data['y_probability'] > 0.333) & (self.data['y_probability'] < 0.4),  # 控股商控制区
            (self.data['y_probability'] < 0.25) | (self.data['inflow_ratio'] < 0.25),  # 强亏损区
        ]
        
        choices = ['HIGH_PROFIT', 'CONTROL_ZONE', 'STRONG_LOSS']
        
        self.data['strategy_zone'] = np.select(conditions, choices, default='OTHER')
        
        # 统计分布
        zone_counts = self.data['strategy_zone'].value_counts()
        total = len(self.data)
        
        print(f"📊 策略区域分布:")
        for zone, count in zone_counts.items():
            print(f"   • {zone}: {count} 天 ({count/total*100:.1f}%)")
    
    def calculate_kelly_position(self, win_rate_modifier=1.0):
        """计算凯利公式仓位"""
        win_rate = self.strategy_params['kelly_win_rate'] * win_rate_modifier
        win_ratio = self.strategy_params['kelly_win_ratio']
        loss_ratio = self.strategy_params['kelly_loss_ratio']
        
        b = win_ratio / loss_ratio
        p = win_rate
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        return max(0, min(kelly_fraction, self.strategy_params['max_position_ratio']))
    
    def check_stop_conditions(self, position, current_price):
        """检查止盈止损"""
        entry_price = position['entry_price']
        direction = position['direction']
        
        if direction == 'LONG':
            profit_pct = (current_price - entry_price) / entry_price
            if profit_pct >= self.strategy_params['long_take_profit']:
                return 'TAKE_PROFIT'
            elif profit_pct <= -self.strategy_params['long_stop_loss']:
                return 'STOP_LOSS'
        else:  # SHORT
            profit_pct = (entry_price - current_price) / entry_price
            if profit_pct >= self.strategy_params['short_take_profit']:
                return 'TAKE_PROFIT'
            elif profit_pct <= -self.strategy_params['short_stop_loss']:
                return 'STOP_LOSS'
        
        return None
    
    def execute_trade(self, date, price, direction, zone, kelly_modifier=1.0):
        """执行交易"""
        kelly_fraction = self.calculate_kelly_position(kelly_modifier)
        position_value = self.current_cash * kelly_fraction
        shares = position_value / price
        
        if shares > 0 and position_value > 100:  # 最小交易金额100港币
            self.current_cash -= position_value
            
            new_position = {
                'entry_date': date,
                'entry_price': price,
                'shares': shares,
                'direction': direction,
                'zone': zone,
                'position_value': position_value
            }
            
            self.current_positions.append(new_position)
            
            print(f"{'📈' if direction == 'LONG' else '📉'} {date.strftime('%Y-%m-%d')} "
                  f"开{direction}仓: {shares:.0f}股 @ {price:.2f}, "
                  f"金额: {position_value:.0f}, 区域: {zone}")
            
            return True
        return False
    
    def close_position(self, position_index, date, price, reason):
        """平仓"""
        position = self.current_positions[position_index]
        direction = position['direction']
        entry_price = position['entry_price']
        shares = position['shares']
        
        # 计算盈亏
        if direction == 'LONG':
            profit = (price - entry_price) * shares
            exit_value = shares * price
        else:  # SHORT
            profit = (entry_price - price) * shares
            exit_value = shares * price  # 做空平仓也是按当前价格
        
        self.current_cash += exit_value
        
        # 记录交易
        trade_record = {
            'entry_date': position['entry_date'],
            'exit_date': date,
            'direction': direction,
            'entry_price': entry_price,
            'exit_price': price,
            'shares': shares,
            'profit': profit,
            'profit_pct': profit / (shares * entry_price) * 100,
            'reason': reason,
            'zone': position['zone'],
            'holding_days': (date - position['entry_date']).days
        }
        
        self.trades.append(trade_record)
        
        print(f"{'📈' if direction == 'LONG' else '📉'} {date.strftime('%Y-%m-%d')} "
              f"平{direction}仓: {shares:.0f}股 @ {price:.2f}, "
              f"盈亏: {profit:+.0f} ({profit/(shares*entry_price)*100:+.1f}%), 原因: {reason}")
        
        # 移除持仓
        del self.current_positions[position_index]
        
        return profit > 0
    
    def run_backtest(self):
        """运行完整回测"""
        print(f"\n🚀 开始Cosmoon完整策略回测...")
        print("="*70)
        print(f"💰 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"🎯 确保买跌策略充分执行")
        print("="*70)
        
        self.current_cash = self.strategy_params['initial_capital']
        winning_trades = 0
        losing_trades = 0
        
        for i, row in self.data.iterrows():
            date = row['date']
            price = row['close']
            zone = row['strategy_zone']
            y_val = row['y_probability']
            x_val = row['inflow_ratio']
            
            # 检查现有持仓的止盈止损
            positions_to_close = []
            for j, position in enumerate(self.current_positions):
                stop_reason = self.check_stop_conditions(position, price)
                if stop_reason:
                    positions_to_close.append((j, stop_reason))
            
            # 平仓处理（倒序处理避免索引问题）
            for j, reason in reversed(positions_to_close):
                is_winning = self.close_position(j, date, price, reason)
                if is_winning:
                    winning_trades += 1
                else:
                    losing_trades += 1
            
            # 开仓逻辑
            current_position_count = len(self.current_positions)
            
            if zone == 'HIGH_PROFIT' and current_position_count < self.strategy_params['max_total_positions']:
                # 高值盈利区：买涨
                self.execute_trade(date, price, 'LONG', zone, kelly_modifier=1.0)
                
            elif zone == 'STRONG_LOSS' and current_position_count < self.strategy_params['max_total_positions']:
                # 强亏损区：买涨（低位反弹）
                self.execute_trade(date, price, 'LONG', zone, kelly_modifier=0.8)
                
            elif zone == 'OTHER' and current_position_count < self.strategy_params['max_total_positions']:
                # 其他区域：买跌 - 确保充分执行
                self.execute_trade(date, price, 'SHORT', zone, kelly_modifier=0.7)
            
            # 记录每日组合价值
            position_value = sum(pos['shares'] * price for pos in self.current_positions)
            total_value = self.current_cash + position_value
            
            daily_record = {
                'date': date,
                'price': price,
                'cash': self.current_cash,
                'position_value': position_value,
                'total_value': total_value,
                'positions_count': len(self.current_positions),
                'zone': zone,
                'y_value': y_val,
                'x_value': x_val
            }
            self.daily_portfolio.append(daily_record)
        
        # 最终清仓
        final_price = self.data['close'].iloc[-1]
        final_date = self.data['date'].iloc[-1]
        
        for i in range(len(self.current_positions)):
            is_winning = self.close_position(0, final_date, final_price, 'FINAL_EXIT')
            if is_winning:
                winning_trades += 1
            else:
                losing_trades += 1
        
        # 计算最终结果
        final_value = self.current_cash
        total_return = (final_value / self.strategy_params['initial_capital'] - 1) * 100
        years = len(self.data) / 252
        annual_return = (final_value / self.strategy_params['initial_capital']) ** (1/years) - 1
        
        total_trades = len(self.trades)
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        
        # 统计各类型交易
        long_trades = len([t for t in self.trades if t['direction'] == 'LONG'])
        short_trades = len([t for t in self.trades if t['direction'] == 'SHORT'])
        
        print(f"\n✅ Cosmoon完整策略回测完成!")
        print(f"📊 交易统计:")
        print(f"   • 总交易次数: {total_trades}")
        print(f"   • 做多交易: {long_trades} 次")
        print(f"   • 做空交易: {short_trades} 次")
        print(f"   • 盈利交易: {winning_trades}")
        print(f"   • 亏损交易: {losing_trades}")
        print(f"   • 实际胜率: {win_rate:.1f}%")
        
        print(f"\n💰 收益统计:")
        print(f"   • 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"   • 最终资金: {final_value:,.0f} 港币")
        print(f"   • 总收益: {final_value - self.strategy_params['initial_capital']:+,.0f} 港币")
        print(f"   • 总收益率: {total_return:+.2f}%")
        print(f"   • 年化收益率: {annual_return*100:.2f}%")
        
        # 分析买跌效果
        if short_trades > 0:
            short_profits = [t['profit'] for t in self.trades if t['direction'] == 'SHORT']
            short_total_profit = sum(short_profits)
            short_winning = len([p for p in short_profits if p > 0])
            short_win_rate = short_winning / short_trades * 100
            
            print(f"\n📉 买跌策略分析:")
            print(f"   • 做空交易次数: {short_trades}")
            print(f"   • 做空胜率: {short_win_rate:.1f}%")
            print(f"   • 做空总盈亏: {short_total_profit:+.0f} 港币")
            print(f"   • 做空平均盈亏: {short_total_profit/short_trades:+.0f} 港币/次")

def main():
    """主函数"""
    print("🎯 Cosmoon完整策略 - 确保买跌充分执行")
    print("="*60)
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 创建策略实例
    strategy = CosmoonFullStrategy()
    
    # 获取并准备数据
    if not strategy.fetch_and_prepare_data():
        return
    
    # 运行回测
    strategy.run_backtest()
    
    print(f"\n🎉 完整策略回测完成!")
    print(f"💡 买跌策略已充分执行！")

if __name__ == "__main__":
    main()
