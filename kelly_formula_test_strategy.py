#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
严格按照凯利公式测试test表策略
=============================

凯利公式: f = (bp - q) / b
其中:
- f = 最优投注比例
- b = 赔率 (1:2，即b=2)
- p = 胜率
- q = 败率 (1-p)

策略参数：
- 赔率: 1:2 (投入1，盈利2，总回报3)
- 止盈: +2% (达到2%盈利时止盈)
- 止损: -1% (达到1%亏损时止损)
- 理论赔率: 止盈2% / 止损1% = 2:1

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime

class KellyFormulaStrategyTester:
    def __init__(self):
        """初始化凯利公式策略测试器"""
        self.db_config = {
            'host': '************',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
        self.connection = None
        self.initial_capital = 30000
        
        # 凯利公式策略参数
        self.strategy_params = {
            # 止盈止损参数
            'take_profit': 0.02,    # 2%止盈
            'stop_loss': 0.01,      # 1%止损
            
            # 赔率参数 (1:2)
            'odds_ratio': 2.0,      # 赔率b=2 (投入1，盈利2)
            
            # 策略区域参数
            'high_profit_y': 0.43,
            'high_profit_x': 0.43,
            'control_zone_min': 0.333,
            'control_zone_max': 0.4,
            'strong_loss_y': 0.25,
            'strong_loss_x': 0.25,
            
            'transaction_cost': 0.0025,
            'max_position_ratio': 0.25,  # 最大仓位25%
        }
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def load_test_data(self):
        """从test表加载数据"""
        try:
            print("📊 从test表加载100行数据...")
            
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT 交易序号, 开仓日期, 平仓日期, 持仓天数, 交易方向, 
                       close, 平仓价格, 交易股数, 交易金额, 净利润, `收益率%`,
                       `控制系数`, `资金流比例`, E值, 策略区域, 交易理由, 平仓原因,
                       `真实流入`, `真实流出`
                FROM test 
                ORDER BY 交易序号
                LIMIT 100
            """)
            
            data = cursor.fetchall()
            
            # 转换为DataFrame
            columns = ['交易序号', '开仓日期', '平仓日期', '持仓天数', '交易方向', 
                      '开仓价格', '平仓价格', '交易股数', '交易金额', '净利润', '收益率',
                      'Y值', 'X值', 'E值', '策略区域', '交易理由', '平仓原因',
                      '真实流入', '真实流出']
            
            df = pd.DataFrame(data, columns=columns)
            
            print(f"✅ 成功加载test表数据: {len(df)} 条记录")
            return df
            
        except Exception as e:
            print(f"❌ 加载test表数据失败: {e}")
            return None
    
    def calculate_kelly_formula(self, df):
        """计算凯利公式参数"""
        print("🧮 计算凯利公式参数...")
        print("📊 凯利公式: f = (bp - q) / b")
        print(f"   其中: b = {self.strategy_params['odds_ratio']} (赔率1:2)")
        
        # 分析历史胜率
        total_trades = len(df[df['净利润'] != 0])  # 排除观望
        winning_trades = len(df[df['净利润'] > 0])
        
        if total_trades > 0:
            historical_win_rate = winning_trades / total_trades
            historical_lose_rate = 1 - historical_win_rate
            
            # 计算凯利公式
            b = self.strategy_params['odds_ratio']
            p = historical_win_rate
            q = historical_lose_rate
            
            kelly_f = (b * p - q) / b
            
            print(f"📈 历史数据分析:")
            print(f"   • 总交易次数: {total_trades}")
            print(f"   • 盈利次数: {winning_trades}")
            print(f"   • 历史胜率 p: {p:.3f}")
            print(f"   • 历史败率 q: {q:.3f}")
            print(f"   • 凯利系数 f: {kelly_f:.3f}")
            
            if kelly_f > 0:
                print(f"   ✅ 凯利系数为正，建议投注比例: {kelly_f*100:.1f}%")
                optimal_position = min(kelly_f, self.strategy_params['max_position_ratio'])
                print(f"   📊 实际使用仓位: {optimal_position*100:.1f}% (限制最大25%)")
            else:
                print(f"   ❌ 凯利系数为负，不建议投注")
                optimal_position = 0
            
            return {
                'win_rate': p,
                'lose_rate': q,
                'kelly_f': kelly_f,
                'optimal_position': optimal_position
            }
        else:
            print("❌ 没有有效交易数据")
            return None
    
    def classify_strategy_zones(self, df):
        """分类策略区域"""
        print("🎯 分类策略区域...")
        
        conditions = [
            (df['Y值'] > self.strategy_params['high_profit_y']) & 
            (df['X值'] > self.strategy_params['high_profit_x']),  # 高值盈利区
            
            (df['Y值'] > self.strategy_params['control_zone_min']) & 
            (df['Y值'] < self.strategy_params['control_zone_max']),  # 控股商控制区
            
            (df['Y值'] < self.strategy_params['strong_loss_y']) | 
            (df['X值'] < self.strategy_params['strong_loss_x']),  # 强亏损区
        ]
        
        choices = ['高值盈利区', '控股商控制区', '强亏损区']
        df['凯利策略区域'] = np.select(conditions, choices, default='其他区域')
        
        return df
    
    def simulate_kelly_strategy(self, df, kelly_params):
        """模拟凯利公式策略"""
        print("\n🎯 开始凯利公式策略模拟...")
        print("💰 初始资金: 30,000港币")
        print(f"📊 止盈: +{self.strategy_params['take_profit']*100}%, 止损: -{self.strategy_params['stop_loss']*100}%")
        print(f"🎲 赔率: 1:{self.strategy_params['odds_ratio']} (投入1，盈利{self.strategy_params['odds_ratio']})")
        
        if kelly_params['optimal_position'] <= 0:
            print("❌ 凯利系数为负或零，不进行交易")
            return pd.DataFrame()
        
        results = []
        current_cash = self.initial_capital
        
        for _, row in df.iterrows():
            trade_id = row['交易序号']
            open_date = row['开仓日期']
            open_price = row['开仓价格']
            close_price = row['平仓价格']
            y_val = row['Y值']
            x_val = row['X值']
            e_val = row['E值']
            zone = row['凯利策略区域']
            real_flow_in = row['真实流入']
            real_flow_out = row['真实流出']
            
            # 确定策略
            action = None
            direction = None
            reason = None
            
            if zone == '控股商控制区':
                action = '观望'
                direction = '观望'
                reason = f'控股商控制区：Y={y_val:.3f}在0.333-0.4之间，观望'
            elif zone == '高值盈利区':
                action = '买入'
                direction = '做多'
                reason = f'高值盈利区：Y={y_val:.3f}>0.43且X={x_val:.3f}>0.43，凯利做多'
            elif zone == '强亏损区':
                action = '卖出'
                direction = '做空'
                reason = f'强亏损区：Y={y_val:.3f}<0.25或X={x_val:.3f}<0.25，凯利做空'
            elif zone == '其他区域':
                action = '卖出'
                direction = '做空'
                reason = f'其他区域：Y={y_val:.3f}，X={x_val:.3f}，凯利做空'
            
            # 计算交易结果
            if action == '观望':
                net_profit = 0
                profit_pct = 0
                exit_reason = '观望'
                actual_value = 0
                shares = 0
                kelly_position = 0
            else:
                # 使用凯利公式计算仓位
                kelly_position = kelly_params['optimal_position']
                position_value = current_cash * kelly_position
                shares = int(position_value / open_price / 100) * 100
                actual_value = shares * open_price
                
                if shares >= 100 and current_cash > actual_value:
                    # 严格按照止盈止损计算
                    if direction == '做多':
                        profit_pct = (close_price - open_price) / open_price
                        if profit_pct >= self.strategy_params['take_profit']:
                            exit_reason = '止盈'
                            profit_pct = self.strategy_params['take_profit']
                        elif profit_pct <= -self.strategy_params['stop_loss']:
                            exit_reason = '止损'
                            profit_pct = -self.strategy_params['stop_loss']
                        else:
                            exit_reason = '到期平仓'
                    else:  # 做空
                        profit_pct = (open_price - close_price) / open_price
                        if profit_pct >= self.strategy_params['take_profit']:
                            exit_reason = '止盈'
                            profit_pct = self.strategy_params['take_profit']
                        elif profit_pct <= -self.strategy_params['stop_loss']:
                            exit_reason = '止损'
                            profit_pct = -self.strategy_params['stop_loss']
                        else:
                            exit_reason = '到期平仓'
                    
                    # 计算净利润
                    gross_profit = profit_pct * actual_value
                    transaction_cost = actual_value * self.strategy_params['transaction_cost'] * 2
                    net_profit = gross_profit - transaction_cost
                    
                    # 更新现金
                    current_cash += net_profit
                else:
                    net_profit = 0
                    profit_pct = 0
                    exit_reason = '资金不足'
                    actual_value = 0
                    shares = 0
                    kelly_position = 0
            
            # 记录结果
            result = {
                '交易序号': trade_id,
                '开仓日期': open_date,
                '开仓价格': round(open_price, 2),
                '平仓价格': round(close_price, 2),
                'Y值(控制系数)': round(y_val, 3),
                'X值(资金流比例)': round(x_val, 3),
                'E值': round(e_val, 3),
                '凯利策略区域': zone,
                '凯利交易方向': direction,
                '凯利仓位比例': f"{kelly_position*100:.1f}%",
                '交易股数': shares,
                '交易金额': round(actual_value, 0),
                '收益率%': round(profit_pct * 100, 2),
                '净利润': round(net_profit, 0),
                '平仓原因': exit_reason,
                '账户余额': round(current_cash, 0),
                '真实流入': round(real_flow_in, 0),
                '真实流出': round(real_flow_out, 0),
                '凯利交易理由': reason,
                '止盈参数': f"+{self.strategy_params['take_profit']*100}%",
                '止损参数': f"-{self.strategy_params['stop_loss']*100}%",
                '赔率': f"1:{self.strategy_params['odds_ratio']}"
            }
            
            results.append(result)
        
        print(f"✅ 凯利公式策略模拟完成!")
        print(f"💰 最终资金: {current_cash:,.0f}港币")
        print(f"📈 总收益: {current_cash - self.initial_capital:+,.0f}港币")
        print(f"📊 总收益率: {(current_cash / self.initial_capital - 1) * 100:+.2f}%")
        
        return pd.DataFrame(results)
    
    def analyze_kelly_results(self, results_df, kelly_params):
        """分析凯利公式策略结果"""
        print("\n📊 凯利公式策略结果分析:")
        print("="*80)
        
        # 基础统计
        total_trades = len(results_df)
        actual_trades = len(results_df[results_df['凯利交易方向'] != '观望'])
        observe_trades = len(results_df[results_df['凯利交易方向'] == '观望'])
        winning_trades = len(results_df[results_df['净利润'] > 0])
        losing_trades = len(results_df[results_df['净利润'] < 0])
        
        total_profit = results_df['净利润'].sum()
        final_capital = results_df['账户余额'].iloc[-1] if len(results_df) > 0 else self.initial_capital
        total_return = (final_capital / self.initial_capital - 1) * 100
        
        print(f"📈 凯利公式参数:")
        print(f"   • 历史胜率: {kelly_params['win_rate']:.3f}")
        print(f"   • 凯利系数: {kelly_params['kelly_f']:.3f}")
        print(f"   • 最优仓位: {kelly_params['optimal_position']*100:.1f}%")
        print(f"   • 赔率设定: 1:{self.strategy_params['odds_ratio']}")
        print(f"   • 止盈止损: +{self.strategy_params['take_profit']*100}%/-{self.strategy_params['stop_loss']*100}%")
        
        print(f"\n📈 交易统计:")
        print(f"   • 总记录数: {total_trades}")
        print(f"   • 实际交易: {actual_trades}")
        print(f"   • 观望次数: {observe_trades}")
        print(f"   • 盈利次数: {winning_trades}")
        print(f"   • 亏损次数: {losing_trades}")
        if actual_trades > 0:
            actual_win_rate = winning_trades / actual_trades
            print(f"   • 实际胜率: {actual_win_rate:.3f}")
            print(f"   • 预期胜率: {kelly_params['win_rate']:.3f}")
            print(f"   • 胜率偏差: {actual_win_rate - kelly_params['win_rate']:+.3f}")
        print(f"   • 总盈亏: {total_profit:+,.0f}港币")
        print(f"   • 总收益率: {total_return:+.2f}%")
        
        # 止盈止损分析
        if actual_trades > 0:
            take_profit_trades = len(results_df[results_df['平仓原因'] == '止盈'])
            stop_loss_trades = len(results_df[results_df['平仓原因'] == '止损'])
            normal_close_trades = len(results_df[results_df['平仓原因'] == '到期平仓'])
            
            print(f"\n📊 止盈止损分析:")
            print(f"   • 止盈次数: {take_profit_trades} ({take_profit_trades/actual_trades*100:.1f}%)")
            print(f"   • 止损次数: {stop_loss_trades} ({stop_loss_trades/actual_trades*100:.1f}%)")
            print(f"   • 正常平仓: {normal_close_trades} ({normal_close_trades/actual_trades*100:.1f}%)")
            
            if take_profit_trades > 0:
                tp_profit = results_df[results_df['平仓原因'] == '止盈']['净利润'].sum()
                print(f"   • 止盈总盈利: {tp_profit:+,.0f}港币")
            
            if stop_loss_trades > 0:
                sl_loss = results_df[results_df['平仓原因'] == '止损']['净利润'].sum()
                print(f"   • 止损总亏损: {sl_loss:+,.0f}港币")
        
        return results_df
    
    def create_kelly_excel(self, results_df, kelly_params):
        """创建凯利公式策略Excel文件"""
        print("\n📄 创建凯利公式策略Excel文件...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"凯利公式策略测试_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 主要结果
            results_df.to_excel(writer, sheet_name='凯利公式策略结果', index=False)
            
            # 凯利公式参数
            kelly_data = {
                '参数': [
                    '凯利公式', '赔率b', '历史胜率p', '历史败率q', '凯利系数f',
                    '最优仓位', '实际使用仓位', '止盈参数', '止损参数', '交易成本'
                ],
                '数值': [
                    'f = (bp - q) / b',
                    self.strategy_params['odds_ratio'],
                    kelly_params['win_rate'],
                    kelly_params['lose_rate'],
                    kelly_params['kelly_f'],
                    f"{kelly_params['kelly_f']*100:.1f}%",
                    f"{kelly_params['optimal_position']*100:.1f}%",
                    f"+{self.strategy_params['take_profit']*100}%",
                    f"-{self.strategy_params['stop_loss']*100}%",
                    f"{self.strategy_params['transaction_cost']*100}%"
                ]
            }
            kelly_df = pd.DataFrame(kelly_data)
            kelly_df.to_excel(writer, sheet_name='凯利公式参数', index=False)
        
        print(f"✅ 凯利公式策略Excel文件已创建: {filename}")
        return filename
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 严格按照凯利公式的策略测试")
    print("="*60)
    print("📊 凯利公式: f = (bp - q) / b")
    print("🎲 赔率: 1:2 (投入1，盈利2)")
    print("📈 止盈: +2%, 止损: -1%")
    
    # 创建测试器
    tester = KellyFormulaStrategyTester()
    
    # 连接数据库
    if not tester.connect_database():
        return
    
    # 加载数据
    df = tester.load_test_data()
    if df is None:
        tester.close_connection()
        return
    
    # 计算凯利公式参数
    kelly_params = tester.calculate_kelly_formula(df)
    if kelly_params is None:
        tester.close_connection()
        return
    
    # 分类策略区域
    df = tester.classify_strategy_zones(df)
    
    # 模拟凯利策略
    results_df = tester.simulate_kelly_strategy(df, kelly_params)
    
    if len(results_df) > 0:
        # 分析结果
        tester.analyze_kelly_results(results_df, kelly_params)
        
        # 创建Excel文件
        filename = tester.create_kelly_excel(results_df, kelly_params)
    
    # 关闭连接
    tester.close_connection()
    
    print(f"\n🎉 凯利公式策略测试完成!")

if __name__ == "__main__":
    main()
