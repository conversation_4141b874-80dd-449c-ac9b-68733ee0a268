"""
MT5交易监控器 - 实时监控所有MT5交易活动
包括手动下单、EA下单、平仓等所有操作
"""

import MetaTrader5 as mt5
import time
from datetime import datetime
import json
import logging
from trading_data_manager import TradingDataManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mt5_trades.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MT5TradeMonitor:
    def __init__(self, data_manager=None):
        """
        初始化MT5交易监控器
        
        Args:
            data_manager: TradingDataManager实例（可选）
        """
        self.data_manager = data_manager
        self.last_deals = set()
        self.last_positions = {}
        self.last_orders = set()
        self.running = False
        
        # 初始化MT5
        if not mt5.initialize():
            logger.error("❌ MT5初始化失败")
            raise Exception("MT5初始化失败")
        
        logger.info("✅ MT5交易监控器初始化成功")
    
    def get_recent_deals(self, hours=24):
        """获取最近的成交记录"""
        from_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        deals = mt5.history_deals_get(from_date, datetime.now())
        if deals is None:
            return []
        
        return list(deals)
    
    def get_current_positions(self):
        """获取当前持仓"""
        positions = mt5.positions_get()
        if positions is None:
            return {}
        
        return {pos.ticket: pos for pos in positions}
    
    def get_current_orders(self):
        """获取当前挂单"""
        orders = mt5.orders_get()
        if orders is None:
            return set()
        
        return {order.ticket for order in orders}
    
    def monitor_deals(self):
        """监控成交记录"""
        current_deals = self.get_recent_deals(1)  # 最近1小时
        current_deal_tickets = {deal.ticket for deal in current_deals}
        
        # 检查新成交
        new_deals = current_deal_tickets - self.last_deals
        
        for deal in current_deals:
            if deal.ticket in new_deals:
                self.log_deal(deal)
                
                # 保存到数据库
                if self.data_manager:
                    self.save_deal_to_database(deal)
        
        self.last_deals = current_deal_tickets
    
    def monitor_positions(self):
        """监控持仓变化"""
        current_positions = self.get_current_positions()
        
        # 检查新开仓
        for ticket, position in current_positions.items():
            if ticket not in self.last_positions:
                self.log_position_open(position)
                
                # 保存到数据库
                if self.data_manager:
                    self.save_position_to_database(position, 'OPEN')
        
        # 检查平仓
        for ticket, position in self.last_positions.items():
            if ticket not in current_positions:
                self.log_position_close(position)
                
                # 更新数据库
                if self.data_manager:
                    self.save_position_to_database(position, 'CLOSE')
        
        self.last_positions = current_positions
    
    def monitor_orders(self):
        """监控挂单变化"""
        current_orders = self.get_current_orders()
        
        # 检查新挂单
        new_orders = current_orders - self.last_orders
        if new_orders:
            orders = mt5.orders_get()
            for order in orders:
                if order.ticket in new_orders:
                    self.log_order(order, 'PLACED')
        
        # 检查取消的挂单
        cancelled_orders = self.last_orders - current_orders
        if cancelled_orders:
            for ticket in cancelled_orders:
                self.log_order_cancelled(ticket)
        
        self.last_orders = current_orders
    
    def log_deal(self, deal):
        """记录成交日志"""
        deal_type = "买入" if deal.type == mt5.ORDER_TYPE_BUY else "卖出"
        
        logger.info(f"🔄 新成交: {deal.symbol} {deal_type} {deal.volume}手 "
                   f"价格:{deal.price} 盈亏:{deal.profit} 票号:{deal.ticket}")
        
        # 详细信息
        deal_info = {
            'ticket': deal.ticket,
            'symbol': deal.symbol,
            'type': deal_type,
            'volume': deal.volume,
            'price': deal.price,
            'profit': deal.profit,
            'commission': deal.commission,
            'swap': deal.swap,
            'time': datetime.fromtimestamp(deal.time),
            'magic': deal.magic,
            'comment': deal.comment
        }
        
        logger.debug(f"成交详情: {json.dumps(deal_info, default=str, ensure_ascii=False)}")
    
    def log_position_open(self, position):
        """记录开仓日志"""
        pos_type = "买入" if position.type == mt5.ORDER_TYPE_BUY else "卖出"
        
        logger.info(f"📈 新开仓: {position.symbol} {pos_type} {position.volume}手 "
                   f"价格:{position.price_open} 票号:{position.ticket}")
        
        if position.sl > 0:
            logger.info(f"   止损: {position.sl}")
        if position.tp > 0:
            logger.info(f"   止盈: {position.tp}")
    
    def log_position_close(self, position):
        """记录平仓日志"""
        pos_type = "买入" if position.type == mt5.ORDER_TYPE_BUY else "卖出"
        
        logger.info(f"📉 平仓: {position.symbol} {pos_type} {position.volume}手 "
                   f"开仓:{position.price_open} 当前:{position.price_current} "
                   f"盈亏:{position.profit} 票号:{position.ticket}")
    
    def log_order(self, order, action):
        """记录挂单日志"""
        order_type_map = {
            mt5.ORDER_TYPE_BUY_LIMIT: "买入限价",
            mt5.ORDER_TYPE_SELL_LIMIT: "卖出限价", 
            mt5.ORDER_TYPE_BUY_STOP: "买入止损",
            mt5.ORDER_TYPE_SELL_STOP: "卖出止损"
        }
        
        order_type = order_type_map.get(order.type, f"类型{order.type}")
        
        logger.info(f"📋 {action}挂单: {order.symbol} {order_type} {order.volume}手 "
                   f"价格:{order.price_open} 票号:{order.ticket}")
    
    def log_order_cancelled(self, ticket):
        """记录取消挂单日志"""
        logger.info(f"❌ 取消挂单: 票号:{ticket}")
    
    def save_deal_to_database(self, deal):
        """保存成交记录到数据库"""
        try:
            # 判断是开仓还是平仓
            if deal.entry == mt5.DEAL_ENTRY_IN:
                action = 'OPEN'
            elif deal.entry == mt5.DEAL_ENTRY_OUT:
                action = 'CLOSE'
            else:
                action = 'OTHER'
            
            trade_data = {
                'ticket': deal.ticket,
                'symbol': deal.symbol,
                'trade_type': 'BUY' if deal.type == mt5.ORDER_TYPE_BUY else 'SELL',
                'trade_action': action,
                'volume': deal.volume,
                'open_price': deal.price if action == 'OPEN' else None,
                'close_price': deal.price if action == 'CLOSE' else None,
                'profit': deal.profit,
                'commission': deal.commission,
                'swap': deal.swap,
                'open_time': datetime.fromtimestamp(deal.time) if action == 'OPEN' else None,
                'close_time': datetime.fromtimestamp(deal.time) if action == 'CLOSE' else None,
                'magic_number': deal.magic,
                'trade_comment': deal.comment or ''
            }
            
            self.data_manager.save_trade_record(trade_data)
            logger.debug(f"✅ 成交记录已保存到数据库: {deal.ticket}")
            
        except Exception as e:
            logger.error(f"❌ 保存成交记录失败: {e}")
    
    def save_position_to_database(self, position, action):
        """保存持仓记录到数据库"""
        try:
            trade_data = {
                'ticket': position.ticket,
                'symbol': position.symbol,
                'trade_type': 'BUY' if position.type == mt5.ORDER_TYPE_BUY else 'SELL',
                'trade_action': action,
                'volume': position.volume,
                'open_price': position.price_open,
                'stop_loss': position.sl if position.sl > 0 else None,
                'take_profit': position.tp if position.tp > 0 else None,
                'profit': position.profit,
                'swap': position.swap,
                'open_time': datetime.fromtimestamp(position.time),
                'magic_number': position.magic,
                'trade_comment': position.comment or ''
            }
            
            if action == 'CLOSE':
                trade_data['close_price'] = position.price_current
                trade_data['close_time'] = datetime.now()
            
            self.data_manager.save_trade_record(trade_data)
            logger.debug(f"✅ 持仓记录已保存到数据库: {position.ticket}")
            
        except Exception as e:
            logger.error(f"❌ 保存持仓记录失败: {e}")
    
    def start_monitoring(self, interval=5):
        """开始监控"""
        logger.info("🚀 开始监控MT5交易活动...")
        self.running = True
        
        # 初始化状态
        self.last_deals = {deal.ticket for deal in self.get_recent_deals(1)}
        self.last_positions = self.get_current_positions()
        self.last_orders = self.get_current_orders()
        
        try:
            while self.running:
                # 监控成交
                self.monitor_deals()
                
                # 监控持仓
                self.monitor_positions()
                
                # 监控挂单
                self.monitor_orders()
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            logger.info("🛑 监控停止")
        except Exception as e:
            logger.error(f"❌ 监控错误: {e}")
        finally:
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        mt5.shutdown()
        logger.info("✅ MT5交易监控器已停止")
    
    def get_today_summary(self):
        """获取今日交易汇总"""
        deals = self.get_recent_deals(24)
        
        total_deals = len(deals)
        total_profit = sum(deal.profit for deal in deals)
        total_commission = sum(deal.commission for deal in deals)
        
        buy_deals = [d for d in deals if d.type == mt5.ORDER_TYPE_BUY]
        sell_deals = [d for d in deals if d.type == mt5.ORDER_TYPE_SELL]
        
        summary = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'total_deals': total_deals,
            'buy_deals': len(buy_deals),
            'sell_deals': len(sell_deals),
            'total_profit': total_profit,
            'total_commission': total_commission,
            'net_profit': total_profit - total_commission
        }
        
        return summary
    
    def print_summary(self):
        """打印交易汇总"""
        summary = self.get_today_summary()
        
        print("\n" + "="*50)
        print("📊 今日MT5交易汇总")
        print("="*50)
        print(f"📅 日期: {summary['date']}")
        print(f"🔄 总成交: {summary['total_deals']}笔")
        print(f"📈 买入: {summary['buy_deals']}笔")
        print(f"📉 卖出: {summary['sell_deals']}笔")
        print(f"💰 总盈亏: {summary['total_profit']:+.2f}")
        print(f"💸 总手续费: {summary['total_commission']:+.2f}")
        print(f"💎 净盈亏: {summary['net_profit']:+.2f}")
        print("="*50)

def main():
    """主函数"""
    print("🚀 MT5交易监控器")
    print("="*30)
    
    # 数据库配置（可选）
    db_config = {
        'host': 'localhost',
        'database': 'game_theory_trading',
        'user': 'your_username',
        'password': 'your_password',
        'charset': 'utf8mb4'
    }
    
    try:
        # 创建数据管理器（可选）
        data_manager = None
        try:
            data_manager = TradingDataManager(db_config)
            print("✅ 数据库连接成功，将记录到数据库")
        except:
            print("⚠️ 数据库连接失败，仅记录到日志文件")
        
        # 创建监控器
        monitor = MT5TradeMonitor(data_manager)
        
        # 显示当前状态
        monitor.print_summary()
        
        # 开始监控
        monitor.start_monitoring(interval=5)  # 每5秒检查一次
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
