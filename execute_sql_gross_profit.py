#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行SQL语句在test表中计算毛利润
=============================

直接用SQL UPDATE语句计算毛利润

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

def execute_gross_profit_sql():
    """执行毛利润计算SQL"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("📊 执行SQL语句在test表中计算毛利润")
        print("="*60)
        
        # 1. 添加毛利润列（如果还没有的话）
        print("1️⃣ 检查并添加毛利润列...")
        try:
            cursor.execute("""
                ALTER TABLE test 
                ADD COLUMN `毛利润` DECIMAL(15,2) DEFAULT 0 
                COMMENT '毛利润(不含交易成本)'
            """)
            print("✅ 成功添加毛利润列")
        except:
            print("ℹ️ 毛利润列已存在，跳过添加")
        
        # 2. 更新毛利润：买涨策略（高值盈利区）
        print("\n2️⃣ 计算高值盈利区买涨策略毛利润...")
        cursor.execute("""
            UPDATE test 
            SET `毛利润` = (平仓价格 - close) * 交易股数
            WHERE `控制系数` > 0.43 AND `资金流比例` > 0.43
        """)
        high_profit_rows = cursor.rowcount
        print(f"✅ 更新了 {high_profit_rows} 条高值盈利区记录")
        
        # 3. 更新毛利润：买跌策略（强亏损区）
        print("\n3️⃣ 计算强亏损区买跌策略毛利润...")
        cursor.execute("""
            UPDATE test 
            SET `毛利润` = (close - 平仓价格) * 交易股数
            WHERE (`控制系数` < 0.25 OR `资金流比例` < 0.25) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4)
        """)
        strong_loss_rows = cursor.rowcount
        print(f"✅ 更新了 {strong_loss_rows} 条强亏损区记录")
        
        # 4. 更新毛利润：买跌策略（其他区域）
        print("\n4️⃣ 计算其他区域买跌策略毛利润...")
        cursor.execute("""
            UPDATE test 
            SET `毛利润` = (close - 平仓价格) * 交易股数
            WHERE NOT (`控制系数` > 0.43 AND `资金流比例` > 0.43) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
              AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)
        """)
        other_rows = cursor.rowcount
        print(f"✅ 更新了 {other_rows} 条其他区域记录")
        
        # 5. 更新毛利润：观望策略（控股商控制区）
        print("\n5️⃣ 计算控股商控制区观望策略毛利润...")
        cursor.execute("""
            UPDATE test 
            SET `毛利润` = 0
            WHERE `控制系数` > 0.333 AND `控制系数` < 0.4
        """)
        control_rows = cursor.rowcount
        print(f"✅ 更新了 {control_rows} 条控股商控制区记录")
        
        # 提交事务
        connection.commit()
        
        # 6. 验证计算结果
        print("\n6️⃣ 验证毛利润计算结果...")
        cursor.execute("""
            SELECT 交易序号, `控制系数`, `资金流比例`, close, 平仓价格, 交易股数, `毛利润`, 净利润,
                   CASE 
                       WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区-买涨'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区-观望'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区-买跌'
                       ELSE '其他区域-买跌'
                   END AS 策略区域
            FROM test 
            ORDER BY 交易序号 
            LIMIT 20
        """)
        
        results = cursor.fetchall()
        
        print(f"\n📊 毛利润计算验证 (前20条记录):")
        print("-" * 140)
        print(f"{'序号':<4} {'策略区域':<15} {'Y值':<6} {'X值':<6} {'开仓价':<8} {'平仓价':<8} {'股数':<6} {'毛利润':<8} {'净利润':<8}")
        print("-" * 140)
        
        for record in results:
            trade_id, y_val, x_val, open_price, close_price, shares, gross_profit, net_profit, strategy = record
            print(f"{trade_id:<4} {strategy:<15} {float(y_val):<6.3f} {float(x_val):<6.3f} {float(open_price):<8.2f} "
                  f"{float(close_price):<8.2f} {shares:<6} {int(gross_profit):<8} {int(net_profit):<8}")
        
        # 7. 统计汇总
        print("\n7️⃣ 毛利润统计汇总...")
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区-买涨'
                    WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区-观望'
                    WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区-买跌'
                    ELSE '其他区域-买跌'
                END AS 策略区域,
                COUNT(*) as 交易次数,
                SUM(`毛利润`) as 总毛利润,
                SUM(净利润) as 总净利润,
                AVG(`毛利润`) as 平均毛利润,
                SUM(CASE WHEN `毛利润` > 0 THEN 1 ELSE 0 END) as 毛利润盈利次数
            FROM test 
            GROUP BY 策略区域
            ORDER BY 总毛利润 DESC
        """)
        
        summary_results = cursor.fetchall()
        
        print(f"\n📈 按策略区域毛利润汇总:")
        print("-" * 100)
        print(f"{'策略区域':<15} {'次数':<6} {'总毛利润':<10} {'总净利润':<10} {'平均毛利润':<10} {'毛利润胜率':<10}")
        print("-" * 100)
        
        total_gross = 0
        total_net = 0
        
        for record in summary_results:
            strategy, count, gross_total, net_total, gross_avg, gross_wins = record
            gross_win_rate = gross_wins / count * 100 if count > 0 else 0
            
            total_gross += float(gross_total)
            total_net += float(net_total)
            
            print(f"{strategy:<15} {count:<6} {int(gross_total):<10} {int(net_total):<10} "
                  f"{int(gross_avg):<10} {gross_win_rate:<9.1f}%")
        
        print("-" * 100)
        print(f"{'总计':<15} {'':<6} {int(total_gross):<10} {int(total_net):<10} {'交易成本:':<10} {int(total_net - total_gross):<9}")
        
        connection.close()
        print(f"\n🎉 SQL毛利润计算完成!")
        print(f"📊 总更新记录: {high_profit_rows + strong_loss_rows + other_rows + control_rows} 条")
        
    except Exception as e:
        print(f"❌ 执行SQL失败: {e}")

if __name__ == "__main__":
    execute_gross_profit_sql()
