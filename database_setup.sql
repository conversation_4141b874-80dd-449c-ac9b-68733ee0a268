-- 博弈论投资策略数据库设计
-- MariaDB/MySQL

-- 创建数据库
CREATE DATABASE IF NOT EXISTS game_theory_trading 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE game_theory_trading;

-- 1. 市场数据表
CREATE TABLE market_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    datetime DATETIME NOT NULL,
    open_price DECIMAL(10,5) NOT NULL,
    high_price DECIMAL(10,5) NOT NULL,
    low_price DECIMAL(10,5) NOT NULL,
    close_price DECIMAL(10,5) NOT NULL,
    tick_volume BIGINT NOT NULL,
    spread INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_symbol_datetime (symbol, datetime),
    INDEX idx_timeframe (timeframe),
    UNIQUE KEY unique_data (symbol, timeframe, datetime)
);

-- 2. 博弈论指标表
CREATE TABLE game_theory_indicators (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    datetime DATETIME NOT NULL,
    y_probability DECIMAL(6,4) NOT NULL COMMENT 'Y值(博弈论概率)',
    x_inflow_ratio DECIMAL(6,4) NOT NULL COMMENT 'X值(资金流入比例)',
    price_trend_factor DECIMAL(6,4) COMMENT '价格趋势因子',
    volume_factor DECIMAL(6,4) COMMENT '成交量因子',
    volatility_factor DECIMAL(6,4) COMMENT '波动率因子',
    hl_factor DECIMAL(6,4) COMMENT '高低价差因子',
    position_factor DECIMAL(6,4) COMMENT '价格位置因子',
    price_volume_match DECIMAL(6,4) COMMENT '价格成交量匹配度',
    money_flow_ratio DECIMAL(6,4) COMMENT '资金流向比例',
    trade_signal VARCHAR(20) NOT NULL COMMENT '交易信号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_symbol_datetime (symbol, datetime),
    INDEX idx_trade_signal (trade_signal)
);

-- 3. 交易记录表
CREATE TABLE trades (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    ticket BIGINT NOT NULL COMMENT 'MT5订单号',
    symbol VARCHAR(20) NOT NULL,
    trade_type ENUM('BUY', 'SELL') NOT NULL,
    action ENUM('OPEN', 'CLOSE') NOT NULL,
    volume DECIMAL(8,2) NOT NULL,
    open_price DECIMAL(10,5),
    close_price DECIMAL(10,5),
    stop_loss DECIMAL(10,5),
    take_profit DECIMAL(10,5),
    profit DECIMAL(12,2) DEFAULT 0,
    commission DECIMAL(8,2) DEFAULT 0,
    swap DECIMAL(8,2) DEFAULT 0,
    open_time DATETIME,
    close_time DATETIME,
    duration_minutes INT,
    y_value_open DECIMAL(6,4) COMMENT '开仓时Y值',
    x_value_open DECIMAL(6,4) COMMENT '开仓时X值',
    y_value_close DECIMAL(6,4) COMMENT '平仓时Y值',
    x_value_close DECIMAL(6,4) COMMENT '平仓时X值',
    magic_number INT NOT NULL,
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ticket (ticket),
    INDEX idx_symbol (symbol),
    INDEX idx_open_time (open_time),
    INDEX idx_magic (magic_number)
);

-- 4. 账户状态表
CREATE TABLE account_status (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    datetime DATETIME NOT NULL,
    balance DECIMAL(12,2) NOT NULL,
    equity DECIMAL(12,2) NOT NULL,
    margin DECIMAL(12,2) NOT NULL,
    free_margin DECIMAL(12,2) NOT NULL,
    margin_level DECIMAL(8,2),
    profit DECIMAL(12,2) NOT NULL,
    positions_count INT DEFAULT 0,
    orders_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_datetime (datetime)
);

-- 5. 策略性能表
CREATE TABLE strategy_performance (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    total_trades INT DEFAULT 0,
    winning_trades INT DEFAULT 0,
    losing_trades INT DEFAULT 0,
    win_rate DECIMAL(5,2) DEFAULT 0,
    total_profit DECIMAL(12,2) DEFAULT 0,
    max_profit DECIMAL(12,2) DEFAULT 0,
    max_loss DECIMAL(12,2) DEFAULT 0,
    avg_profit DECIMAL(12,2) DEFAULT 0,
    avg_loss DECIMAL(12,2) DEFAULT 0,
    profit_factor DECIMAL(8,4) DEFAULT 0,
    max_drawdown DECIMAL(12,2) DEFAULT 0,
    sharpe_ratio DECIMAL(8,4) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (date, symbol),
    INDEX idx_date (date)
);

-- 6. 风险监控表
CREATE TABLE risk_monitoring (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    datetime DATETIME NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    risk_level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') NOT NULL,
    risk_type VARCHAR(50) NOT NULL COMMENT '风险类型',
    description TEXT COMMENT '风险描述',
    current_value DECIMAL(12,4) COMMENT '当前值',
    threshold_value DECIMAL(12,4) COMMENT '阈值',
    action_taken VARCHAR(100) COMMENT '采取的行动',
    resolved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_datetime (datetime),
    INDEX idx_risk_level (risk_level),
    INDEX idx_resolved (resolved)
);

-- 7. 系统日志表
CREATE TABLE system_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    datetime DATETIME NOT NULL,
    level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL,
    component VARCHAR(50) NOT NULL COMMENT '组件名称',
    message TEXT NOT NULL,
    details JSON COMMENT '详细信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_datetime (datetime),
    INDEX idx_level (level),
    INDEX idx_component (component)
);

-- 8. 配置参数表
CREATE TABLE strategy_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    parameter_name VARCHAR(100) NOT NULL,
    parameter_value VARCHAR(500) NOT NULL,
    parameter_type ENUM('STRING', 'INTEGER', 'DECIMAL', 'BOOLEAN') NOT NULL,
    description TEXT,
    category VARCHAR(50) DEFAULT 'GENERAL',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_param (parameter_name)
);

-- 插入默认配置
INSERT INTO strategy_config (parameter_name, parameter_value, parameter_type, description, category) VALUES
('Y_BUY_THRESHOLD', '0.55', 'DECIMAL', 'Y值买入阈值', 'STRATEGY'),
('X_BUY_THRESHOLD', '0.5', 'DECIMAL', 'X值买入阈值', 'STRATEGY'),
('Y_SELL_THRESHOLD', '0.45', 'DECIMAL', 'Y值卖出阈值', 'STRATEGY'),
('X_SELL_THRESHOLD', '0.4', 'DECIMAL', 'X值卖出阈值', 'STRATEGY'),
('Y_STRONG_SELL_THRESHOLD', '0.332', 'DECIMAL', 'Y值强卖出阈值', 'STRATEGY'),
('X_STRONG_SELL_THRESHOLD', '0.332', 'DECIMAL', 'X值强卖出阈值', 'STRATEGY'),
('LOT_SIZE', '0.01', 'DECIMAL', '交易手数', 'RISK'),
('STOP_LOSS', '50', 'INTEGER', '止损点数', 'RISK'),
('TAKE_PROFIT', '100', 'INTEGER', '止盈点数', 'RISK'),
('MAX_POSITIONS', '1', 'INTEGER', '最大持仓数', 'RISK'),
('MAX_DAILY_LOSS', '1000', 'DECIMAL', '每日最大亏损', 'RISK'),
('MAX_DRAWDOWN', '2000', 'DECIMAL', '最大回撤', 'RISK'),
('TRADING_ENABLED', 'true', 'BOOLEAN', '是否启用交易', 'SYSTEM'),
('DATA_COLLECTION_ENABLED', 'true', 'BOOLEAN', '是否启用数据收集', 'SYSTEM'),
('ALERT_EMAIL', '<EMAIL>', 'STRING', '警报邮箱', 'SYSTEM');

-- 创建视图：交易统计
CREATE VIEW v_trading_summary AS
SELECT 
    DATE(open_time) as trade_date,
    symbol,
    COUNT(*) as total_trades,
    SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) as winning_trades,
    SUM(CASE WHEN profit < 0 THEN 1 ELSE 0 END) as losing_trades,
    ROUND(SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as win_rate,
    ROUND(SUM(profit), 2) as total_profit,
    ROUND(MAX(profit), 2) as max_profit,
    ROUND(MIN(profit), 2) as max_loss,
    ROUND(AVG(CASE WHEN profit > 0 THEN profit END), 2) as avg_win,
    ROUND(AVG(CASE WHEN profit < 0 THEN profit END), 2) as avg_loss
FROM trades 
WHERE action = 'CLOSE' AND close_time IS NOT NULL
GROUP BY DATE(open_time), symbol;

-- 创建视图：当前持仓
CREATE VIEW v_current_positions AS
SELECT 
    t1.ticket,
    t1.symbol,
    t1.trade_type,
    t1.volume,
    t1.open_price,
    t1.stop_loss,
    t1.take_profit,
    t1.open_time,
    t1.y_value_open,
    t1.x_value_open,
    t1.comment
FROM trades t1
LEFT JOIN trades t2 ON t1.ticket = t2.ticket AND t2.action = 'CLOSE'
WHERE t1.action = 'OPEN' AND t2.ticket IS NULL;

-- 创建存储过程：计算策略性能
DELIMITER //
CREATE PROCEDURE CalculateStrategyPerformance(IN target_date DATE, IN target_symbol VARCHAR(20))
BEGIN
    DECLARE total_trades INT DEFAULT 0;
    DECLARE winning_trades INT DEFAULT 0;
    DECLARE losing_trades INT DEFAULT 0;
    DECLARE total_profit DECIMAL(12,2) DEFAULT 0;
    DECLARE max_profit DECIMAL(12,2) DEFAULT 0;
    DECLARE max_loss DECIMAL(12,2) DEFAULT 0;
    DECLARE avg_profit DECIMAL(12,2) DEFAULT 0;
    DECLARE avg_loss DECIMAL(12,2) DEFAULT 0;
    DECLARE profit_factor DECIMAL(8,4) DEFAULT 0;
    DECLARE win_rate DECIMAL(5,2) DEFAULT 0;
    
    -- 计算统计数据
    SELECT 
        COUNT(*),
        SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END),
        SUM(CASE WHEN profit < 0 THEN 1 ELSE 0 END),
        SUM(profit),
        MAX(profit),
        MIN(profit),
        AVG(CASE WHEN profit > 0 THEN profit END),
        AVG(CASE WHEN profit < 0 THEN profit END)
    INTO total_trades, winning_trades, losing_trades, total_profit, max_profit, max_loss, avg_profit, avg_loss
    FROM trades 
    WHERE DATE(close_time) = target_date 
    AND symbol = target_symbol 
    AND action = 'CLOSE';
    
    -- 计算胜率和盈亏比
    IF total_trades > 0 THEN
        SET win_rate = winning_trades * 100.0 / total_trades;
    END IF;
    
    IF avg_loss < 0 THEN
        SET profit_factor = ABS(avg_profit / avg_loss);
    END IF;
    
    -- 插入或更新性能数据
    INSERT INTO strategy_performance 
    (date, symbol, total_trades, winning_trades, losing_trades, win_rate, 
     total_profit, max_profit, max_loss, avg_profit, avg_loss, profit_factor)
    VALUES 
    (target_date, target_symbol, total_trades, winning_trades, losing_trades, win_rate,
     total_profit, max_profit, max_loss, avg_profit, avg_loss, profit_factor)
    ON DUPLICATE KEY UPDATE
    total_trades = VALUES(total_trades),
    winning_trades = VALUES(winning_trades),
    losing_trades = VALUES(losing_trades),
    win_rate = VALUES(win_rate),
    total_profit = VALUES(total_profit),
    max_profit = VALUES(max_profit),
    max_loss = VALUES(max_loss),
    avg_profit = VALUES(avg_profit),
    avg_loss = VALUES(avg_loss),
    profit_factor = VALUES(profit_factor),
    updated_at = CURRENT_TIMESTAMP;
    
END //
DELIMITER ;
