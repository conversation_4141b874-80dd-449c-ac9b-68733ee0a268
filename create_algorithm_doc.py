#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略算法说明文档生成器
====================

生成一个详细的Excel文档，说明：
1. X、Y、E值的计算方法
2. 各个区域的定义和交易规则
3. 实际计算示例

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import numpy as np
from datetime import datetime
import openpyxl
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter

def create_algorithm_excel():
    """创建算法说明Excel文件"""


    
    print("📝 创建算法说明文档...")
    
    # 创建Excel写入器
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"策略算法说明_{timestamp}.xlsx"
    writer = pd.ExcelWriter(filename, engine='openpyxl')
    
    # === 1. Y值计算说明 ===
    y_calc_data = {
        '计算步骤': [
            '1. 基础Y值计算',
            '2. 趋势调整',
            '3. 成交量调整',
            '4. 最终Y值'
        ],
        '计算公式': [
            'if price/MA20 >= 1:\n    base_y = 0.5 + 0.4 * tanh((price/MA20 - 1) * 3)\nelse:\n    base_y = 0.5 - 0.4 * tanh((1 - price/MA20) * 3)',
            'trend_adj = 0.1 * tanh((MA20/MA60 - 1) * 2)',
            'vol_adj = 0.05 * tanh((volume/volume_MA20 - 1))',
            'Y = clip(base_y + trend_adj + vol_adj, 0.1, 0.9)'
        ],
        '参数说明': [
            '• 使用价格相对MA20的位置\n• tanh函数使变化更平滑\n• 基础值范围：0.1-0.9',
            '• MA20与MA60的关系反映趋势\n• 最大贡献±0.1',
            '• 成交量相对20日均量\n• 最大贡献±0.05',
            '• 确保最终Y值在0.1-0.9之间'
        ],
        '指标意义': [
            '反映价格强度，高于均线为强势',
            '反映趋势强度，上升趋势加分',
            '反映成交量确认程度',
            'Y>0.45表示强势，Y<0.25表示弱势'
        ]
    }
    
    # === 2. X值计算说明 ===
    x_calc_data = {
        '计算步骤': [
            '1. 资金流计算',
            '2. 基础X值计算',
            '3. RSI调整',
            '4. 最终X值'
        ],
        '计算公式': [
            'money_flow = volume * (close - open) / open',
            'base_x = inflows / (inflows + outflows)\n[20日滚动计算]',
            'rsi_adj = 0.3 * (RSI/100 - 0.5)',
            'X = clip(base_x + rsi_adj, 0.1, 0.9)'
        ],
        '参数说明': [
            '• 计算每日资金流向\n• 正值表示流入，负值表示流出',
            '• 计算20日资金流入占比\n• 基础值范围：0-1',
            '• RSI贡献范围：±0.15\n• RSI>50加分，<50减分',
            '• 确保最终X值在0.1-0.9之间'
        ],
        '指标意义': [
            '反映当日资金流向强度',
            '反映中期资金流向趋势',
            '反映超买超卖程度',
            'X>0.45表示资金流入，X<0.25表示流出'
        ]
    }
    
    # === 3. E值计算说明 ===
    e_calc_data = {
        '计算步骤': [
            '1. E值计算公式',
            '2. 正E值区域',
            '3. 负E值区域',
            '4. E≈0区域'
        ],
        '计算公式': [
            'E = 8xy - 3x - 3y + 1',
            'E > 0: 通常在x,y都大于0.45时',
            'E < 0: 通常在x,y都小于0.3时',
            '|E| < 0.1: 在0.333<y<0.4区域'
        ],
        '参数说明': [
            '• x: 资金流比例（0.1-0.9）\n• y: 价格强度（0.1-0.9）',
            '• 高值盈利区\n• 适合做多策略',
            '• 强亏损区或其他区域\n• 适合做空策略',
            '• 控股商控制区\n• 建议观望不交易'
        ],
        '策略应用': [
            '根据E值判断交易方向和时机',
            '止盈1.6%，止损0.8%',
            '止盈0.8-1%，止损1.6-2%',
            '避免在此区域交易'
        ]
    }
    
    # === 4. 交易规则说明 ===
    trading_rules_data = {
        '交易区域': [
            '高值盈利区',
            '强亏损区',
            '其他区域',
            '控股商控制区'
        ],
        '条件': [
            'Y>0.4 且 X>0.4',
            'Y<0.25 或 X<0.25',
            '不满足以上条件',
            '0.333<Y<0.4'
        ],
        '交易方向': [
            '做多',
            '做空',
            '做空',
            '观望'
        ],
        '止盈': [
            '1.6%',
            '0.8%',
            '1.0%',
            '-'
        ],
        '止损': [
            '0.8%',
            '1.6%',
            '2.0%',
            '-'
        ],
        '理由说明': [
            '趋势和资金流都强，适合做多',
            '价格或资金流极弱，适合做空',
            '不确定区域，偏向做空',
            '控盘区域，避免交易'
        ]
    }
    
    # === 5. 实际计算示例 ===
    example_data = {
        '示例情况': [
            '强势上涨',
            '弱势下跌',
            '盘整震荡',
            '控盘阶段'
        ],
        '市场特征': [
            '价格高于MA20 20%\nMA20上穿MA60\n成交量放大2倍',
            '价格低于MA20 15%\nMA20下穿MA60\n成交量萎缩50%',
            '价格贴近MA20\nMA20与MA60平行\n成交量正常',
            '价格在MA20附近\n股东增持\n成交量低'
        ],
        'Y值计算': [
            'base_y = 0.7\ntrend_adj = 0.08\nvol_adj = 0.04\nY = 0.82',
            'base_y = 0.3\ntrend_adj = -0.06\nvol_adj = -0.02\nY = 0.22',
            'base_y = 0.5\ntrend_adj = 0\nvol_adj = 0\nY = 0.5',
            'base_y = 0.48\ntrend_adj = 0.02\nvol_adj = -0.03\nY = 0.37'
        ],
        'X值计算': [
            'base_x = 0.75\nrsi_adj = 0.12\nX = 0.87',
            'base_x = 0.25\nrsi_adj = -0.12\nX = 0.13',
            'base_x = 0.55\nrsi_adj = 0\nX = 0.55',
            'base_x = 0.45\nrsi_adj = -0.05\nX = 0.4'
        ],
        'E值': [
            'E = 2.45 (显著正值)',
            'E = -1.85 (显著负值)',
            'E = 0.75 (小正值)',
            'E = 0.05 (接近零)'
        ],
        '交易决策': [
            '做多，止盈1.6%，止损0.8%',
            '做空，止盈0.8%，止损1.6%',
            '做空，止盈1%，止损2%',
            '观望，不开仓'
        ]
    }
    
    # 写入各个工作表
    pd.DataFrame(y_calc_data).to_excel(writer, sheet_name='Y值计算说明', index=False)
    pd.DataFrame(x_calc_data).to_excel(writer, sheet_name='X值计算说明', index=False)
    pd.DataFrame(e_calc_data).to_excel(writer, sheet_name='E值计算说明', index=False)
    pd.DataFrame(trading_rules_data).to_excel(writer, sheet_name='交易规则', index=False)
    pd.DataFrame(example_data).to_excel(writer, sheet_name='计算示例', index=False)
    
    # 获取工作簿对象
    workbook = writer.book
    
    # 设置格式
    for sheet_name in writer.sheets:
        worksheet = writer.sheets[sheet_name]
        
        # 设置列宽
        for i, col in enumerate(worksheet.columns):
            max_length = 0
            column = get_column_letter(i + 1)
            
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column].width = adjusted_width
        
        # 设置行高
        for row in worksheet.rows:
            worksheet.row_dimensions[row[0].row].height = 60
        
        # 设置标题行格式
        for cell in worksheet[1]:
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.font = Font(color="FFFFFF", bold=True)
            cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
        
        # 设置其他单元格格式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in worksheet.iter_rows(min_row=2):
            for cell in row:
                cell.border = thin_border
                cell.alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)
    
    # 保存文件
    writer.close()
    
    print(f"✅ 算法说明文档已创建: {filename}")
    return filename

def main():
    """主函数"""
    print("📚 策略算法说明文档生成器")
    print("="*50)
    
    filename = create_algorithm_excel()
    
    if filename:
        print("\n📋 文档包含以下内容：")
        print("1. Y值计算方法和原理")
        print("2. X值计算方法和原理")
        print("3. E值计算和意义")
        print("4. 交易规则说明")
        print("5. 实际计算示例")
        print(f"\n💡 请打开 {filename} 查看详细信息")

if __name__ == "__main__":
    main()
