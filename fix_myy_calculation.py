#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正test表MyY计算
================

修正MyY计算逻辑：
- 第一行累积Y = 1 (而不是Y值)
- 第二行开始累积Y = 前一行累积Y + 当前Y值
- MyY = 累积Y / 交易序号

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd

class TestTableMyYFixer:
    def __init__(self):
        """初始化MyY修正器"""
        self.db_config = {
            'host': '************',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
        self.connection = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def recalculate_myy_values(self):
        """重新计算MyY值（修正版）"""
        try:
            print("🧮 重新计算MyY值（修正版）...")
            print("📊 修正逻辑: 第一行累积Y=1，后续累积Y=前一行累积Y+当前Y值")
            
            cursor = self.connection.cursor()
            
            # 获取所有记录，按交易序号排序
            cursor.execute("""
                SELECT 交易序号, Y值
                FROM test 
                ORDER BY 交易序号
            """)
            
            records = cursor.fetchall()
            
            if not records:
                print("❌ test表中没有数据")
                return False
            
            print(f"📊 处理 {len(records)} 条记录...")
            
            # 修正计算累积Y值和MyY
            cumulative_y = 1  # 第一行累积Y从1开始
            updates = []
            
            for i, (trade_id, y_value) in enumerate(records):
                if i == 0:
                    # 第一行：累积Y = 1
                    cumulative_y = 1
                else:
                    # 后续行：累积Y = 前一行累积Y + 当前Y值
                    cumulative_y += float(y_value)
                
                # 计算MyY = 累积Y / 交易序号
                myy = cumulative_y / trade_id
                
                updates.append((myy, trade_id))
                
                # 显示前15条计算过程
                if trade_id <= 15:
                    if i == 0:
                        print(f"   交易{trade_id}: Y值={y_value:.3f}, 累积Y={cumulative_y:.3f} (初始值), MyY={myy:.6f}")
                    else:
                        print(f"   交易{trade_id}: Y值={y_value:.3f}, 累积Y={cumulative_y:.3f}, MyY={myy:.6f}")
            
            # 批量更新MyY值
            update_sql = "UPDATE test SET MyY = %s WHERE 交易序号 = %s"
            cursor.executemany(update_sql, updates)
            self.connection.commit()
            
            print(f"✅ 成功重新计算并更新 {len(updates)} 条记录的MyY值")
            return True
            
        except Exception as e:
            print(f"❌ 重新计算MyY值失败: {e}")
            return False
    
    def verify_fixed_myy_calculation(self):
        """验证修正后的MyY计算结果"""
        try:
            cursor = self.connection.cursor()
            
            # 获取前20条记录验证
            cursor.execute("""
                SELECT 交易序号, Y值, MyY
                FROM test 
                ORDER BY 交易序号 
                LIMIT 20
            """)
            
            verification_data = cursor.fetchall()
            
            print("\n📊 修正后MyY计算验证 (前20条记录):")
            print("="*70)
            print(f"{'序号':<4} {'Y值':<8} {'累积Y':<10} {'MyY':<10} {'手工验证':<10} {'差异':<8}")
            print("-" * 70)
            
            cumulative_y = 1  # 第一行累积Y从1开始
            for i, (trade_id, y_value, myy) in enumerate(verification_data):
                if i == 0:
                    cumulative_y = 1  # 第一行
                else:
                    cumulative_y += float(y_value)
                
                manual_myy = cumulative_y / trade_id
                difference = abs(float(myy) - manual_myy)
                
                print(f"{trade_id:<4} {float(y_value):<8.3f} {cumulative_y:<10.3f} {float(myy):<10.6f} {manual_myy:<10.6f} {difference:<8.6f}")
            
            # 统计信息
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    MIN(MyY) as min_myy,
                    MAX(MyY) as max_myy,
                    AVG(MyY) as avg_myy,
                    AVG(Y值) as avg_y
                FROM test
            """)
            
            stats = cursor.fetchone()
            total, min_myy, max_myy, avg_myy, avg_y = stats
            
            print(f"\n📈 修正后MyY统计信息:")
            print(f"   • 总记录数: {total}")
            print(f"   • MyY最小值: {float(min_myy):.6f}")
            print(f"   • MyY最大值: {float(max_myy):.6f}")
            print(f"   • MyY平均值: {float(avg_myy):.6f}")
            print(f"   • Y值平均值: {float(avg_y):.6f}")
            
            # 显示关键节点的MyY值
            cursor.execute("""
                SELECT 交易序号, Y值, MyY
                FROM test 
                WHERE 交易序号 IN (1, 5, 10, 20, 50, 100)
                ORDER BY 交易序号
            """)
            
            key_records = cursor.fetchall()
            
            print(f"\n📋 关键节点的MyY值:")
            print("-" * 40)
            for trade_id, y_value, myy in key_records:
                print(f"交易{trade_id}: Y值={float(y_value):.3f}, MyY={float(myy):.6f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证修正后MyY计算失败: {e}")
            return False
    
    def analyze_fixed_myy_trends(self):
        """分析修正后的MyY趋势"""
        try:
            cursor = self.connection.cursor()
            
            # 按策略区域分析修正后的MyY
            cursor.execute("""
                SELECT 策略区域,
                       COUNT(*) as count,
                       AVG(Y值) as avg_y,
                       AVG(MyY) as avg_myy,
                       MIN(MyY) as min_myy,
                       MAX(MyY) as max_myy
                FROM test 
                GROUP BY 策略区域
                ORDER BY avg_myy DESC
            """)
            
            zone_stats = cursor.fetchall()
            
            print(f"\n📊 按策略区域的修正后MyY分析:")
            print("-" * 80)
            print(f"{'策略区域':<15} {'次数':<6} {'平均Y值':<10} {'平均MyY':<10} {'最小MyY':<10} {'最大MyY':<10}")
            print("-" * 80)
            
            for zone, count, avg_y, avg_myy, min_myy, max_myy in zone_stats:
                print(f"{zone:<15} {count:<6} {float(avg_y):<10.3f} {float(avg_myy):<10.6f} "
                      f"{float(min_myy):<10.6f} {float(max_myy):<10.6f}")
            
            # 分析修正后MyY与盈亏的关系
            cursor.execute("""
                SELECT 
                    CASE 
                        WHEN MyY >= 1.0 THEN 'MyY高(>=1.0)'
                        WHEN MyY >= 0.8 THEN 'MyY中高(0.8-1.0)'
                        WHEN MyY >= 0.6 THEN 'MyY中(0.6-0.8)'
                        ELSE 'MyY低(<0.6)'
                    END as myy_category,
                    COUNT(*) as count,
                    AVG(净利润) as avg_profit,
                    SUM(净利润) as total_profit,
                    AVG(MyY) as avg_myy
                FROM test 
                GROUP BY myy_category
                ORDER BY avg_myy DESC
            """)
            
            myy_profit_stats = cursor.fetchall()
            
            print(f"\n📈 修正后MyY与盈亏关系分析:")
            print("-" * 70)
            print(f"{'MyY区间':<15} {'次数':<6} {'平均盈亏':<10} {'总盈亏':<10} {'平均MyY':<10}")
            print("-" * 70)
            
            for category, count, avg_profit, total_profit, avg_myy in myy_profit_stats:
                print(f"{category:<15} {count:<6} {int(avg_profit):<10} {int(total_profit):<10} {float(avg_myy):<10.6f}")
            
            # 分析MyY趋势变化
            cursor.execute("""
                SELECT 交易序号, MyY
                FROM test 
                WHERE 交易序号 % 10 = 0 OR 交易序号 <= 10
                ORDER BY 交易序号
            """)
            
            trend_data = cursor.fetchall()
            
            print(f"\n📈 MyY趋势变化:")
            print("-" * 30)
            for trade_id, myy in trend_data:
                print(f"交易{trade_id}: MyY={float(myy):.6f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析修正后MyY趋势失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 修正test表MyY计算")
    print("="*50)
    print("📊 修正逻辑:")
    print("   • 第一行累积Y = 1")
    print("   • 后续行累积Y = 前一行累积Y + 当前Y值")
    print("   • MyY = 累积Y / 交易序号")
    
    # 创建修正器
    fixer = TestTableMyYFixer()
    
    # 连接数据库
    if not fixer.connect_database():
        return
    
    # 重新计算MyY值
    if not fixer.recalculate_myy_values():
        fixer.close_connection()
        return
    
    # 验证修正后的计算结果
    fixer.verify_fixed_myy_calculation()
    
    # 分析修正后的MyY趋势
    fixer.analyze_fixed_myy_trends()
    
    # 关闭连接
    fixer.close_connection()
    
    print("\n🎉 test表MyY计算修正完成!")
    print("📊 修正后公式: MyY = 累积Y / 交易序号")
    print("💡 第一行累积Y从1开始，符合Excel公式逻辑")

if __name__ == "__main__":
    main()
