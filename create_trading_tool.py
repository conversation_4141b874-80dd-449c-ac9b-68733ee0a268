#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略计算工具Excel生成器
=====================

创建一个便于使用的Excel工具，包含：
1. 数据输入区
2. 自动计算区
3. 交易建议区
4. 实时监控表

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import numpy as np
from datetime import datetime
import openpyxl
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.datavalidation import DataValidation

def create_trading_tool_excel():
    """创建交易工具Excel"""
    print("📝 创建交易计算工具...")
    
    # 创建Excel文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"策略计算工具_{timestamp}.xlsx"
    writer = pd.ExcelWriter(filename, engine='openpyxl')
    
    # === 1. 数据输入表 ===
    input_data = pd.DataFrame({
        '参数': [
            '股票代码',
            '当前价格',
            '20日均线',
            '60日均线',
            '今日成交量',
            '20日均量',
            '14日RSI',
            '开盘价',
            '收盘价'
        ],
        '数值': ['', 0, 0, 0, 0, 0, 0, 0, 0],
        '说明': [
            '输入股票代码（如：0023.HK）',
            '输入当前最新价格',
            '输入20日移动平均线值',
            '输入60日移动平均线值',
            '输入今日成交量',
            '输入20日平均成交量',
            '输入当前RSI值(0-100)',
            '输入今日开盘价',
            '输入今日收盘价'
        ]
    })
    
    # === 2. 计算结果表 ===
    calc_data = pd.DataFrame({
        '指标': [
            'Y值计算',
            '├─ 基础Y值',
            '├─ 趋势调整',
            '├─ 成交量调整',
            '└─ 最终Y值',
            '',
            'X值计算',
            '├─ 价格变动',
            '├─ 基础X值',
            '├─ RSI调整',
            '└─ 最终X值',
            '',
            'E值计算',
            '└─ 最终E值'
        ],
        '计算结果': [''] * 14,
        '参考范围': [
            '0.1 - 0.9',
            '0.1 - 0.9',
            '-0.1 - +0.1',
            '-0.05 - +0.05',
            '0.1 - 0.9',
            '',
            '0.1 - 0.9',
            '-1 - +1',
            '0 - 1',
            '-0.15 - +0.15',
            '0.1 - 0.9',
            '',
            '-2 - +2',
            '-2 - +2'
        ],
        '状态评估': [''] * 14
    })
    
    # === 3. 交易建议表 ===
    advice_data = pd.DataFrame({
        '分析项目': [
            '策略区域',
            '建议操作',
            '止盈位',
            '止损位',
            '风险等级',
            '建议仓位',
            '关键提示'
        ],
        '结果': [''] * 7,
        '说明': [
            '根据X、Y值判断所处区域',
            '建议的交易方向和操作',
            '建议的止盈价格位置',
            '建议的止损价格位置',
            '当前交易风险评估',
            '建议的仓位比例',
            '需要特别注意的事项'
        ]
    })
    
    # === 4. 实时监控表 ===
    monitor_template = pd.DataFrame({
        '时间': [],
        '价格': [],
        'Y值': [],
        'X值': [],
        'E值': [],
        '区域': [],
        '信号': [],
        '建议': []
    })
    
    # 写入各个工作表
    input_data.to_excel(writer, sheet_name='数据输入', index=False)
    calc_data.to_excel(writer, sheet_name='计算结果', index=False)
    advice_data.to_excel(writer, sheet_name='交易建议', index=False)
    monitor_template.to_excel(writer, sheet_name='实时监控', index=False)
    
    # 获取工作簿对象
    workbook = writer.book
    
    # 设置数据输入表格式
    worksheet1 = writer.sheets['数据输入']
    
    # 添加数据验证
    rsi_validation = DataValidation(
        type="decimal",
        operator="between",
        formula1="0",
        formula2="100",
        allow_blank=True
    )
    worksheet1.add_data_validation(rsi_validation)
    rsi_validation.add('B7')  # RSI单元格
    
    # 设置公式
    worksheet1['B10'] = '=IF(AND(B2>0,B3>0,B4>0,B5>0,B6>0,B7>0,B8>0,B9>0),"✅ 数据完整","❌ 数据不完整")'
    
    # 添加计算结果公式
    worksheet2 = writer.sheets['计算结果']
    
    # Y值计算公式
    base_y = '=IF(数据输入!B2/数据输入!B3>=1,0.5+0.4*TANH((数据输入!B2/数据输入!B3-1)*3),0.5-0.4*TANH((1-数据输入!B2/数据输入!B3)*3))'
    trend_adj = '=0.1*TANH((数据输入!B3/数据输入!B4-1)*2)'
    vol_adj = '=0.05*TANH((数据输入!B5/数据输入!B6-1))'
    final_y = f'=MAX(MIN({base_y}+{trend_adj}+{vol_adj},0.9),0.1)'
    
    worksheet2['B2'] = base_y
    worksheet2['B3'] = trend_adj
    worksheet2['B4'] = vol_adj
    worksheet2['B5'] = final_y
    
    # X值计算公式
    price_change = '=(数据输入!B9-数据输入!B8)/数据输入!B8'
    base_x = '=IF(B8>0,0.5+0.4*TANH(B8*5),0.5-0.4*TANH(-B8*5))'
    rsi_adj = '=0.3*(数据输入!B7/100-0.5)'
    final_x = f'=MAX(MIN({base_x}+{rsi_adj},0.9),0.1)'
    
    worksheet2['B8'] = price_change
    worksheet2['B9'] = base_x
    worksheet2['B10'] = rsi_adj
    worksheet2['B11'] = final_x
    
    # E值计算公式
    e_value = '=8*B11*B5-3*B11-3*B5+1'
    worksheet2['B14'] = e_value
    
    # 状态评估公式
    y_status = '=IF(B5>0.45,"强势",IF(B5<0.25,"弱势","中性"))'
    x_status = '=IF(B11>0.45,"资金流入",IF(B11<0.25,"资金流出","中性"))'
    e_status = '=IF(B14>0.5,"做多信号",IF(B14<-0.5,"做空信号","观望"))'
    
    worksheet2['D5'] = y_status
    worksheet2['D11'] = x_status
    worksheet2['D14'] = e_status
    
    # 交易建议公式
    worksheet3 = writer.sheets['交易建议']
    
    zone_formula = """=IF(AND(计算结果!B5>0.4,计算结果!B11>0.4),"高值盈利区",
    IF(AND(计算结果!B5>0.333,计算结果!B5<0.4),"控股商控制区",
    IF(OR(计算结果!B5<0.25,计算结果!B11<0.25),"强亏损区","其他区域")))"""
    
    action_formula = """=IF(B1="高值盈利区","买入做多",
    IF(B1="控股商控制区","观望",
    IF(B1="强亏损区","卖出做空",
    IF(B1="其他区域","卖出做空",""))))"""
    
    tp_formula = """=IF(B2="买入做多",数据输入!B2*1.016,
    IF(B2="卖出做空",数据输入!B2*0.984,""))"""
    
    sl_formula = """=IF(B2="买入做多",数据输入!B2*0.992,
    IF(B2="卖出做空",数据输入!B2*1.016,""))"""
    
    risk_formula = """=IF(B1="控股商控制区","高风险",
    IF(计算结果!B14>1,"低风险",
    IF(计算结果!B14<-1,"低风险","中等风险")))"""
    
    position_formula = """=IF(B5="低风险","12%",
    IF(B5="中等风险","8%",
    IF(B5="高风险","0%","")))"""
    
    worksheet3['B1'] = zone_formula
    worksheet3['B2'] = action_formula
    worksheet3['B3'] = tp_formula
    worksheet3['B4'] = sl_formula
    worksheet3['B5'] = risk_formula
    worksheet3['B6'] = position_formula
    
    # 设置格式
    for sheet_name in writer.sheets:
        worksheet = writer.sheets[sheet_name]
        
        # 设置列宽
        for i, col in enumerate(worksheet.columns):
            max_length = 0
            column = get_column_letter(i + 1)
            
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column].width = adjusted_width
        
        # 设置标题行格式
        for cell in worksheet[1]:
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.font = Font(color="FFFFFF", bold=True)
            cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
        
        # 设置单元格格式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in worksheet.iter_rows(min_row=2):
            for cell in row:
                cell.border = thin_border
                cell.alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)
    
    # 保存文件
    writer.close()
    
    print(f"✅ 交易计算工具已创建: {filename}")
    return filename

def main():
    """主函数"""
    print("🔧 策略计算工具生成器")
    print("="*50)
    
    filename = create_trading_tool_excel()
    
    if filename:
        print("\n📋 工具包含以下功能：")
        print("1. 数据输入表 - 输入基础数据")
        print("2. 计算结果表 - 自动计算X、Y、E值")
        print("3. 交易建议表 - 提供交易建议")
        print("4. 实时监控表 - 记录交易过程")
        print(f"\n💡 请打开 {filename} 使用计算工具")
        print("\n📌 使用说明：")
        print('1. 在"数据输入"表中输入当前市场数据')
        print('2. 系统会自动计算X、Y、E值')
        print('3. 查看"交易建议"获取操作建议')
        print('4. 使用"实时监控"记录交易过程')

if __name__ == "__main__":
    main()
