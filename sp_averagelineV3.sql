DELIMITER //

DROP PROCEDURE IF EXISTS sp_averagelineV3 //

CREATE PROCEDURE sp_averagelineV3(IN tablename VARCHAR(64))
BEGIN
    DECLARE Xavg DECIMAL(20, 6) DEFAULT 0;
    DECLARE Yavg DECIMAL(20, 6) DEFAULT 0;
    DECLAR<PERSON> sum_xy DECIMAL(20, 6) DEFAULT 0;
    DECLARE sum_xx DECIMAL(20, 6) DEFAULT 0;
    DECLARE b DECIMAL(20, 6) DEFAULT 0;
    DECLARE m DECIMAL(20, 6) DEFAULT 0;
    DECLARE total_rows INT DEFAULT 0;
    DECLARE col_exists INT DEFAULT 0;
    DECLARE stmt_sql TEXT;

    -- 检查 i 列是否存在（hkhsi50表已有此列）
    SET @col_exists = 0;
    SET stmt_sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''i'''
    );
    PREPARE stmt FROM stmt_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    -- 如果i列不存在则添加
    IF col_exists = 0 THEN
        SET stmt_sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `i` INT AUTO_INCREMENT UNIQUE');
        PREPARE stmt FROM stmt_sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;

    -- 获取总行数
    SET @total_rows = 0;
    SET stmt_sql = CONCAT('SELECT COUNT(*) INTO @total_rows FROM `', tablename, '`');
    PREPARE stmt FROM stmt_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SET total_rows = @total_rows;

    -- 计算 Xavg (行号的平均值)
    SET Xavg = (total_rows + 1) / 2;

    -- 计算 Yavg (收盘价的平均值) - 使用正确的字段名Close
    SET @Yavg = 0;
    SET stmt_sql = CONCAT('SELECT AVG(Close) INTO @Yavg FROM `', tablename, '`');
    PREPARE stmt FROM stmt_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SET Yavg = @Yavg;

    -- 计算 sum_xy (使用ROW_NUMBER确保正确的行号)
    SET @sum_xy = 0;
    SET stmt_sql = CONCAT(
        'SELECT SUM((row_num - ', Xavg, ') * (Close - ', Yavg, ')) INTO @sum_xy ',
        'FROM (SELECT ROW_NUMBER() OVER (ORDER BY Date) as row_num, Close FROM `', tablename, '`) t'
    );
    PREPARE stmt FROM stmt_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SET sum_xy = @sum_xy;

    -- 计算 sum_xx (使用ROW_NUMBER确保正确的行号)
    SET @sum_xx = 0;
    SET stmt_sql = CONCAT(
        'SELECT SUM(POWER(row_num - ', Xavg, ', 2)) INTO @sum_xx ',
        'FROM (SELECT ROW_NUMBER() OVER (ORDER BY Date) as row_num FROM `', tablename, '`) t'
    );
    PREPARE stmt FROM stmt_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SET sum_xx = @sum_xx;

    -- 计算回归系数
    IF sum_xx > 0 THEN
        SET b = sum_xy / sum_xx;
        SET m = Yavg - b * Xavg;
    ELSE
        SET b = 0;
        SET m = Yavg;
    END IF;

    -- 检查Midprice列是否存在（hkhsi50表已有此列）
    SET @col_exists = 0;
    SET stmt_sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''Midprice'''
    );
    PREPARE stmt FROM stmt_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    -- 如果Midprice列不存在则添加
    IF col_exists = 0 THEN
        SET stmt_sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `Midprice` DECIMAL(20, 6)');
        PREPARE stmt FROM stmt_sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;

    -- 更新Midprice字段为回归线值
    SET stmt_sql = CONCAT(
        'UPDATE `', tablename, '` t1 ',
        'JOIN (SELECT Date, ROW_NUMBER() OVER (ORDER BY Date) as row_num FROM `', tablename, '`) t2 ',
        'ON t1.Date = t2.Date ',
        'SET t1.Midprice = ', m, ' + ', b, ' * t2.row_num'
    );
    PREPARE stmt FROM stmt_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    -- 返回回归参数和统计信息
    SELECT
        m AS Intercept,
        b AS Slope,
        sum_xx AS SUM_xx,
        sum_xy AS SUM_xy,
        Xavg AS Xavg,
        Yavg AS Yavg,
        total_rows AS TOTAL_rows,
        CONCAT('y = ', ROUND(m, 4), ' + ', ROUND(b, 4), ' * x') AS 回归方程;

    -- 显示最新几条记录的回归线值
    SET stmt_sql = CONCAT(
        'SELECT Date, Close, Midprice as 回归线值, ',
        'ROUND(Close - Midprice, 2) as 偏差 ',
        'FROM `', tablename, '` ',
        'ORDER BY Date DESC LIMIT 10'
    );
    PREPARE stmt FROM stmt_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

END //

DELIMITER ;
