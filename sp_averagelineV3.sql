DELIMITER //

CREATE PROCEDURE `sp_averagelineV3`(IN tablename VARCHAR(64))
BEGIN
  DECLARE Xavg DECIMAL(20, 6) DEFAULT 0;
  DECLARE Yavg DECIMAL(20, 6) DEFAULT 0;
  DECLARE sum_xy DECIMAL(20, 6) DEFAULT 0;
  DECLARE sum_xx DECIMAL(20, 6) DEFAULT 0;
  DECLARE b DECIMAL(20, 6) DEFAULT 0;
  DECLARE m DECIMAL(20, 6) DEFAULT 0;
  DECLARE total_rows INT DEFAULT 0;
  DECLARE col_exists INT DEFAULT 0;
  DECLARE stmt_sql TEXT;

  -- 检查 i 列是否存在
  SELECT COUNT(*) INTO col_exists 
  FROM information_schema.COLUMNS 
  WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = tablename 
    AND COLUMN_NAME = 'i';

  IF col_exists = 0 THEN
    SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `i` INT');
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SET @sql = CONCAT(
      'SET @row_num = 0; ',
      'UPDATE `', tablename, '` SET i = (@row_num := @row_num + 1) ',
      'ORDER BY date'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
  END IF;

  -- 获取总行数和平均值
  SET @sql = CONCAT(
    'SELECT COUNT(*) INTO @total_rows, AVG(close) INTO @Yavg ',
    'FROM `', tablename, '`'
  );
  PREPARE stmt FROM @sql;
  EXECUTE stmt;
  DEALLOCATE PREPARE stmt;
  
  SET total_rows = @total_rows;
  SET Xavg = total_rows / 2;
  SET Yavg = @Yavg;

  -- 计算回归参数
  SET @sql = CONCAT(
    'SELECT ',
    'SUM((i - ?) * (close - ?)) INTO @sum_xy, ',
    'SUM(POWER(i - ?, 2)) INTO @sum_xx ',
    'FROM `', tablename, '`'
  );
  PREPARE stmt FROM @sql;
  SET @x = Xavg;
  SET @y = Yavg;
  EXECUTE stmt USING @x, @y, @x;
  DEALLOCATE PREPARE stmt;
  
  SET sum_xy = @sum_xy;
  SET sum_xx = @sum_xx;

  -- 计算斜率和截距
  SET b = sum_xy / sum_xx;
  SET m = Yavg - b * Xavg;

  -- 检查 midprice 列是否存在
  SELECT COUNT(*) INTO col_exists 
  FROM information_schema.COLUMNS 
  WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = tablename 
    AND COLUMN_NAME = 'midprice';

  IF col_exists = 0 THEN
    SET @sql = CONCAT(
      'ALTER TABLE `', tablename, '` ',
      'ADD COLUMN `midprice` DECIMAL(20, 6)'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
  END IF;

  -- 更新中值
  SET @sql = CONCAT(
    'UPDATE `', tablename, '` ',
    'SET midprice = ? + ? * i'
  );
  PREPARE stmt FROM @sql;
  EXECUTE stmt USING m, b;
  DEALLOCATE PREPARE stmt;

  -- 返回回归参数
  SELECT 
    m AS Intercept,
    b AS Slope,
    sum_xx AS SUM_xx,
    sum_xy AS SUM_xy,
    Xavg AS Xavg,
    Yavg AS Yavg,
    total_rows AS TOTAL_rows;
    
END //

DELIMITER ;
