#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建HK00023 20年数据库并添加资金流列
===================================

功能：
1. 删除旧数据库，重新创建
2. 从yfinance获取20年历史数据
3. 计算并添加资金流数据
4. 保存到数据库

作者: Cosmoon NG
日期: 2025年7月
"""

import sqlite3
import pandas as pd
import yfinance as yf
import numpy as np
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class HK00023_20YearDatabaseCreator:
    def __init__(self, db_path="hk00023_20year.db"):
        """初始化20年数据库创建器"""
        self.db_path = db_path
        self.conn = None
        
    def remove_old_database(self):
        """删除旧数据库文件"""
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
            print(f"🗑️ 已删除旧数据库文件: {self.db_path}")
        
        # 也删除旧的5年数据库
        old_db = "hk00023.db"
        if os.path.exists(old_db):
            print(f"📋 发现旧的5年数据库: {old_db}")
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            print(f"✅ 成功连接新数据库: {self.db_path}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def create_hk00023_table(self):
        """创建HK00023表"""
        try:
            cursor = self.conn.cursor()
            
            # 创建表，包含基础数据和资金流列
            create_table_sql = """
                CREATE TABLE IF NOT EXISTS hk00023 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL UNIQUE,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume INTEGER,
                    dividends REAL DEFAULT 0,
                    stock_splits REAL DEFAULT 0,
                    money_flow_in REAL DEFAULT 0,
                    money_flow_out REAL DEFAULT 0,
                    net_money_flow REAL DEFAULT 0,
                    money_flow_ratio REAL DEFAULT 0.5,
                    money_flow_intensity REAL DEFAULT 1.0,
                    cumulative_money_flow REAL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
            
            cursor.execute(create_table_sql)
            self.conn.commit()
            print("✅ 成功创建HK00023表")
            return True
            
        except Exception as e:
            print(f"❌ 创建表失败: {e}")
            return False
    
    def fetch_hk00023_20year_data(self):
        """从yfinance获取HK00023 20年数据"""
        try:
            print("🏦 从yfinance获取HK00023东亚银行20年历史数据...")
            print("⏳ 正在下载大量数据，请耐心等待...")
            
            ticker = yf.Ticker("0023.HK")
            data = ticker.history(period="20y", interval="1d")  # 获取20年数据
            
            if data.empty:
                print("❌ 未获取到数据")
                return None
            
            # 重置索引，将日期作为列
            data.reset_index(inplace=True)
            data.columns = [col.lower() for col in data.columns]
            
            print(f"✅ 成功获取HK00023 20年数据:")
            print(f"   • 数据期间: {data['date'].min().strftime('%Y-%m-%d')} 至 {data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(data)} 天")
            print(f"   • 数据跨度: {(data['date'].max() - data['date'].min()).days} 天")
            
            return data
            
        except Exception as e:
            print(f"❌ 获取20年数据失败: {e}")
            return None
    
    def calculate_enhanced_money_flow(self, data):
        """计算增强的资金流数据"""
        print("🧮 计算20年增强资金流指标...")
        
        # 方法1: 基于价量关系的资金流
        price_change = (data['close'] - data['open']) / data['open']
        price_change = price_change.fillna(0)
        
        # 上涨时为流入，下跌时为流出
        flow_in_1 = np.where(price_change > 0, data['volume'] * price_change, 0)
        flow_out_1 = np.where(price_change < 0, data['volume'] * abs(price_change), 0)
        
        # 方法2: 基于典型价格的资金流 (Money Flow Index方法)
        typical_price = (data['high'] + data['low'] + data['close']) / 3
        raw_money_flow = typical_price * data['volume']
        
        # 比较典型价格确定流向
        price_up = typical_price > typical_price.shift(1)
        price_up = price_up.fillna(False)
        
        flow_in_2 = np.where(price_up, raw_money_flow, 0)
        flow_out_2 = np.where(~price_up, raw_money_flow, 0)
        
        # 方法3: 基于收盘价位置的资金流 (A/D Line方法)
        high_low_diff = data['high'] - data['low']
        close_location_value = np.where(
            high_low_diff > 0,
            ((data['close'] - data['low']) - (data['high'] - data['close'])) / high_low_diff,
            0
        )
        
        money_flow_multiplier = close_location_value * data['volume']
        flow_in_3 = np.where(money_flow_multiplier > 0, money_flow_multiplier, 0)
        flow_out_3 = np.where(money_flow_multiplier < 0, abs(money_flow_multiplier), 0)
        
        # 方法4: 基于RSI调整的资金流
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14, min_periods=1).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14, min_periods=1).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        rsi = rsi.fillna(50)
        
        # RSI调整因子
        rsi_factor = (rsi - 50) / 50  # -1到1之间
        flow_in_4 = np.where(rsi_factor > 0, data['volume'] * rsi_factor, 0)
        flow_out_4 = np.where(rsi_factor < 0, data['volume'] * abs(rsi_factor), 0)
        
        # 综合计算 (加权平均，增加RSI权重)
        flow_in = (flow_in_1 * 0.3 + flow_in_2 * 0.3 + flow_in_3 * 0.2 + flow_in_4 * 0.2)
        flow_out = (flow_out_1 * 0.3 + flow_out_2 * 0.3 + flow_out_3 * 0.2 + flow_out_4 * 0.2)
        
        # 计算其他指标
        net_flow = flow_in - flow_out
        total_flow = flow_in + flow_out
        flow_ratio = np.where(total_flow > 0, flow_in / total_flow, 0.5)
        
        # 计算资金流强度 (相对于多个周期的平均成交量)
        volume_ma5 = data['volume'].rolling(window=5, min_periods=1).mean()
        volume_ma20 = data['volume'].rolling(window=20, min_periods=1).mean()
        volume_ma60 = data['volume'].rolling(window=60, min_periods=1).mean()
        
        # 综合成交量基准
        volume_baseline = (volume_ma5 * 0.5 + volume_ma20 * 0.3 + volume_ma60 * 0.2)
        flow_intensity = total_flow / volume_baseline
        flow_intensity = flow_intensity.fillna(1.0)
        
        # 计算累积资金流
        cumulative_flow = net_flow.cumsum()
        
        # 添加到数据中
        data['money_flow_in'] = flow_in
        data['money_flow_out'] = flow_out
        data['net_money_flow'] = net_flow
        data['money_flow_ratio'] = flow_ratio
        data['money_flow_intensity'] = flow_intensity
        data['cumulative_money_flow'] = cumulative_flow
        
        # 处理无穷大和NaN值
        money_flow_columns = ['money_flow_in', 'money_flow_out', 'net_money_flow', 
                             'money_flow_ratio', 'money_flow_intensity', 'cumulative_money_flow']
        
        for col in money_flow_columns:
            data[col] = data[col].replace([np.inf, -np.inf], 0)
            data[col] = data[col].fillna(0)
        
        print("✅ 20年增强资金流计算完成")
        
        # 显示资金流统计
        print(f"\n📊 20年资金流数据统计:")
        print(f"   • 平均日流入: {data['money_flow_in'].mean():,.0f}")
        print(f"   • 平均日流出: {data['money_flow_out'].mean():,.0f}")
        print(f"   • 平均流入比例: {data['money_flow_ratio'].mean():.3f}")
        print(f"   • 累积资金流范围: {data['cumulative_money_flow'].min():,.0f} 至 {data['cumulative_money_flow'].max():,.0f}")
        
        return data
    
    def insert_data_to_database(self, data):
        """将20年数据插入数据库"""
        try:
            print("💾 将20年数据插入数据库...")
            print("⏳ 正在处理大量数据，请稍候...")
            
            # 准备插入数据
            insert_data = []
            for _, row in data.iterrows():
                insert_data.append((
                    row['date'].strftime('%Y-%m-%d'),
                    float(row['open']),
                    float(row['high']),
                    float(row['low']),
                    float(row['close']),
                    int(row['volume']),
                    float(row.get('dividends', 0)),
                    float(row.get('stock_splits', 0)),
                    float(row['money_flow_in']),
                    float(row['money_flow_out']),
                    float(row['net_money_flow']),
                    float(row['money_flow_ratio']),
                    float(row['money_flow_intensity']),
                    float(row['cumulative_money_flow'])
                ))
            
            # 批量插入数据
            insert_sql = """
                INSERT OR REPLACE INTO hk00023 
                (date, open, high, low, close, volume, dividends, stock_splits,
                 money_flow_in, money_flow_out, net_money_flow, money_flow_ratio,
                 money_flow_intensity, cumulative_money_flow)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            cursor = self.conn.cursor()
            cursor.executemany(insert_sql, insert_data)
            self.conn.commit()
            
            print(f"✅ 成功插入 {len(insert_data)} 条20年记录")
            return True
            
        except Exception as e:
            print(f"❌ 插入20年数据失败: {e}")
            return False
    
    def verify_20year_database(self):
        """验证20年数据库数据"""
        try:
            # 查询数据统计
            cursor = self.conn.cursor()
            
            # 总记录数
            cursor.execute("SELECT COUNT(*) FROM hk00023")
            total_count = cursor.fetchone()[0]
            
            # 日期范围
            cursor.execute("SELECT MIN(date), MAX(date) FROM hk00023")
            date_range = cursor.fetchone()
            
            # 计算实际年数
            start_date = datetime.strptime(date_range[0], '%Y-%m-%d')
            end_date = datetime.strptime(date_range[1], '%Y-%m-%d')
            years = (end_date - start_date).days / 365.25
            
            # 资金流数据统计
            cursor.execute("""
                SELECT 
                    AVG(money_flow_in) as avg_flow_in,
                    AVG(money_flow_out) as avg_flow_out,
                    AVG(money_flow_ratio) as avg_ratio,
                    MAX(cumulative_money_flow) as max_cumulative,
                    MIN(cumulative_money_flow) as min_cumulative,
                    AVG(money_flow_intensity) as avg_intensity
                FROM hk00023
            """)
            flow_stats = cursor.fetchone()
            
            print("\n📊 20年数据库验证结果:")
            print("="*70)
            print(f"• 总记录数: {total_count:,}")
            print(f"• 日期范围: {date_range[0]} 至 {date_range[1]}")
            print(f"• 实际年数: {years:.1f} 年")
            print(f"• 平均每年交易日: {total_count/years:.0f} 天")
            print(f"• 平均资金流入: {flow_stats[0]:,.0f}")
            print(f"• 平均资金流出: {flow_stats[1]:,.0f}")
            print(f"• 平均流入比例: {flow_stats[2]:.3f}")
            print(f"• 平均资金流强度: {flow_stats[5]:.2f}")
            print(f"• 累积资金流范围: {flow_stats[4]:,.0f} 至 {flow_stats[3]:,.0f}")
            
            # 显示最新几条记录
            df_sample = pd.read_sql_query("""
                SELECT date, close, volume, money_flow_in, money_flow_out, 
                       money_flow_ratio, money_flow_intensity, cumulative_money_flow
                FROM hk00023 
                ORDER BY date DESC 
                LIMIT 5
            """, self.conn)
            
            print("\n📈 最新5条记录:")
            print("="*120)
            print(df_sample.to_string(index=False))
            
            # 显示最早几条记录
            df_earliest = pd.read_sql_query("""
                SELECT date, close, volume, money_flow_in, money_flow_out, 
                       money_flow_ratio, money_flow_intensity, cumulative_money_flow
                FROM hk00023 
                ORDER BY date ASC 
                LIMIT 5
            """, self.conn)
            
            print("\n📈 最早5条记录:")
            print("="*120)
            print(df_earliest.to_string(index=False))
            
            return True
            
        except Exception as e:
            print(f"❌ 验证20年数据库失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 创建HK00023 20年数据库并添加资金流数据")
    print("="*70)
    print("⏳ 注意：20年数据量较大，处理时间较长，请耐心等待")
    
    # 创建20年数据库创建器
    creator = HK00023_20YearDatabaseCreator()
    
    # 删除旧数据库
    creator.remove_old_database()
    
    # 连接数据库
    if not creator.connect_database():
        return
    
    # 创建HK00023表
    if not creator.create_hk00023_table():
        creator.close_connection()
        return
    
    # 获取20年数据
    data = creator.fetch_hk00023_20year_data()
    if data is None:
        creator.close_connection()
        return
    
    # 计算增强资金流
    data = creator.calculate_enhanced_money_flow(data)
    
    # 插入数据库
    if not creator.insert_data_to_database(data):
        creator.close_connection()
        return
    
    # 验证数据库
    creator.verify_20year_database()
    
    # 关闭连接
    creator.close_connection()
    
    print("\n🎉 HK00023 20年数据库创建完成!")
    print("📊 包含以下增强资金流列:")
    print("   • money_flow_in: 资金流入")
    print("   • money_flow_out: 资金流出")
    print("   • net_money_flow: 净资金流")
    print("   • money_flow_ratio: 资金流入比例")
    print("   • money_flow_intensity: 资金流强度")
    print("   • cumulative_money_flow: 累积资金流")
    print(f"📄 数据库文件: hk00023_20year.db")

if __name__ == "__main__":
    main()
