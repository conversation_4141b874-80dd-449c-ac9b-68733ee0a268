#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示包含均值回归中值的Excel结果
==============================

显示HK00023优化策略的回测结果，包含均值回归分析

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import glob
import os

def show_mean_reversion_excel():
    """显示包含均值回归的Excel结果"""
    # 查找最新的优化策略文件
    excel_files = glob.glob("HK00023优化策略交易记录_*.xlsx")
    if not excel_files:
        print("❌ 未找到优化策略文件")
        return
    
    # 获取最新文件
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"🏦 HK00023优化策略交易记录 (含均值回归): {latest_file}")
    
    try:
        # 读取所有工作表
        df_trades = pd.read_excel(latest_file, sheet_name='优化策略交易记录')
        df_summary = pd.read_excel(latest_file, sheet_name='汇总统计')
        df_strategy = pd.read_excel(latest_file, sheet_name='优化策略说明')
        df_mean_reversion = pd.read_excel(latest_file, sheet_name='均值回归分析')
        
        print(f"\n📊 HK00023优化策略汇总 (含均值回归中值):")
        print("="*90)
        
        # 显示关键统计
        for _, row in df_summary.iterrows():
            if pd.notna(row['数值']):
                print(f"{row['项目']}: {row['数值']}")
        
        print(f"\n📈 前10条交易记录预览 (含均值回归数据):")
        print("="*150)
        
        # 显示包含均值回归的关键列
        preview_columns = ['交易序号', '开仓日期', '交易方向', '开仓价格', '均值回归中值', 
                          '价格偏离度%', '价格中值比', '净利润', 'Y值', 'X值', 'E值', '策略区域']
        
        print(df_trades[preview_columns].head(10).to_string(index=False))
        
        print(f"\n📊 均值回归分析:")
        print("="*80)
        
        # 按价格偏离度分析
        print(f"按价格偏离度分析:")
        deviation_ranges = [
            ('严重高估 (>10%)', df_trades[df_trades['价格偏离度%'] > 10]),
            ('轻微高估 (5%-10%)', df_trades[(df_trades['价格偏离度%'] > 5) & (df_trades['价格偏离度%'] <= 10)]),
            ('合理区间 (-5%-5%)', df_trades[(df_trades['价格偏离度%'] >= -5) & (df_trades['价格偏离度%'] <= 5)]),
            ('轻微低估 (-10%到-5%)', df_trades[(df_trades['价格偏离度%'] >= -10) & (df_trades['价格偏离度%'] < -5)]),
            ('严重低估 (<-10%)', df_trades[df_trades['价格偏离度%'] < -10])
        ]
        
        for range_name, range_data in deviation_ranges:
            if len(range_data) > 0:
                count = len(range_data)
                total_profit = range_data['净利润'].sum()
                avg_profit = range_data['净利润'].mean()
                win_rate = len(range_data[range_data['净利润'] > 0]) / count * 100
                avg_deviation = range_data['价格偏离度%'].mean()
                
                print(f"• {range_name}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                      f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%, 平均偏离{avg_deviation:+.1f}%")
        
        # 按价格中值比分析
        print(f"\n按价格中值比分析:")
        ratio_ranges = [
            ('明显高估 (>1.1)', df_trades[df_trades['价格中值比'] > 1.1]),
            ('轻微高估 (1.05-1.1)', df_trades[(df_trades['价格中值比'] > 1.05) & (df_trades['价格中值比'] <= 1.1)]),
            ('合理估值 (0.95-1.05)', df_trades[(df_trades['价格中值比'] >= 0.95) & (df_trades['价格中值比'] <= 1.05)]),
            ('轻微低估 (0.9-0.95)', df_trades[(df_trades['价格中值比'] >= 0.9) & (df_trades['价格中值比'] < 0.95)]),
            ('明显低估 (<0.9)', df_trades[df_trades['价格中值比'] < 0.9])
        ]
        
        for range_name, range_data in ratio_ranges:
            if len(range_data) > 0:
                count = len(range_data)
                total_profit = range_data['净利润'].sum()
                avg_profit = range_data['净利润'].mean()
                win_rate = len(range_data[range_data['净利润'] > 0]) / count * 100
                avg_ratio = range_data['价格中值比'].mean()
                
                print(f"• {range_name}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                      f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%, 平均比值{avg_ratio:.3f}")
        
        # 均值回归策略效果分析
        print(f"\n📈 均值回归策略效果分析:")
        print("="*70)
        
        # 高估时做空效果
        overvalued_shorts = df_trades[(df_trades['价格偏离度%'] > 5) & (df_trades['交易方向'] == '做空')]
        if len(overvalued_shorts) > 0:
            short_profit = overvalued_shorts['净利润'].sum()
            short_count = len(overvalued_shorts)
            short_win_rate = len(overvalued_shorts[overvalued_shorts['净利润'] > 0]) / short_count * 100
            print(f"• 高估时做空: {short_count}次, 总盈亏{short_profit:+,.0f}港币, 胜率{short_win_rate:.1f}%")
        
        # 低估时做多效果
        undervalued_longs = df_trades[(df_trades['价格偏离度%'] < -5) & (df_trades['交易方向'] == '做多')]
        if len(undervalued_longs) > 0:
            long_profit = undervalued_longs['净利润'].sum()
            long_count = len(undervalued_longs)
            long_win_rate = len(undervalued_longs[undervalued_longs['净利润'] > 0]) / long_count * 100
            print(f"• 低估时做多: {long_count}次, 总盈亏{long_profit:+,.0f}港币, 胜率{long_win_rate:.1f}%")
        
        # 合理估值时的交易效果
        fair_value_trades = df_trades[(df_trades['价格偏离度%'] >= -5) & (df_trades['价格偏离度%'] <= 5)]
        if len(fair_value_trades) > 0:
            fair_profit = fair_value_trades['净利润'].sum()
            fair_count = len(fair_value_trades)
            fair_win_rate = len(fair_value_trades[fair_value_trades['净利润'] > 0]) / fair_count * 100
            print(f"• 合理估值交易: {fair_count}次, 总盈亏{fair_profit:+,.0f}港币, 胜率{fair_win_rate:.1f}%")
        
        # 显示均值回归指标说明
        print(f"\n📋 均值回归指标说明:")
        print("="*80)
        for _, row in df_mean_reversion.iterrows():
            print(f"• {row['指标名称']}: {row['计算方法']}")
            print(f"  意义: {row['意义']}")
            print(f"  应用: {row['交易应用']}")
            print()
        
        # 统计最佳交易时机
        print(f"🎯 最佳交易时机分析:")
        print("="*60)
        
        # 找出最盈利的交易
        best_trades = df_trades.nlargest(5, '净利润')
        print(f"最盈利的5笔交易:")
        for _, trade in best_trades.iterrows():
            print(f"• 交易{trade['交易序号']}: {trade['交易方向']}, 盈利{trade['净利润']:+.0f}港币, "
                  f"偏离度{trade['价格偏离度%']:+.1f}%, 价格比{trade['价格中值比']:.3f}")
        
        # 找出最亏损的交易
        worst_trades = df_trades.nsmallest(5, '净利润')
        print(f"\n最亏损的5笔交易:")
        for _, trade in worst_trades.iterrows():
            print(f"• 交易{trade['交易序号']}: {trade['交易方向']}, 亏损{trade['净利润']:+.0f}港币, "
                  f"偏离度{trade['价格偏离度%']:+.1f}%, 价格比{trade['价格中值比']:.3f}")
        
        print(f"\n💡 包含均值回归的Excel文件结构:")
        print("="*70)
        print(f"📊 工作表1: 优化策略交易记录")
        print(f"   • 100条详细交易记录")
        print(f"   • 新增: 均值回归中值、价格偏离度%、价格中值比")
        print(f"   • 包含: Y值、X值、E值、策略区域等")
        
        print(f"\n📈 工作表2: 汇总统计")
        print(f"   • 完整的财务和交易统计")
        print(f"   • 止盈止损次数统计")
        
        print(f"\n🎯 工作表3: 优化策略说明")
        print(f"   • Y>0.43, X>0.43策略详解")
        print(f"   • 止盈止损设置说明")
        
        print(f"\n📋 工作表4: 均值回归分析 (新增)")
        print(f"   • 均值回归中值计算方法")
        print(f"   • 价格偏离度分析指导")
        print(f"   • 价格中值比应用说明")
        print(f"   • 各指标的交易应用指引")
        
        print(f"\n🎉 均值回归分析已完整集成到Excel文件中!")
        print(f"💡 现在可以基于均值回归理论进行更精确的交易分析")
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")

if __name__ == "__main__":
    show_mean_reversion_excel()
