#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示优化策略结果
===============

显示HK00023优化策略的回测结果

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import glob
import os

def show_optimized_results():
    """显示优化策略结果"""
    # 查找最新的优化策略文件
    excel_files = glob.glob("HK00023优化策略交易记录_*.xlsx")
    if not excel_files:
        print("❌ 未找到优化策略文件")
        return
    
    # 获取最新文件
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"🏦 HK00023优化策略交易记录: {latest_file}")
    
    try:
        # 读取所有工作表
        df_trades = pd.read_excel(latest_file, sheet_name='优化策略交易记录')
        df_summary = pd.read_excel(latest_file, sheet_name='汇总统计')
        df_strategy = pd.read_excel(latest_file, sheet_name='优化策略说明')
        
        print(f"\n📊 HK00023优化策略汇总 (Y>0.43, X>0.43, 止盈2%, 止损1%):")
        print("="*90)
        
        # 显示关键统计
        for _, row in df_summary.iterrows():
            if pd.notna(row['数值']):
                print(f"{row['项目']}: {row['数值']}")
        
        print(f"\n📈 前15条优化交易记录预览:")
        print("="*130)
        
        # 显示前15条记录
        preview_columns = ['交易序号', '开仓日期', '交易方向', '开仓价格', '平仓价格', 
                          '净利润', '收益率%', 'Y值', 'X值', 'E值', '策略区域', '平仓原因']
        
        print(df_trades[preview_columns].head(15).to_string(index=False))
        
        print(f"\n📊 优化策略详细分析:")
        print("="*70)
        
        # 按策略区域分析
        print(f"按策略区域分析:")
        for zone in df_trades['策略区域'].unique():
            zone_data = df_trades[df_trades['策略区域'] == zone]
            count = len(zone_data)
            total_profit = zone_data['净利润'].sum()
            avg_profit = zone_data['净利润'].mean()
            win_rate = len(zone_data[zone_data['净利润'] > 0]) / count * 100
            
            print(f"• {zone}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # 按交易方向分析
        print(f"\n按交易方向分析:")
        for direction in df_trades['交易方向'].unique():
            dir_data = df_trades[df_trades['交易方向'] == direction]
            count = len(dir_data)
            total_profit = dir_data['净利润'].sum()
            avg_profit = dir_data['净利润'].mean()
            win_rate = len(dir_data[dir_data['净利润'] > 0]) / count * 100
            
            print(f"• {direction}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # E值分析
        print(f"\n按E值分析:")
        e_positive = df_trades[df_trades['E值'] > 0]
        e_negative = df_trades[df_trades['E值'] < 0]
        
        if len(e_positive) > 0:
            e_pos_profit = e_positive['净利润'].sum()
            e_pos_count = len(e_positive)
            e_pos_win_rate = len(e_positive[e_positive['净利润'] > 0]) / e_pos_count * 100
            print(f"• E>0: {e_pos_count}次, 总盈亏{e_pos_profit:+,.0f}港币, 胜率{e_pos_win_rate:.1f}%")
        
        if len(e_negative) > 0:
            e_neg_profit = e_negative['净利润'].sum()
            e_neg_count = len(e_negative)
            e_neg_win_rate = len(e_negative[e_negative['净利润'] > 0]) / e_neg_count * 100
            print(f"• E<0: {e_neg_count}次, 总盈亏{e_neg_profit:+,.0f}港币, 胜率{e_neg_win_rate:.1f}%")
        
        # 平仓原因分析
        print(f"\n按平仓原因分析:")
        exit_reasons = df_trades['平仓原因'].value_counts()
        for reason, count in exit_reasons.items():
            reason_data = df_trades[df_trades['平仓原因'] == reason]
            total_profit = reason_data['净利润'].sum()
            avg_profit = reason_data['净利润'].mean()
            win_rate = len(reason_data[reason_data['净利润'] > 0]) / count * 100
            print(f"• {reason}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # 收益率分析
        print(f"\n收益率分布分析:")
        profit_ranges = [
            ('大盈利 (>1%)', df_trades[df_trades['收益率%'] > 1]),
            ('小盈利 (0-1%)', df_trades[(df_trades['收益率%'] > 0) & (df_trades['收益率%'] <= 1)]),
            ('小亏损 (0到-1%)', df_trades[(df_trades['收益率%'] < 0) & (df_trades['收益率%'] >= -1)]),
            ('大亏损 (<-1%)', df_trades[df_trades['收益率%'] < -1])
        ]
        
        for range_name, range_data in profit_ranges:
            if len(range_data) > 0:
                count = len(range_data)
                total_profit = range_data['净利润'].sum()
                avg_return = range_data['收益率%'].mean()
                print(f"• {range_name}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                      f"平均收益率{avg_return:+.2f}%")
        
        # 与之前版本对比
        print(f"\n🔄 策略优化对比:")
        print("="*70)
        print(f"📊 区域分布优化:")
        print(f"   • 高值盈利区: 从56.3%提升到60.8% ✅")
        print(f"   • 更多交易机会，更保守止盈止损")
        
        print(f"\n📈 止盈止损优化:")
        print(f"   • 止盈: 从4%降到2% (更保守)")
        print(f"   • 止损: 从2%降到1% (更保守)")
        print(f"   • 预期: 提高胜率，降低单笔风险")
        
        print(f"\n💡 优化策略Excel文件包含:")
        print(f"   📊 100条详细优化交易记录")
        print(f"   📈 完整汇总统计和对比分析")
        print(f"   🎯 优化策略说明 (Y>0.43, X>0.43)")
        print(f"   💰 保守止盈止损设置 (2%/1%)")
        print(f"   📋 详细的平仓原因和收益分析")
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")

if __name__ == "__main__":
    show_optimized_results()
