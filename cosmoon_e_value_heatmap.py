#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cosmoon博弈论策略 - E值热力图
============================

绘制X和Y值对应的期望收益E值热力图
基于公式：E = 8xy - 3x - 3y + 1

作者: Cosmoon NG
日期: 2025年7月
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_e_value_heatmap():
    """创建E值热力图"""
    
    # 根据Cosmoon提供的数据创建E值矩阵
    e_values = np.array([
        [1.00, 0.70, 0.40, 0.10, -0.20, -0.50, -0.80, -1.10, -1.40, -1.70, -2.00],
        [0.70, 0.48, 0.26, 0.04, -0.18, -0.40, -0.62, -0.84, -1.06, -1.28, -1.50],
        [0.40, 0.26, 0.12, -0.02, -0.16, -0.30, -0.44, -0.58, -0.72, -0.86, -1.00],
        [0.10, 0.04, -0.02, -0.08, -0.14, -0.20, -0.26, -0.32, -0.38, -0.44, -0.50],
        [-0.20, -0.18, -0.16, -0.14, -0.12, -0.10, -0.08, -0.06, -0.04, -0.02, 0.00],
        [-0.50, -0.40, -0.30, -0.20, -0.10, 0.00, 0.10, 0.20, 0.30, 0.40, 0.50],
        [-0.80, -0.62, -0.44, -0.26, -0.08, 0.10, 0.28, 0.46, 0.64, 0.82, 1.00],
        [-1.10, -0.84, -0.58, -0.32, -0.06, 0.20, 0.46, 0.72, 0.98, 1.24, 1.50],
        [-1.40, -1.06, -0.72, -0.38, -0.04, 0.30, 0.64, 0.98, 1.32, 1.66, 2.00],
        [-1.70, -1.28, -0.86, -0.44, -0.02, 0.40, 0.82, 1.24, 1.66, 2.08, 2.50],
        [-2.00, -1.50, -1.00, -0.50, 0.00, 0.50, 1.00, 1.50, 2.00, 2.50, 3.00]
    ])
    
    # X和Y的标签
    x_labels = [f'{i/10:.1f}' for i in range(0, 11)]
    y_labels = [f'{i/10:.1f}' for i in range(0, 11)]
    
    # 创建图形
    plt.figure(figsize=(14, 10))
    
    # 创建热力图
    ax = sns.heatmap(e_values, 
                     xticklabels=x_labels,
                     yticklabels=y_labels,
                     annot=True,
                     fmt='.2f',
                     cmap='RdYlGn',  # 红-黄-绿色谱，红色表示负值，绿色表示正值
                     center=0,       # 以0为中心
                     cbar_kws={'label': 'E值 (期望收益)'},
                     linewidths=0.5,
                     linecolor='white')
    
    # 设置标题和标签
    plt.title('Cosmoon博弈论策略 - E值热力图\nE = 8xy - 3x - 3y + 1', 
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('X值 (资金流入比例)', fontsize=14, fontweight='bold')
    plt.ylabel('Y值 (控股商托价概率)', fontsize=14, fontweight='bold')
    
    # 添加关键区域标注
    # 盈利区域 (E > 0)
    plt.text(0.5, 0.5, '亏损区域\nE < 0', 
             ha='center', va='center', fontsize=12, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.3))
    
    plt.text(8.5, 8.5, '盈利区域\nE > 0', 
             ha='center', va='center', fontsize=12, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.3))
    
    # 标注关键阈值线
    # Y=0.4线 (买入阈值)
    plt.axhline(y=4.5, color='blue', linestyle='--', linewidth=2, alpha=0.7)
    plt.text(10.5, 4.5, 'Y=0.4\n(买入阈值)', ha='left', va='center', 
             fontsize=10, fontweight='bold', color='blue')
    
    # Y=0.333线 (卖出阈值)
    plt.axhline(y=3.33, color='orange', linestyle='--', linewidth=2, alpha=0.7)
    plt.text(10.5, 3.33, 'Y=0.333\n(卖出阈值)', ha='left', va='center', 
             fontsize=10, fontweight='bold', color='orange')
    
    # X=0.4线 (关键阈值)
    plt.axvline(x=4.5, color='purple', linestyle='--', linewidth=2, alpha=0.7)
    plt.text(4.5, 10.5, 'X=0.4\n(关键阈值)', ha='center', va='bottom', 
             fontsize=10, fontweight='bold', color='purple')
    
    plt.tight_layout()
    
    # 保存图表
    filename = 'cosmoon_e_value_heatmap.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ E值热力图已保存: {filename}")
    
    return filename

def analyze_strategy_zones():
    """分析策略区域"""
    print("\n🎯 Cosmoon博弈论策略区域分析:")
    print("="*50)
    
    print("📈 盈利区域 (E > 0):")
    print("   • 高Y高X区域: Y > 0.5 且 X > 0.5")
    print("   • 最佳盈利点: Y=1.0, X=1.0, E=3.00")
    print("   • 策略建议: 积极买入并持有")
    
    print("\n📉 亏损区域 (E < 0):")
    print("   • 低Y低X区域: Y < 0.4 且 X < 0.4")
    print("   • 最大亏损点: Y=0.0, X=1.0, E=-2.00")
    print("   • 策略建议: 避免交易或卖出")
    
    print("\n⚖️ 平衡区域 (E ≈ 0):")
    print("   • 对角线区域: Y=0.4-0.5, X=0.4-0.5")
    print("   • 策略建议: 谨慎观望")
    
    print("\n🎯 Cosmoon的交易规则:")
    print("   • 买入条件: Y > 0.4 且 X > 0.4")
    print("   • 卖出条件: Y < 0.333 或 X < 0.4")
    print("   • 避免区域: 0.333 ≤ Y ≤ 0.4 (控股商控制区)")

def create_3d_surface():
    """创建3D表面图"""
    print("\n🎨 创建3D表面图...")
    
    # 创建更精细的网格
    x = np.linspace(0, 1, 50)
    y = np.linspace(0, 1, 50)
    X, Y = np.meshgrid(x, y)
    
    # 计算E值: E = 8xy - 3x - 3y + 1
    E = 8 * X * Y - 3 * X - 3 * Y + 1
    
    # 创建3D图
    fig = plt.figure(figsize=(12, 9))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制表面
    surf = ax.plot_surface(X, Y, E, cmap='RdYlGn', alpha=0.8, 
                          linewidth=0, antialiased=True)
    
    # 添加等高线
    contours = ax.contour(X, Y, E, levels=10, colors='black', alpha=0.4, linewidths=0.5)
    
    # 标注关键点
    # 最大值点
    ax.scatter([1], [1], [3], color='green', s=100, label='最大盈利点 (1,1,3)')
    
    # 最小值点
    ax.scatter([1], [0], [-2], color='red', s=100, label='最大亏损点 (1,0,-2)')
    
    # 零点
    ax.scatter([0.4], [0.4], [0], color='blue', s=100, label='关键点 (0.4,0.4,0)')
    
    # 设置标签
    ax.set_xlabel('X值 (资金流入比例)', fontsize=12)
    ax.set_ylabel('Y值 (控股商托价概率)', fontsize=12)
    ax.set_zlabel('E值 (期望收益)', fontsize=12)
    ax.set_title('Cosmoon博弈论策略 - E值3D表面图\nE = 8xy - 3x - 3y + 1', 
                fontsize=14, fontweight='bold')
    
    # 添加颜色条
    fig.colorbar(surf, shrink=0.5, aspect=5, label='E值')
    
    # 添加图例
    ax.legend()
    
    plt.tight_layout()
    
    # 保存3D图
    filename_3d = 'cosmoon_e_value_3d.png'
    plt.savefig(filename_3d, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 3D表面图已保存: {filename_3d}")
    
    return filename_3d

def main():
    """主函数"""
    print("🎯 Cosmoon博弈论策略 - E值可视化分析")
    print("="*50)
    print("📅 生成时间:", pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("📊 公式: E = 8xy - 3x - 3y + 1")
    
    # 创建热力图
    heatmap_file = create_e_value_heatmap()
    
    # 分析策略区域
    analyze_strategy_zones()
    
    # 创建3D图
    surface_file = create_3d_surface()
    
    print(f"\n🎉 可视化分析完成!")
    print(f"📊 热力图: {heatmap_file}")
    print(f"🎨 3D图: {surface_file}")
    print(f"💡 现在可以根据这些图表进行博弈论策略交易了!")

if __name__ == "__main__":
    main()
