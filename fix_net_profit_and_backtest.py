#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正test表净利润并重新回测
========================

1. 修正净利润计算错误
2. 修正观望记录净利润
3. 验证数据一致性
4. 重新运行回测

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

def fix_net_profit_and_backtest():
    """修正净利润并重新回测"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("🔧 修正test表净利润并重新回测")
        print("="*80)
        
        # 1. 修正净利润计算
        print("\n1️⃣ 修正净利润计算...")
        print("   使用公式: 净利润 = 毛利润 - 交易成本")
        
        cursor.execute("""
            UPDATE test 
            SET 净利润 = 毛利润 - 交易成本
            WHERE 交易方向 != '观望'
        """)
        
        updated_rows = cursor.rowcount
        print(f"✅ 更新了 {updated_rows} 条交易记录的净利润")
        
        # 2. 修正观望记录净利润
        print("\n2️⃣ 修正观望记录净利润...")
        print("   观望记录净利润应该为0")
        
        cursor.execute("""
            UPDATE test 
            SET 净利润 = 0, 毛利润 = 0, 交易成本 = 0
            WHERE 交易方向 = '观望'
        """)
        
        observe_rows = cursor.rowcount
        print(f"✅ 更新了 {observe_rows} 条观望记录的净利润")
        
        # 提交修正
        connection.commit()
        
        # 3. 验证修正结果
        print("\n3️⃣ 验证修正结果...")
        
        cursor.execute("""
            SELECT 
                COUNT(*) as 总记录数,
                SUM(净利润) as 净利润总和,
                AVG(净利润) as 平均净利润,
                COUNT(CASE WHEN ABS(净利润 - (毛利润 - 交易成本)) > 1 THEN 1 END) as 异常记录
            FROM test
        """)
        
        verification = cursor.fetchone()
        total_count, net_sum, net_avg, anomaly_count = verification
        
        print(f"   • 总记录数: {total_count}")
        print(f"   • 净利润总和: {net_sum:+,.0f}港币")
        print(f"   • 平均净利润: {net_avg:+.1f}港币")
        print(f"   • 异常记录: {anomaly_count}条")
        
        if anomaly_count == 0:
            print("✅ 所有净利润计算正确")
        else:
            print("⚠️ 仍有异常记录需要检查")
        
        # 4. 验证观望记录
        cursor.execute("""
            SELECT COUNT(*), SUM(净利润) 
            FROM test 
            WHERE 交易方向 = '观望'
        """)
        
        observe_check = cursor.fetchone()
        observe_count, observe_sum = observe_check
        
        print(f"   • 观望记录数: {observe_count}")
        print(f"   • 观望净利润总和: {observe_sum if observe_sum else 0}港币")
        
        if observe_sum == 0:
            print("✅ 观望记录净利润正确")
        else:
            print("⚠️ 观望记录净利润仍有问题")
        
        # 5. 显示修正后的前10条记录
        print("\n4️⃣ 修正后前10条记录验证:")
        cursor.execute("""
            SELECT 交易序号, 交易方向, close, 平仓价格, 毛利润, 交易成本, 净利润,
                   (毛利润 - 交易成本) as 计算净利润
            FROM test 
            ORDER BY 交易序号 
            LIMIT 10
        """)
        
        records = cursor.fetchall()
        
        print("-" * 100)
        print(f"{'序号':<4} {'方向':<6} {'开仓价':<8} {'平仓价':<8} {'毛利润':<8} {'成本':<6} {'净利润':<8} {'计算值':<8}")
        print("-" * 100)
        
        for record in records:
            trade_id, direction, open_price, close_price, gross_profit, cost, net_profit, calc_net = record
            print(f"{trade_id:<4} {direction:<6} {float(open_price):<8.2f} {float(close_price):<8.2f} "
                  f"{gross_profit:<8} {cost:<6} {net_profit:<8} {calc_net:<8}")
        
        # 6. 重新运行回测
        print("\n" + "="*80)
        print("🎯 使用修正后数据重新运行回测")
        print("="*80)
        
        # 获取所有记录进行回测
        cursor.execute("""
            SELECT 交易序号, 开仓日期, close, 平仓价格, 交易方向, 交易股数,
                   `profit价格`, `loss价格`, 净利润, `控制系数`, `资金流比例`,
                   CASE 
                       WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                       ELSE '其他区域'
                   END AS 策略区域
            FROM test 
            ORDER BY 交易序号
        """)
        
        backtest_records = cursor.fetchall()
        
        # 回测逻辑
        initial_capital = 30000
        current_capital = initial_capital
        
        total_trades = 0
        observe_count = 0
        take_profit_count = 0
        stop_loss_count = 0
        normal_close_count = 0
        
        total_net_profit = 0
        
        print(f"\n📊 修正后回测详细结果 (前30条记录):")
        print("-" * 140)
        print(f"{'序号':<4} {'策略区域':<12} {'方向':<6} {'开仓价':<8} {'平仓价':<8} "
              f"{'profit价格':<10} {'loss价格':<9} {'回测结果':<10} {'净利润':<8}")
        print("-" * 140)
        
        for i, record in enumerate(backtest_records):
            (trade_id, open_date, open_price, close_price, direction, shares,
             profit_price, loss_price, net_profit, y_val, x_val, zone) = record
            
            # 回测逻辑判断
            if direction == '观望':
                backtest_result = '观望'
                observe_count += 1
            else:
                total_trades += 1
                
                if direction == '买涨':
                    if float(close_price) >= float(profit_price):
                        backtest_result = '止盈'
                        take_profit_count += 1
                    elif float(close_price) <= float(loss_price):
                        backtest_result = '止损'
                        stop_loss_count += 1
                    else:
                        backtest_result = '到期平仓'
                        normal_close_count += 1
                else:  # 买跌
                    if float(close_price) <= float(profit_price):
                        backtest_result = '止盈'
                        take_profit_count += 1
                    elif float(close_price) >= float(loss_price):
                        backtest_result = '止损'
                        stop_loss_count += 1
                    else:
                        backtest_result = '到期平仓'
                        normal_close_count += 1
            
            current_capital += net_profit
            total_net_profit += net_profit
            
            # 显示前30条记录
            if i < 30:
                print(f"{trade_id:<4} {zone:<12} {direction:<6} {float(open_price):<8.2f} {float(close_price):<8.2f} "
                      f"{float(profit_price):<10.2f} {float(loss_price):<9.2f} {backtest_result:<10} {net_profit:<8}")
        
        print("\n" + "="*100)
        
        # 回测结果统计
        print(f"📈 修正后回测结果统计:")
        print(f"   • 初始资金: {initial_capital:,}港币")
        print(f"   • 最终资金: {current_capital:,.0f}港币")
        print(f"   • 总净利润: {total_net_profit:+,.0f}港币")
        print(f"   • 总收益率: {(current_capital/initial_capital-1)*100:+.2f}%")
        
        print(f"\n📊 交易统计:")
        print(f"   • 总记录数: {len(backtest_records)}")
        print(f"   • 实际交易: {total_trades}")
        print(f"   • 观望次数: {observe_count}")
        print(f"   • 止盈次数: {take_profit_count} ({take_profit_count/total_trades*100:.1f}%)")
        print(f"   • 止损次数: {stop_loss_count} ({stop_loss_count/total_trades*100:.1f}%)")
        print(f"   • 到期平仓: {normal_close_count} ({normal_close_count/total_trades*100:.1f}%)")
        
        if total_trades > 0:
            win_rate = take_profit_count / total_trades
            print(f"   • 胜率: {win_rate*100:.1f}%")
        
        # 按策略区域分析
        print(f"\n📊 按策略区域分析修正后结果:")
        print("-" * 80)
        
        zones = ['高值盈利区', '强亏损区', '其他区域', '控股商控制区']
        
        for zone_name in zones:
            zone_records = [r for r in backtest_records if r[11] == zone_name]
            
            if zone_records:
                zone_count = len(zone_records)
                zone_net_profit = sum(r[8] for r in zone_records)
                zone_take_profit = 0
                zone_stop_loss = 0
                zone_normal_close = 0
                zone_observe = 0
                
                for record in zone_records:
                    direction = record[4]
                    close_price = record[3]
                    profit_price = record[6]
                    loss_price = record[7]
                    
                    if direction == '观望':
                        zone_observe += 1
                    elif direction == '买涨':
                        if float(close_price) >= float(profit_price):
                            zone_take_profit += 1
                        elif float(close_price) <= float(loss_price):
                            zone_stop_loss += 1
                        else:
                            zone_normal_close += 1
                    else:  # 买跌
                        if float(close_price) <= float(profit_price):
                            zone_take_profit += 1
                        elif float(close_price) >= float(loss_price):
                            zone_stop_loss += 1
                        else:
                            zone_normal_close += 1
                
                zone_trades = zone_take_profit + zone_stop_loss + zone_normal_close
                
                print(f"• {zone_name}:")
                print(f"  - 总次数: {zone_count}")
                print(f"  - 净利润: {zone_net_profit:+,.0f}港币")
                print(f"  - 止盈: {zone_take_profit}次, 止损: {zone_stop_loss}次, 到期: {zone_normal_close}次, 观望: {zone_observe}次")
                
                if zone_trades > 0:
                    zone_win_rate = zone_take_profit / zone_trades
                    print(f"  - 胜率: {zone_win_rate*100:.1f}%")
                    print(f"  - 平均盈亏: {zone_net_profit/zone_trades:+.0f}港币/次")
        
        connection.close()
        print(f"\n🎉 净利润修正和回测完成!")
        
    except Exception as e:
        print(f"❌ 修正和回测失败: {e}")

if __name__ == "__main__":
    fix_net_profit_and_backtest()
