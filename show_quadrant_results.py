#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示四象限策略结果
=================

显示HK00023四象限策略的回测结果

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import glob
import os

def show_quadrant_results():
    """显示四象限策略结果"""
    # 查找最新的四象限策略文件
    excel_files = glob.glob("HK00023四象限策略回测_*.xlsx")
    if not excel_files:
        print("❌ 未找到四象限策略文件")
        return
    
    # 获取最新文件
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"🏦 HK00023四象限策略回测: {latest_file}")
    
    try:
        # 读取所有工作表
        df_trades = pd.read_excel(latest_file, sheet_name='四象限交易记录')
        df_summary = pd.read_excel(latest_file, sheet_name='汇总统计')
        df_quadrant = pd.read_excel(latest_file, sheet_name='四象限分析')
        df_strategy = pd.read_excel(latest_file, sheet_name='四象限策略说明')
        df_indicators = pd.read_excel(latest_file, sheet_name='指标说明')
        
        print(f"\n📊 HK00023四象限策略汇总:")
        print("="*90)
        
        # 显示关键统计
        for _, row in df_summary.iterrows():
            if pd.notna(row['数值']):
                print(f"{row['项目']}: {row['数值']}")
        
        print(f"\n📊 四象限分析:")
        print("="*80)
        print(df_quadrant.to_string(index=False))
        
        print(f"\n📈 前15条四象限交易记录预览:")
        print("="*150)
        
        # 显示关键列
        preview_columns = ['交易序号', '开仓日期', '交易方向', '开仓价格', '均值回归中值', 
                          '价格偏离度%', '净利润', 'Y值', 'X值', 'E值', '四象限', '平仓原因']
        
        print(df_trades[preview_columns].head(15).to_string(index=False))
        
        print(f"\n📊 四象限策略详细分析:")
        print("="*80)
        
        # 按四象限分析
        print(f"按四象限详细分析:")
        for quadrant in df_trades['四象限'].unique():
            quadrant_data = df_trades[df_trades['四象限'] == quadrant]
            count = len(quadrant_data)
            total_profit = quadrant_data['净利润'].sum()
            avg_profit = quadrant_data['净利润'].mean()
            win_rate = len(quadrant_data[quadrant_data['净利润'] > 0]) / count * 100
            
            # 显示该象限的交易方向
            directions = quadrant_data['交易方向'].value_counts()
            direction_str = ", ".join([f"{dir}:{cnt}次" for dir, cnt in directions.items()])
            
            print(f"• {quadrant}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%, 方向({direction_str})")
        
        # 按交易方向分析
        print(f"\n按交易方向分析:")
        for direction in df_trades['交易方向'].unique():
            dir_data = df_trades[df_trades['交易方向'] == direction]
            count = len(dir_data)
            total_profit = dir_data['净利润'].sum()
            avg_profit = dir_data['净利润'].mean()
            win_rate = len(dir_data[dir_data['净利润'] > 0]) / count * 100
            
            print(f"• {direction}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # 四象限理论验证
        print(f"\n🎯 四象限理论验证:")
        print("="*70)
        
        # 第一象限分析 (Y>0.5,X>0.5 买升)
        q1_data = df_trades[df_trades['四象限'] == '第一象限(Y>0.5,X>0.5)']
        if len(q1_data) > 0:
            q1_profit = q1_data['净利润'].sum()
            q1_count = len(q1_data)
            q1_win_rate = len(q1_data[q1_data['净利润'] > 0]) / q1_count * 100
            q1_avg_y = q1_data['Y值'].mean()
            q1_avg_x = q1_data['X值'].mean()
            
            print(f"第一象限 (Y>0.5,X>0.5 买升):")
            print(f"• 理论: 控股商托价且资金流入，双重利好，应该买升")
            print(f"• 实际: {q1_count}次做多，总盈亏{q1_profit:+,.0f}港币，胜率{q1_win_rate:.1f}%")
            print(f"• 平均Y值: {q1_avg_y:.3f}, 平均X值: {q1_avg_x:.3f}")
            print(f"• 验证结果: {'✅ 理论正确' if q1_profit > 0 else '❌ 理论待验证'}")
        
        # 其他象限分析
        other_quadrants = ['第二象限(Y<0.5,X>0.5)', '第三象限(Y>0.5,X<0.5)', '第四象限(Y<0.4,X<0.4)']
        for quadrant in other_quadrants:
            q_data = df_trades[df_trades['四象限'] == quadrant]
            if len(q_data) > 0:
                q_profit = q_data['净利润'].sum()
                q_count = len(q_data)
                q_win_rate = len(q_data[q_data['净利润'] > 0]) / q_count * 100
                q_avg_y = q_data['Y值'].mean()
                q_avg_x = q_data['X值'].mean()
                
                print(f"\n{quadrant} (买跌):")
                print(f"• 实际: {q_count}次做空，总盈亏{q_profit:+,.0f}港币，胜率{q_win_rate:.1f}%")
                print(f"• 平均Y值: {q_avg_y:.3f}, 平均X值: {q_avg_x:.3f}")
                print(f"• 验证结果: {'✅ 理论正确' if q_profit > 0 else '❌ 理论待验证'}")
        
        # E值分析
        print(f"\n按E值分析:")
        e_positive = df_trades[df_trades['E值'] > 0]
        e_negative = df_trades[df_trades['E值'] < 0]
        
        if len(e_positive) > 0:
            e_pos_profit = e_positive['净利润'].sum()
            e_pos_count = len(e_positive)
            e_pos_win_rate = len(e_positive[e_positive['净利润'] > 0]) / e_pos_count * 100
            print(f"• E>0: {e_pos_count}次, 总盈亏{e_pos_profit:+,.0f}港币, 胜率{e_pos_win_rate:.1f}%")
        
        if len(e_negative) > 0:
            e_neg_profit = e_negative['净利润'].sum()
            e_neg_count = len(e_negative)
            e_neg_win_rate = len(e_negative[e_negative['净利润'] > 0]) / e_neg_count * 100
            print(f"• E<0: {e_neg_count}次, 总盈亏{e_neg_profit:+,.0f}港币, 胜率{e_neg_win_rate:.1f}%")
        
        # 平仓原因分析
        print(f"\n按平仓原因分析:")
        exit_reasons = df_trades['平仓原因'].value_counts()
        for reason, count in exit_reasons.items():
            reason_data = df_trades[df_trades['平仓原因'] == reason]
            total_profit = reason_data['净利润'].sum()
            avg_profit = reason_data['净利润'].mean()
            win_rate = len(reason_data[reason_data['净利润'] > 0]) / count * 100
            print(f"• {reason}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # 赔率1:2效果分析
        print(f"\n📊 赔率1:2效果分析:")
        print("="*60)
        
        take_profit_trades = df_trades[df_trades['平仓原因'] == '止盈']
        stop_loss_trades = df_trades[df_trades['平仓原因'] == '止损']
        
        if len(take_profit_trades) > 0 and len(stop_loss_trades) > 0:
            tp_count = len(take_profit_trades)
            sl_count = len(stop_loss_trades)
            tp_profit = take_profit_trades['净利润'].sum()
            sl_loss = stop_loss_trades['净利润'].sum()
            
            print(f"止盈交易: {tp_count}次, 总盈利{tp_profit:+,.0f}港币")
            print(f"止损交易: {sl_count}次, 总亏损{sl_loss:+,.0f}港币")
            print(f"止盈止损比例: {tp_count}:{sl_count}")
            
            # 计算盈亏平衡点
            break_even_rate = sl_count / (tp_count + sl_count) * 100
            theoretical_break_even = 1 / (1 + 2) * 100  # 33.33%
            
            print(f"实际止损比例: {break_even_rate:.1f}%")
            print(f"理论盈亏平衡点: {theoretical_break_even:.1f}%")
            print(f"赔率效果: {'✅ 有利' if break_even_rate < theoretical_break_even else '❌ 不利'}")
        
        # 均值回归分析
        print(f"\n📈 均值回归分析:")
        print("="*60)
        
        # 分析不同偏离度下的策略效果
        deviation_ranges = [
            ('高估区 (>5%)', df_trades[df_trades['价格偏离度%'] > 5]),
            ('合理区 (-5%到5%)', df_trades[(df_trades['价格偏离度%'] >= -5) & (df_trades['价格偏离度%'] <= 5)]),
            ('低估区 (<-5%)', df_trades[df_trades['价格偏离度%'] < -5])
        ]
        
        for range_name, range_data in deviation_ranges:
            if len(range_data) > 0:
                count = len(range_data)
                total_profit = range_data['净利润'].sum()
                win_rate = len(range_data[range_data['净利润'] > 0]) / count * 100
                avg_deviation = range_data['价格偏离度%'].mean()
                
                # 分析该区域的四象限分布
                quadrant_dist = range_data['四象限'].value_counts()
                
                print(f"• {range_name}: {count}次, 盈亏{total_profit:+,.0f}港币, "
                      f"胜率{win_rate:.1f}%, 平均偏离{avg_deviation:+.1f}%")
                print(f"  主要象限: {quadrant_dist.index[0] if len(quadrant_dist) > 0 else 'N/A'}")
        
        # 最佳和最差交易分析
        print(f"\n🎯 最佳和最差交易分析:")
        print("="*60)
        
        best_trade = df_trades.loc[df_trades['净利润'].idxmax()]
        worst_trade = df_trades.loc[df_trades['净利润'].idxmin()]
        
        print(f"最佳交易:")
        print(f"• 交易{best_trade['交易序号']}: {best_trade['开仓日期']}, {best_trade['交易方向']}")
        print(f"  盈利{best_trade['净利润']:+.0f}港币, {best_trade['四象限']}")
        print(f"  Y={best_trade['Y值']:.3f}, X={best_trade['X值']:.3f}, E={best_trade['E值']:.3f}")
        print(f"  偏离度{best_trade['价格偏离度%']:+.1f}%, 平仓原因: {best_trade['平仓原因']}")
        
        print(f"\n最差交易:")
        print(f"• 交易{worst_trade['交易序号']}: {worst_trade['开仓日期']}, {worst_trade['交易方向']}")
        print(f"  亏损{worst_trade['净利润']:+.0f}港币, {worst_trade['四象限']}")
        print(f"  Y={worst_trade['Y值']:.3f}, X={worst_trade['X值']:.3f}, E={worst_trade['E值']:.3f}")
        print(f"  偏离度{worst_trade['价格偏离度%']:+.1f}%, 平仓原因: {worst_trade['平仓原因']}")
        
        # 四象限策略总结
        print(f"\n📊 四象限策略总结:")
        print("="*70)
        
        final_capital = df_trades['账户余额'].iloc[-1]
        total_return = (final_capital / 30000 - 1) * 100
        total_profit = df_trades['净利润'].sum()
        win_rate = len(df_trades[df_trades['净利润'] > 0]) / len(df_trades) * 100
        
        print(f"• 策略框架: 四象限分析 + 1:2赔率")
        print(f"• 总收益率: {total_return:+.2f}%")
        print(f"• 总盈亏: {total_profit:+,.0f}港币")
        print(f"• 总交易次数: {len(df_trades)}次")
        print(f"• 胜率: {win_rate:.1f}%")
        print(f"• 最大单笔盈利: {df_trades['净利润'].max():+.0f}港币")
        print(f"• 最大单笔亏损: {df_trades['净利润'].min():+.0f}港币")
        print(f"• 平均每笔盈亏: {total_profit/len(df_trades):+.0f}港币")
        
        print(f"\n💡 四象限策略Excel文件结构:")
        print("="*70)
        print(f"📊 工作表1: 四象限交易记录")
        print(f"   • {len(df_trades)}条详细交易记录")
        print(f"   • 包含四象限分类和理由")
        
        print(f"\n📈 工作表2: 汇总统计")
        print(f"   • 完整的财务统计")
        print(f"   • 赔率1:2效果分析")
        
        print(f"\n📊 工作表3: 四象限分析")
        print(f"   • 各象限详细表现")
        print(f"   • 象限盈亏统计")
        
        print(f"\n🎯 工作表4: 四象限策略说明")
        print(f"   • 四象限理论基础")
        print(f"   • 各象限操作指引")
        
        print(f"\n📋 工作表5: 指标说明")
        print(f"   • Y、X、E值详细说明")
        print(f"   • 均值回归指标应用")
        
        print(f"\n🎉 四象限策略关键发现:")
        print("="*60)
        
        # 找出表现最好的象限
        best_quadrant = df_quadrant.loc[df_quadrant['总盈亏'].idxmax()]
        worst_quadrant = df_quadrant.loc[df_quadrant['总盈亏'].idxmin()]
        
        print(f"✅ 最佳象限: {best_quadrant['四象限']}")
        print(f"   {best_quadrant['交易次数']}次{best_quadrant['交易方向']}，盈利{best_quadrant['总盈亏']:+.0f}港币")
        
        print(f"❌ 最差象限: {worst_quadrant['四象限']}")
        print(f"   {worst_quadrant['交易次数']}次{worst_quadrant['交易方向']}，亏损{worst_quadrant['总盈亏']:+.0f}港币")
        
        print(f"💡 四象限理论验证: 需要进一步优化参数和条件")
        print(f"🎯 赔率1:2设置: 理论可行，实际效果待优化")
        
        # 显示策略说明
        print(f"\n📋 四象限策略说明:")
        print("="*70)
        print(df_strategy.to_string(index=False))
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")

if __name__ == "__main__":
    show_quadrant_results()
