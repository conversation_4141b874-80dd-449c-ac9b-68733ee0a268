#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cosmoon博弈论策略 - 简化E值热力图
===============================

快速生成E值热力图，不显示交互界面
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_simple_heatmap():
    """创建简化的E值热力图"""
    
    print("📊 正在生成E值热力图...")
    
    # Cosmoon提供的E值数据
    e_values = np.array([
        [1.00, 0.70, 0.40, 0.10, -0.20, -0.50, -0.80, -1.10, -1.40, -1.70, -2.00],
        [0.70, 0.48, 0.26, 0.04, -0.18, -0.40, -0.62, -0.84, -1.06, -1.28, -1.50],
        [0.40, 0.26, 0.12, -0.02, -0.16, -0.30, -0.44, -0.58, -0.72, -0.86, -1.00],
        [0.10, 0.04, -0.02, -0.08, -0.14, -0.20, -0.26, -0.32, -0.38, -0.44, -0.50],
        [-0.20, -0.18, -0.16, -0.14, -0.12, -0.10, -0.08, -0.06, -0.04, -0.02, 0.00],
        [-0.50, -0.40, -0.30, -0.20, -0.10, 0.00, 0.10, 0.20, 0.30, 0.40, 0.50],
        [-0.80, -0.62, -0.44, -0.26, -0.08, 0.10, 0.28, 0.46, 0.64, 0.82, 1.00],
        [-1.10, -0.84, -0.58, -0.32, -0.06, 0.20, 0.46, 0.72, 0.98, 1.24, 1.50],
        [-1.40, -1.06, -0.72, -0.38, -0.04, 0.30, 0.64, 0.98, 1.32, 1.66, 2.00],
        [-1.70, -1.28, -0.86, -0.44, -0.02, 0.40, 0.82, 1.24, 1.66, 2.08, 2.50],
        [-2.00, -1.50, -1.00, -0.50, 0.00, 0.50, 1.00, 1.50, 2.00, 2.50, 3.00]
    ])
    
    # 翻转数组，使Y轴从下到上递增
    e_values = np.flipud(e_values)
    
    # X和Y的标签
    x_labels = [f'{i/10:.1f}' for i in range(0, 11)]
    y_labels = [f'{i/10:.1f}' for i in range(10, -1, -1)]  # 从1.0到0.0
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 10))
    
    # 创建热力图
    im = ax.imshow(e_values, cmap='RdYlGn', aspect='auto', 
                   vmin=-2, vmax=3, interpolation='nearest')
    
    # 设置刻度
    ax.set_xticks(range(11))
    ax.set_yticks(range(11))
    ax.set_xticklabels(x_labels)
    ax.set_yticklabels(y_labels)
    
    # 添加数值标注
    for i in range(11):
        for j in range(11):
            text = ax.text(j, i, f'{e_values[i, j]:.2f}',
                          ha="center", va="center", color="black", fontsize=8)
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax)
    cbar.set_label('E值 (期望收益)', fontsize=12)
    
    # 设置标题和标签
    ax.set_title('Cosmoon博弈论策略 - E值热力图\nE = 8xy - 3x - 3y + 1', 
                fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('X值 (资金流入比例)', fontsize=14)
    ax.set_ylabel('Y值 (控股商托价概率)', fontsize=14)
    
    # 添加关键线条
    # Y=0.4线 (买入阈值) - 对应索引6
    ax.axhline(y=6, color='blue', linestyle='--', linewidth=2, alpha=0.8)
    ax.text(10.2, 6, 'Y=0.4\n买入阈值', ha='left', va='center', 
            fontsize=10, fontweight='bold', color='blue')
    
    # Y=0.333线 (卖出阈值) - 对应索引6.67
    ax.axhline(y=6.67, color='orange', linestyle='--', linewidth=2, alpha=0.8)
    ax.text(10.2, 6.67, 'Y=0.333\n卖出阈值', ha='left', va='center', 
            fontsize=10, fontweight='bold', color='orange')
    
    # X=0.4线 (关键阈值) - 对应索引4
    ax.axvline(x=4, color='purple', linestyle='--', linewidth=2, alpha=0.8)
    ax.text(4, -0.5, 'X=0.4\n关键阈值', ha='center', va='top', 
            fontsize=10, fontweight='bold', color='purple')
    
    plt.tight_layout()
    
    # 保存图表
    filename = 'cosmoon_e_heatmap.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形，释放内存
    
    print(f"✅ E值热力图已保存: {filename}")
    return filename

def analyze_zones():
    """分析策略区域"""
    print("\n🎯 Cosmoon博弈论策略区域分析:")
    print("="*50)
    
    print("📈 盈利区域 (E > 0):")
    print("   • Y > 0.5 且 X > 0.5: 强盈利区")
    print("   • Y > 0.4 且 X > 0.4: 一般盈利区")
    print("   • 最佳点: Y=1.0, X=1.0, E=3.00")
    
    print("\n📉 亏损区域 (E < 0):")
    print("   • Y < 0.4 且 X < 0.6: 主要亏损区")
    print("   • Y < 0.333: 强制卖出区")
    print("   • 最差点: Y=0.0, X=1.0, E=-2.00")
    
    print("\n⚖️ 平衡区域 (E ≈ 0):")
    print("   • Y=0.4, X=1.0: E=0.00")
    print("   • Y=0.5, X=0.5: E=0.00")
    
    print("\n🎯 交易策略:")
    print("   ✅ 买入: Y > 0.4 且 X > 0.4")
    print("   ❌ 卖出: Y < 0.333 或 X < 0.4")
    print("   ⏸️ 观望: 0.333 ≤ Y ≤ 0.4")

def main():
    """主函数"""
    print("🎯 Cosmoon博弈论策略 - E值热力图生成器")
    print("="*50)
    
    try:
        # 生成热力图
        filename = create_simple_heatmap()
        
        # 分析策略区域
        analyze_zones()
        
        print(f"\n🎉 E值热力图生成完成!")
        print(f"📊 文件: {filename}")
        print(f"💡 可以根据此图制定交易策略!")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")

if __name__ == "__main__":
    main()
