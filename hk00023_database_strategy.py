#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于数据库资金流的HK00023策略
============================

使用真实的资金流数据库优化策略：
- 高值盈利区 (Y>0.43, X>0.43): 买涨，止盈+2%，止损-1%
- 控股商控制区 (0.333<Y<0.4): 观望
- 强亏损区 (Y<0.25或X<0.25): 买涨，止盈+2%，止损-1%
- 其他区域: 买跌，止盈-1%，止损+2.3%

使用数据库中的真实资金流数据替代估算的X值

作者: Cosmoon NG
日期: 2025年7月
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class HK00023DatabaseStrategy:
    def __init__(self, db_path="hk00023_20year.db"):
        """初始化基于数据库的策略"""
        self.db_path = db_path
        self.initial_capital = 30000
        self.conn = None
        
        # 策略参数
        self.strategy_params = {
            'high_profit_y': 0.43,
            'high_profit_x': 0.43,
            'control_zone_min': 0.333,
            'control_zone_max': 0.4,
            'strong_loss_y': 0.25,
            'strong_loss_x': 0.25,
            
            # 做多参数
            'long_take_profit': 0.02,   # 2%止盈
            'long_stop_loss': 0.01,     # 1%止损
            
            # 做空参数
            'short_take_profit': 0.01,  # 1%止盈
            'short_stop_loss': 0.023,   # 2.3%止损
            
            'transaction_cost': 0.0025,
            'position_ratio': 0.08,
        }
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            print(f"✅ 成功连接数据库: {self.db_path}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def load_database_data(self):
        """从数据库加载数据"""
        try:
            print("📊 从数据库加载HK00023数据...")
            
            sql = """
                SELECT date, open, high, low, close, volume,
                       money_flow_in, money_flow_out, net_money_flow,
                       money_flow_ratio, money_flow_intensity, cumulative_money_flow
                FROM hk00023 
                ORDER BY date
            """
            
            df = pd.read_sql_query(sql, self.conn)
            df['date'] = pd.to_datetime(df['date'])
            
            print(f"✅ 成功加载数据:")
            print(f"   • 数据期间: {df['date'].min().strftime('%Y-%m-%d')} 至 {df['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(df)} 天")
            print(f"   • 包含真实资金流数据")
            
            return df
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return None
    
    def calculate_enhanced_indicators(self, data):
        """计算增强指标，使用真实资金流数据"""
        print("🎯 计算增强策略指标 (使用真实资金流数据)...")
        
        # 移动平均线
        data['ma_20'] = data['close'].rolling(window=20).mean()
        data['ma_60'] = data['close'].rolling(window=60).mean()
        data['ma_120'] = data['close'].rolling(window=120).mean()
        
        # 均值回归中值计算
        data['mean_reversion_center'] = (data['ma_20'] + data['ma_60'] + data['ma_120']) / 3
        data['price_vs_center'] = data['close'] / data['mean_reversion_center']
        data['deviation_from_center'] = (data['close'] - data['mean_reversion_center']) / data['mean_reversion_center'] * 100
        
        # RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # Y值计算 (控股商托价概率)
        price_vs_ma20 = data['close'] / data['ma_20']
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
        
        ma_trend = (data['ma_20'] / data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        
        # 使用真实资金流强度调整Y值
        flow_adjustment = 0.05 * np.tanh((data['money_flow_intensity'] - 1))
        
        data['y_probability'] = base_y + trend_adjustment + flow_adjustment
        data['y_probability'] = np.clip(data['y_probability'], 0.1, 0.9)
        data['y_probability'].fillna(0.5, inplace=True)
        
        # X值直接使用数据库中的真实资金流比例
        data['inflow_ratio'] = data['money_flow_ratio']
        
        # 对X值进行平滑处理，减少噪音
        data['inflow_ratio_smooth'] = data['inflow_ratio'].rolling(window=3, center=True).mean()
        data['inflow_ratio_smooth'].fillna(data['inflow_ratio'], inplace=True)
        
        # 使用平滑后的X值
        data['inflow_ratio'] = data['inflow_ratio_smooth']
        
        # E值计算
        data['e_value'] = (8 * data['inflow_ratio'] * data['y_probability'] - 
                          3 * data['inflow_ratio'] - 3 * data['y_probability'] + 1)
        
        # 策略区域分类
        conditions = [
            (data['y_probability'] > self.strategy_params['high_profit_y']) & 
            (data['inflow_ratio'] > self.strategy_params['high_profit_x']),  # 高值盈利区
            
            (data['y_probability'] > self.strategy_params['control_zone_min']) & 
            (data['y_probability'] < self.strategy_params['control_zone_max']),  # 控股商控制区
            
            (data['y_probability'] < self.strategy_params['strong_loss_y']) | 
            (data['inflow_ratio'] < self.strategy_params['strong_loss_x']),  # 强亏损区
        ]
        
        choices = ['高值盈利区', '控股商控制区', '强亏损区']
        data['strategy_zone'] = np.select(conditions, choices, default='其他区域')
        
        # 统计策略区域分布
        zone_counts = data['strategy_zone'].value_counts()
        total = len(data)
        print(f"📊 基于真实资金流的策略区域分布:")
        for zone, count in zone_counts.items():
            print(f"   • {zone}: {count} 天 ({count/total*100:.1f}%)")
        
        print("✅ 增强指标计算完成 (使用真实资金流数据)")
        return data
    
    def run_database_strategy_backtest(self):
        """运行基于数据库的策略回测"""
        print("🎯 开始基于真实资金流数据的策略回测...")
        
        # 加载数据
        data = self.load_database_data()
        if data is None:
            return []
        
        # 计算指标
        data = self.calculate_enhanced_indicators(data)
        
        trades = []
        current_cash = self.initial_capital
        
        # 从第120天开始，确保指标计算完整
        start_index = 120
        trading_data = data[start_index:].copy().reset_index(drop=True)
        
        trade_count = 0
        i = 0
        
        while i < len(trading_data) - 10:
            row = trading_data.iloc[i]
            
            date = row['date']
            price = row['close']
            y_val = row['y_probability']
            x_val = row['inflow_ratio']  # 真实资金流比例
            e_val = row['e_value']
            zone = row['strategy_zone']
            real_flow_in = row['money_flow_in']
            real_flow_out = row['money_flow_out']
            flow_intensity = row['money_flow_intensity']
            
            # 跳过控股商控制区
            if zone == '控股商控制区':
                i += 1
                continue
            
            # 确定交易策略
            action = None
            direction = None
            reason = None
            take_profit = None
            stop_loss = None
            
            if zone == '高值盈利区':
                action = '买入'
                direction = '做多'
                take_profit = self.strategy_params['long_take_profit']
                stop_loss = self.strategy_params['long_stop_loss']
                reason = f'高值盈利区：Y={y_val:.3f}>0.43且X={x_val:.3f}>0.43，真实流入{real_flow_in:,.0f}，买涨策略'
            elif zone == '强亏损区':
                action = '买入'
                direction = '做多'
                take_profit = self.strategy_params['long_take_profit']
                stop_loss = self.strategy_params['long_stop_loss']
                reason = f'强亏损区：Y={y_val:.3f}<0.25或X={x_val:.3f}<0.25，真实流出{real_flow_out:,.0f}，低位反弹买涨'
            elif zone == '其他区域':
                action = '卖出'
                direction = '做空'
                take_profit = self.strategy_params['short_take_profit']
                stop_loss = self.strategy_params['short_stop_loss']
                reason = f'其他区域：Y={y_val:.3f}，X={x_val:.3f}，真实流入比例{x_val:.1%}，买跌策略'
            
            if action and current_cash > 2000:
                # 计算仓位
                position_value = current_cash * self.strategy_params['position_ratio']
                shares = int(position_value / price / 100) * 100
                actual_value = shares * price
                
                if shares >= 100:
                    # 模拟持仓期
                    holding_days = np.random.randint(3, 10)
                    exit_index = min(i + holding_days, len(trading_data) - 1)
                    exit_row = trading_data.iloc[exit_index]
                    exit_price = exit_row['close']
                    exit_date = exit_row['date']
                    
                    # 计算盈亏
                    if direction == '做多':
                        profit_pct = (exit_price - price) / price
                        if profit_pct >= take_profit:
                            exit_reason = '止盈'
                            profit_pct = take_profit
                            exit_price = price * (1 + take_profit)
                        elif profit_pct <= -stop_loss:
                            exit_reason = '止损'
                            profit_pct = -stop_loss
                            exit_price = price * (1 - stop_loss)
                        else:
                            exit_reason = '到期平仓'
                        
                        profit = profit_pct * actual_value
                    else:  # 做空
                        profit_pct = (price - exit_price) / price
                        if profit_pct >= take_profit:
                            exit_reason = '止盈'
                            profit_pct = take_profit
                            exit_price = price * (1 - take_profit)
                        elif profit_pct <= -stop_loss:
                            exit_reason = '止损'
                            profit_pct = -stop_loss
                            exit_price = price * (1 + stop_loss)
                        else:
                            exit_reason = '到期平仓'
                        
                        profit = profit_pct * actual_value
                    
                    # 交易成本
                    transaction_cost = actual_value * self.strategy_params['transaction_cost'] * 2
                    net_profit = profit - transaction_cost
                    
                    # 更新现金
                    current_cash += net_profit
                    
                    # 计算均值回归相关数据
                    mean_center = row['mean_reversion_center']
                    price_vs_center = row['price_vs_center']
                    deviation = row['deviation_from_center']
                    
                    # 记录交易
                    trade_record = {
                        '交易序号': trade_count + 1,
                        '股票代码': 'HK00023',
                        '股票名称': '东亚银行',
                        '开仓日期': date.strftime('%Y-%m-%d'),
                        '平仓日期': exit_date.strftime('%Y-%m-%d'),
                        '持仓天数': holding_days,
                        '交易方向': direction,
                        '开仓价格': round(price, 2),
                        '平仓价格': round(exit_price, 2),
                        '均值回归中值': round(mean_center, 2),
                        '价格偏离度%': round(deviation, 2),
                        '价格中值比': round(price_vs_center, 3),
                        '交易股数': shares,
                        '交易金额': round(actual_value, 0),
                        '毛利润': round(profit, 0),
                        '交易成本': round(transaction_cost, 0),
                        '净利润': round(net_profit, 0),
                        '收益率%': round(profit_pct * 100, 2),
                        'Y值': round(y_val, 3),
                        'X值(真实)': round(x_val, 3),
                        'E值': round(e_val, 3),
                        '策略区域': zone,
                        '交易理由': reason,
                        '平仓原因': exit_reason,
                        '账户余额': round(current_cash, 0),
                        '真实流入': round(real_flow_in, 0),
                        '真实流出': round(real_flow_out, 0),
                        '资金流强度': round(flow_intensity, 2),
                        'RSI': round(row['rsi'], 1)
                    }
                    
                    trades.append(trade_record)
                    trade_count += 1
                    
                    if trade_count % 50 == 0:
                        print(f"已完成 {trade_count} 笔交易，当前资金: {current_cash:,.0f}港币")
            
            # 随机间隔
            i += np.random.randint(2, 8)
        
        print(f"\n✅ 基于真实资金流的策略回测完成!")
        print(f"📊 总交易次数: {trade_count}")
        print(f"💰 最终资金: {current_cash:,.0f}港币")
        print(f"📈 总收益: {current_cash - self.initial_capital:+,.0f}港币")
        
        return trades

    def create_database_strategy_excel(self, trades):
        """创建基于数据库的策略Excel文件"""
        print("📄 创建基于真实资金流的策略Excel文件...")

        if not trades:
            return None

        # 创建交易记录DataFrame
        df_trades = pd.DataFrame(trades)

        # 计算汇总统计
        total_trades = len(df_trades)
        winning_trades = len(df_trades[df_trades['净利润'] > 0])
        losing_trades = len(df_trades[df_trades['净利润'] < 0])
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        total_profit = df_trades['净利润'].sum()
        final_capital = df_trades['账户余额'].iloc[-1] if len(df_trades) > 0 else self.initial_capital
        total_return = (final_capital / self.initial_capital - 1) * 100

        # 按策略区域分析
        zone_analysis = df_trades.groupby('策略区域').agg({
            '净利润': ['count', 'sum', 'mean'],
            '收益率%': 'mean',
            '交易方向': lambda x: x.iloc[0]
        }).round(2)
        zone_analysis.columns = ['交易次数', '总盈亏', '平均盈亏', '平均收益率%', '交易方向']
        zone_analysis = zone_analysis.reset_index()

        # 创建汇总统计
        summary_data = {
            '项目': [
                '股票代码', '股票名称', '策略版本', '数据源', '初始资金(港币)', '最终资金(港币)', '总盈亏(港币)',
                '总收益率(%)', '总交易次数', '盈利次数', '亏损次数', '胜率(%)',
                '平均每笔盈亏(港币)', '最大单笔盈利(港币)', '最大单笔亏损(港币)',
                '做多次数', '做空次数', '高值盈利区交易', '强亏损区交易', '其他区域交易',
                '止盈次数', '止损次数', '到期平仓次数', '关键优势'
            ],
            '数值': [
                'HK00023', '东亚银行', '真实资金流策略', '数据库真实资金流', self.initial_capital, final_capital, total_profit,
                round(total_return, 2), total_trades, winning_trades, losing_trades, round(win_rate, 1),
                round(total_profit/total_trades, 0) if total_trades > 0 else 0,
                df_trades['净利润'].max(), df_trades['净利润'].min(),
                len(df_trades[df_trades['交易方向'] == '做多']),
                len(df_trades[df_trades['交易方向'] == '做空']),
                len(df_trades[df_trades['策略区域'] == '高值盈利区']),
                len(df_trades[df_trades['策略区域'] == '强亏损区']),
                len(df_trades[df_trades['策略区域'] == '其他区域']),
                len(df_trades[df_trades['平仓原因'] == '止盈']),
                len(df_trades[df_trades['平仓原因'] == '止损']),
                len(df_trades[df_trades['平仓原因'] == '到期平仓']),
                '使用真实资金流数据'
            ]
        }
        summary_df = pd.DataFrame(summary_data)

        # 创建真实资金流优势说明
        advantage_explanation = {
            '优势项目': [
                '数据源质量',
                'X值准确性',
                '资金流指标',
                '策略精确度',
                '信号可靠性'
            ],
            '传统方法': [
                '基于yfinance估算资金流',
                'X值通过价量关系估算，误差较大',
                '使用技术指标近似计算',
                '策略区域分类可能不准确',
                '交易信号存在噪音'
            ],
            '数据库方法': [
                '使用真实的资金流入流出数据',
                'X值直接来自真实资金流比例，准确度高',
                '包含多种资金流强度和累积指标',
                '基于真实数据的策略区域分类更精确',
                '交易信号更可靠，减少假信号'
            ],
            '预期改善': [
                '提高策略整体表现',
                '减少因X值误差导致的错误分类',
                '更好地识别真实的资金流向',
                '提高各策略区域的准确性',
                '降低交易信号的噪音，提高胜率'
            ]
        }
        advantage_df = pd.DataFrame(advantage_explanation)

        # 创建真实资金流分析
        flow_analysis_data = []
        for _, trade in df_trades.iterrows():
            flow_analysis_data.append({
                '交易序号': trade['交易序号'],
                '开仓日期': trade['开仓日期'],
                '策略区域': trade['策略区域'],
                '交易方向': trade['交易方向'],
                'X值(真实)': trade['X值(真实)'],
                '真实流入': trade['真实流入'],
                '真实流出': trade['真实流出'],
                '资金流强度': trade['资金流强度'],
                '净利润': trade['净利润'],
                '平仓原因': trade['平仓原因']
            })

        flow_analysis_df = pd.DataFrame(flow_analysis_data)

        # 创建Excel文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"HK00023真实资金流策略_{timestamp}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 写入各个工作表
            df_trades.to_excel(writer, sheet_name='真实资金流交易记录', index=False)
            summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
            zone_analysis.to_excel(writer, sheet_name='区域分析', index=False)
            advantage_df.to_excel(writer, sheet_name='真实资金流优势', index=False)
            flow_analysis_df.to_excel(writer, sheet_name='资金流分析', index=False)

            # 设置格式
            workbook = writer.book

            # 交易记录表格式
            worksheet1 = writer.sheets['真实资金流交易记录']
            column_widths = {
                'A': 8, 'B': 10, 'C': 10, 'D': 12, 'E': 12, 'F': 8, 'G': 8, 'H': 10, 'I': 10, 'J': 12, 'K': 12, 'L': 12,
                'M': 10, 'N': 12, 'O': 10, 'P': 10, 'Q': 10, 'R': 10, 'S': 8, 'T': 8, 'U': 8, 'V': 12, 'W': 80, 'X': 12, 'Y': 12, 'Z': 12, 'AA': 12, 'AB': 12, 'AC': 8
            }
            for col, width in column_widths.items():
                worksheet1.column_dimensions[col].width = width

        print(f"✅ 真实资金流策略Excel文件已创建: {filename}")
        return filename

    def close_connection(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            print("🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 HK00023基于真实资金流数据的策略回测")
    print("="*70)
    print("💰 初始资金: 30,000港币")
    print("🎯 策略优势: 使用数据库中的真实资金流数据")
    print("📊 数据源: 本地数据库 (hk00023_20year.db) - 20年完整数据")

    # 创建策略
    strategy = HK00023DatabaseStrategy()

    # 连接数据库
    if not strategy.connect_database():
        return

    # 运行回测
    trades = strategy.run_database_strategy_backtest()

    if not trades:
        print("❌ 未能生成交易记录")
        strategy.close_connection()
        return

    # 创建Excel文件
    filename = strategy.create_database_strategy_excel(trades)

    # 关闭连接
    strategy.close_connection()

    if filename:
        print(f"\n🎉 基于真实资金流的策略回测完成!")
        print(f"📄 文件名: {filename}")
        print(f"📊 包含 {len(trades)} 条基于真实资金流的交易记录")
        print(f"💡 请打开Excel文件查看真实资金流策略结果")

if __name__ == "__main__":
    main()
