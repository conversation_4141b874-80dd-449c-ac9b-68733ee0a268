#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析凯利公式策略为什么不生效
==============================

严格按照策略执行：
- 高值盈利区 (Y>0.43, X>0.43): 买涨，止盈+2%，止损-1%
- 控股商控制区 (0.333<Y<0.4): 观望
- 强亏损区 (Y<0.25或X<0.25): 买跌，止盈+2%，止损-1%
- 其他区域: 买跌，止盈+1%，止损-2%

分析凯利公式失效原因

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np

class KellyStrategyAnalyzer:
    def __init__(self):
        """初始化凯利策略分析器"""
        self.db_config = {
            'host': '************',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
        self.connection = None
        self.initial_capital = 30000
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def analyze_each_trade_detailed(self):
        """详细分析每笔交易"""
        try:
            cursor = self.connection.cursor()
            
            # 获取所有记录
            cursor.execute("""
                SELECT 交易序号, 开仓日期, close, 平仓价格, `控制系数`, `资金流比例`, 
                       E值, 净利润, `收益率%`
                FROM test 
                ORDER BY 交易序号
            """)
            
            records = cursor.fetchall()
            
            print("📊 详细分析每笔交易的策略执行情况")
            print("="*180)
            print(f"{'序号':<4} {'日期':<12} {'Y值':<8} {'X值':<8} {'E值':<10} {'策略区域':<12} {'方向':<6} "
                  f"{'开仓价':<8} {'平仓价':<8} {'实际收益%':<10} {'策略收益%':<12} {'止盈止损':<10} {'原因分析':<30}")
            print("-" * 180)
            
            total_strategy_profit = 0
            total_actual_profit = 0
            strategy_wins = 0
            strategy_losses = 0
            
            for record in records:
                (trade_id, open_date, open_price, close_price, y_val, x_val, 
                 e_val, actual_profit, actual_return) = record
                
                # 分类策略区域
                if y_val > 0.43 and x_val > 0.43:
                    zone = '高值盈利区'
                    direction = '买涨'
                    take_profit = 2.0  # +2%
                    stop_loss = 1.0    # -1%
                elif 0.333 < y_val < 0.4:
                    zone = '控股商控制区'
                    direction = '观望'
                    take_profit = 0
                    stop_loss = 0
                elif y_val < 0.25 or x_val < 0.25:
                    zone = '强亏损区'
                    direction = '买跌'
                    take_profit = 2.0  # +2%
                    stop_loss = 1.0    # -1%
                else:
                    zone = '其他区域'
                    direction = '买跌'
                    take_profit = 1.0  # +1%
                    stop_loss = 2.0    # -2%
                
                # 计算实际价格变动
                price_change_pct = (close_price - open_price) / open_price * 100
                
                # 计算策略收益
                if direction == '观望':
                    strategy_return = 0
                    exit_reason = '观望'
                elif direction == '买涨':
                    # 做多
                    if price_change_pct >= take_profit:
                        strategy_return = take_profit
                        exit_reason = '止盈'
                    elif price_change_pct <= -stop_loss:
                        strategy_return = -stop_loss
                        exit_reason = '止损'
                    else:
                        strategy_return = price_change_pct
                        exit_reason = '到期平仓'
                else:  # 买跌
                    # 做空
                    short_return = -price_change_pct
                    if short_return >= take_profit:
                        strategy_return = take_profit
                        exit_reason = '止盈'
                    elif short_return <= -stop_loss:
                        strategy_return = -stop_loss
                        exit_reason = '止损'
                    else:
                        strategy_return = short_return
                        exit_reason = '到期平仓'
                
                # 统计
                total_actual_profit += actual_profit
                if direction != '观望':
                    if strategy_return > 0:
                        strategy_wins += 1
                    elif strategy_return < 0:
                        strategy_losses += 1
                
                # 分析原因
                if direction == '观望':
                    reason = '正确观望'
                elif direction == '买涨' and price_change_pct > 0:
                    if exit_reason == '止盈':
                        reason = '做多成功-止盈'
                    else:
                        reason = '做多盈利-未止盈'
                elif direction == '买涨' and price_change_pct < 0:
                    if exit_reason == '止损':
                        reason = '做多失败-止损'
                    else:
                        reason = '做多亏损-未止损'
                elif direction == '买跌' and price_change_pct < 0:
                    if exit_reason == '止盈':
                        reason = '做空成功-止盈'
                    else:
                        reason = '做空盈利-未止盈'
                elif direction == '买跌' and price_change_pct > 0:
                    if exit_reason == '止损':
                        reason = '做空失败-止损'
                    else:
                        reason = '做空亏损-未止损'
                else:
                    reason = '价格无变化'
                
                print(f"{trade_id:<4} {open_date:<12} {y_val:<8.3f} {x_val:<8.3f} {e_val:<10.3f} "
                      f"{zone:<12} {direction:<6} {open_price:<8.2f} {close_price:<8.2f} "
                      f"{price_change_pct:<9.2f}% {strategy_return:<11.2f}% {exit_reason:<10} {reason:<30}")
            
            print("\n" + "="*100)
            
            # 计算凯利公式参数
            total_trades = strategy_wins + strategy_losses
            if total_trades > 0:
                win_rate = strategy_wins / total_trades
                lose_rate = 1 - win_rate
                
                # 计算平均赔率
                # 这里需要根据不同区域的止盈止损计算加权平均赔率
                print(f"📊 凯利公式失效分析:")
                print(f"   • 策略总交易: {total_trades}")
                print(f"   • 策略盈利: {strategy_wins}")
                print(f"   • 策略亏损: {strategy_losses}")
                print(f"   • 策略胜率: {win_rate:.3f}")
                print(f"   • 策略败率: {lose_rate:.3f}")
                
                # 分析不同区域的赔率
                self.analyze_odds_by_zone()
                
            return True
            
        except Exception as e:
            print(f"❌ 分析交易失败: {e}")
            return False
    
    def analyze_odds_by_zone(self):
        """分析不同区域的赔率"""
        try:
            cursor = self.connection.cursor()
            
            print(f"\n📊 各策略区域详细分析:")
            print("-" * 120)
            
            zones = [
                ('高值盈利区', 'Y值 > 0.43 AND X值 > 0.43', '买涨', 2.0, 1.0),
                ('控股商控制区', 'Y值 > 0.333 AND Y值 < 0.4', '观望', 0, 0),
                ('强亏损区', '(Y值 < 0.25 OR X值 < 0.25) AND NOT (Y值 > 0.333 AND Y值 < 0.4)', '买跌', 2.0, 1.0),
                ('其他区域', 'NOT (Y值 > 0.43 AND X值 > 0.43) AND NOT (Y值 > 0.333 AND Y值 < 0.4) AND NOT (Y值 < 0.25 OR X值 < 0.25)', '买跌', 1.0, 2.0)
            ]
            
            for zone_name, condition, direction, take_profit, stop_loss in zones:
                # 构建查询条件
                if zone_name == '高值盈利区':
                    where_clause = "`控制系数` > 0.43 AND `资金流比例` > 0.43"
                elif zone_name == '控股商控制区':
                    where_clause = "`控制系数` > 0.333 AND `控制系数` < 0.4"
                elif zone_name == '强亏损区':
                    where_clause = "(`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4)"
                else:  # 其他区域
                    where_clause = """NOT (`控制系数` > 0.43 AND `资金流比例` > 0.43) 
                                     AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
                                     AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)"""
                
                cursor.execute(f"""
                    SELECT COUNT(*) as count,
                           AVG((平仓价格 - close) / close * 100) as avg_price_change
                    FROM test 
                    WHERE {where_clause}
                """)
                
                result = cursor.fetchone()
                count, avg_price_change = result
                
                if count > 0:
                    print(f"\n🎯 {zone_name} ({count}次交易):")
                    print(f"   • 策略: {direction}")
                    print(f"   • 止盈: +{take_profit}%, 止损: -{stop_loss}%")
                    print(f"   • 平均价格变动: {avg_price_change:.2f}%")
                    
                    if direction != '观望':
                        # 计算理论赔率
                        theoretical_odds = take_profit / stop_loss if stop_loss > 0 else 0
                        print(f"   • 理论赔率: {theoretical_odds:.1f}:1")
                        
                        # 分析该区域的具体表现
                        cursor.execute(f"""
                            SELECT 交易序号, close, 平仓价格, 
                                   (平仓价格 - close) / close * 100 as price_change_pct
                            FROM test 
                            WHERE {where_clause}
                            ORDER BY 交易序号
                        """)
                        
                        trades = cursor.fetchall()
                        
                        wins = 0
                        losses = 0
                        total_strategy_return = 0
                        
                        for trade_id, open_price, close_price, price_change_pct in trades:
                            if direction == '买涨':
                                # 做多
                                if price_change_pct >= take_profit:
                                    strategy_return = take_profit
                                    wins += 1
                                elif price_change_pct <= -stop_loss:
                                    strategy_return = -stop_loss
                                    losses += 1
                                else:
                                    strategy_return = price_change_pct
                                    if price_change_pct > 0:
                                        wins += 1
                                    else:
                                        losses += 1
                            else:  # 买跌
                                short_return = -price_change_pct
                                if short_return >= take_profit:
                                    strategy_return = take_profit
                                    wins += 1
                                elif short_return <= -stop_loss:
                                    strategy_return = -stop_loss
                                    losses += 1
                                else:
                                    strategy_return = short_return
                                    if short_return > 0:
                                        wins += 1
                                    else:
                                        losses += 1
                            
                            total_strategy_return += strategy_return
                        
                        actual_win_rate = wins / count if count > 0 else 0
                        avg_strategy_return = total_strategy_return / count if count > 0 else 0
                        
                        print(f"   • 实际胜率: {actual_win_rate:.3f}")
                        print(f"   • 平均策略收益: {avg_strategy_return:.2f}%")
                        
                        # 凯利公式计算
                        if theoretical_odds > 0:
                            kelly_f = (theoretical_odds * actual_win_rate - (1 - actual_win_rate)) / theoretical_odds
                            print(f"   • 该区域凯利系数: {kelly_f:.6f}")
                            
                            if kelly_f > 0:
                                print(f"   ✅ 该区域理论上有利可图")
                            else:
                                print(f"   ❌ 该区域理论上不利，凯利系数为负")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析区域赔率失败: {e}")
            return False
    
    def calculate_overall_kelly_effectiveness(self):
        """计算整体凯利公式有效性"""
        try:
            cursor = self.connection.cursor()
            
            print(f"\n🔍 凯利公式失效原因总结:")
            print("="*80)
            
            # 分析止盈止损执行情况
            cursor.execute("""
                SELECT 
                    SUM(CASE WHEN `凯利平仓原因` = '止盈' THEN 1 ELSE 0 END) as take_profits,
                    SUM(CASE WHEN `凯利平仓原因` = '止损' THEN 1 ELSE 0 END) as stop_losses,
                    SUM(CASE WHEN `凯利平仓原因` = '到期平仓' THEN 1 ELSE 0 END) as normal_closes,
                    SUM(CASE WHEN `凯利平仓原因` = '观望' THEN 1 ELSE 0 END) as observes,
                    SUM(`凯利净利润`) as total_kelly_profit,
                    AVG(`凯利胜率`) as kelly_win_rate,
                    AVG(`凯利系数`) as kelly_coefficient
                FROM test
            """)
            
            result = cursor.fetchone()
            take_profits, stop_losses, normal_closes, observes, total_kelly_profit, kelly_win_rate, kelly_coefficient = result
            
            total_actual_trades = take_profits + stop_losses + normal_closes
            
            print(f"📊 止盈止损执行统计:")
            print(f"   • 止盈次数: {take_profits} ({take_profits/total_actual_trades*100:.1f}%)")
            print(f"   • 止损次数: {stop_losses} ({stop_losses/total_actual_trades*100:.1f}%)")
            print(f"   • 正常平仓: {normal_closes} ({normal_closes/total_actual_trades*100:.1f}%)")
            print(f"   • 观望次数: {observes}")
            
            print(f"\n💰 凯利公式理论 vs 实际:")
            print(f"   • 理论胜率: {kelly_win_rate:.3f}")
            print(f"   • 理论凯利系数: {kelly_coefficient:.6f}")
            print(f"   • 实际总盈亏: {total_kelly_profit:+,.0f}港币")
            
            # 分析失效原因
            print(f"\n❌ 凯利公式失效的主要原因:")
            
            if stop_losses > take_profits:
                print(f"   1. 止损过多: {stop_losses}次止损 > {take_profits}次止盈")
                print(f"      → 实际胜率低于理论胜率")
            
            if kelly_coefficient > 0 and total_kelly_profit < 0:
                print(f"   2. 理论有利但实际亏损: 凯利系数{kelly_coefficient:.3f}>0 但亏损{total_kelly_profit}港币")
                print(f"      → 市场实际表现与历史数据不符")
            
            # 分析各区域贡献
            cursor.execute("""
                SELECT `凯利策略区域`, 
                       COUNT(*) as count,
                       SUM(`凯利净利润`) as profit,
                       AVG(`凯利净利润`) as avg_profit,
                       SUM(CASE WHEN `凯利净利润` > 0 THEN 1 ELSE 0 END) as wins
                FROM test 
                WHERE `凯利交易方向` != '观望'
                GROUP BY `凯利策略区域`
                ORDER BY profit DESC
            """)
            
            zone_results = cursor.fetchall()
            
            print(f"\n📊 各区域对亏损的贡献:")
            for zone, count, profit, avg_profit, wins in zone_results:
                win_rate = wins/count*100 if count > 0 else 0
                print(f"   • {zone}: {count}次, {profit:+,.0f}港币 (平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%)")
            
            return True
            
        except Exception as e:
            print(f"❌ 计算凯利有效性失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🔍 详细分析凯利公式策略为什么不生效")
    print("="*80)
    print("📊 策略规则:")
    print("   • 高值盈利区 (Y>0.43, X>0.43): 买涨，止盈+2%，止损-1%")
    print("   • 控股商控制区 (0.333<Y<0.4): 观望")
    print("   • 强亏损区 (Y<0.25或X<0.25): 买跌，止盈+2%，止损-1%")
    print("   • 其他区域: 买跌，止盈+1%，止损-2%")
    
    # 创建分析器
    analyzer = KellyStrategyAnalyzer()
    
    # 连接数据库
    if not analyzer.connect_database():
        return
    
    # 详细分析每笔交易
    analyzer.analyze_each_trade_detailed()
    
    # 计算整体凯利有效性
    analyzer.calculate_overall_kelly_effectiveness()
    
    # 关闭连接
    analyzer.close_connection()
    
    print(f"\n🎯 分析完成！现在您可以看到凯利公式失效的具体原因")

if __name__ == "__main__":
    main()
