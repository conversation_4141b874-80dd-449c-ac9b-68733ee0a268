#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析HK00023资金流数据库
======================

功能：
1. 查询HK00023数据库中的资金流数据
2. 分析资金流模式和趋势
3. 生成资金流报告
4. 验证资金流计算的有效性

作者: Cosmoon NG
日期: 2025年7月
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class MoneyFlowAnalyzer:
    def __init__(self, db_path="hk00023.db"):
        """初始化资金流分析器"""
        self.db_path = db_path
        self.conn = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            print(f"✅ 成功连接数据库: {self.db_path}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def load_data(self, days=None):
        """加载数据"""
        try:
            if days:
                sql = f"""
                    SELECT * FROM hk00023 
                    ORDER BY date DESC 
                    LIMIT {days}
                """
            else:
                sql = "SELECT * FROM hk00023 ORDER BY date"
            
            df = pd.read_sql_query(sql, self.conn)
            df['date'] = pd.to_datetime(df['date'])
            
            if days:
                df = df.sort_values('date')  # 重新按日期正序排列
            
            print(f"📊 加载了 {len(df)} 条记录")
            return df
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return None
    
    def analyze_money_flow_patterns(self, df):
        """分析资金流模式"""
        print("\n📈 资金流模式分析:")
        print("="*60)
        
        # 基础统计
        total_days = len(df)
        flow_in_days = len(df[df['money_flow_in'] > df['money_flow_out']])
        flow_out_days = len(df[df['money_flow_out'] > df['money_flow_in']])
        
        print(f"📊 基础统计:")
        print(f"   • 总交易日: {total_days} 天")
        print(f"   • 资金净流入日: {flow_in_days} 天 ({flow_in_days/total_days*100:.1f}%)")
        print(f"   • 资金净流出日: {flow_out_days} 天 ({flow_out_days/total_days*100:.1f}%)")
        
        # 资金流量统计
        avg_flow_in = df['money_flow_in'].mean()
        avg_flow_out = df['money_flow_out'].mean()
        avg_net_flow = df['net_money_flow'].mean()
        avg_ratio = df['money_flow_ratio'].mean()
        
        print(f"\n💰 资金流量统计:")
        print(f"   • 平均日流入: {avg_flow_in:,.0f}")
        print(f"   • 平均日流出: {avg_flow_out:,.0f}")
        print(f"   • 平均净流量: {avg_net_flow:+,.0f}")
        print(f"   • 平均流入比例: {avg_ratio:.3f}")
        
        # 极值分析
        max_flow_in = df['money_flow_in'].max()
        max_flow_out = df['money_flow_out'].max()
        max_net_positive = df['net_money_flow'].max()
        max_net_negative = df['net_money_flow'].min()
        
        max_in_date = df.loc[df['money_flow_in'].idxmax(), 'date'].strftime('%Y-%m-%d')
        max_out_date = df.loc[df['money_flow_out'].idxmax(), 'date'].strftime('%Y-%m-%d')
        max_pos_date = df.loc[df['net_money_flow'].idxmax(), 'date'].strftime('%Y-%m-%d')
        max_neg_date = df.loc[df['net_money_flow'].idxmin(), 'date'].strftime('%Y-%m-%d')
        
        print(f"\n🎯 极值分析:")
        print(f"   • 最大单日流入: {max_flow_in:,.0f} ({max_in_date})")
        print(f"   • 最大单日流出: {max_flow_out:,.0f} ({max_out_date})")
        print(f"   • 最大净流入: {max_net_positive:+,.0f} ({max_pos_date})")
        print(f"   • 最大净流出: {max_net_negative:+,.0f} ({max_neg_date})")
        
        return {
            'total_days': total_days,
            'flow_in_days': flow_in_days,
            'flow_out_days': flow_out_days,
            'avg_flow_in': avg_flow_in,
            'avg_flow_out': avg_flow_out,
            'avg_net_flow': avg_net_flow,
            'avg_ratio': avg_ratio
        }
    
    def analyze_price_flow_correlation(self, df):
        """分析价格与资金流的相关性"""
        print("\n📊 价格与资金流相关性分析:")
        print("="*60)
        
        # 计算价格变化
        df['price_change'] = df['close'].pct_change()
        df['price_change_abs'] = df['price_change'].abs()
        
        # 相关性分析
        correlations = {
            '价格变化 vs 净资金流': df['price_change'].corr(df['net_money_flow']),
            '价格变化 vs 流入比例': df['price_change'].corr(df['money_flow_ratio']),
            '价格变化幅度 vs 资金流强度': df['price_change_abs'].corr(df['money_flow_intensity']),
            '成交量 vs 总资金流': df['volume'].corr(df['money_flow_in'] + df['money_flow_out'])
        }
        
        print("🔗 相关系数:")
        for desc, corr in correlations.items():
            strength = "强" if abs(corr) > 0.7 else "中" if abs(corr) > 0.3 else "弱"
            direction = "正" if corr > 0 else "负"
            print(f"   • {desc}: {corr:.3f} ({direction}相关, {strength})")
        
        # 分析不同价格变化区间的资金流特征
        print(f"\n📈 不同价格变化区间的资金流特征:")
        
        # 定义价格变化区间
        price_ranges = [
            ('大涨 (>3%)', df['price_change'] > 0.03),
            ('小涨 (0-3%)', (df['price_change'] > 0) & (df['price_change'] <= 0.03)),
            ('小跌 (0到-3%)', (df['price_change'] < 0) & (df['price_change'] >= -0.03)),
            ('大跌 (<-3%)', df['price_change'] < -0.03)
        ]
        
        for range_name, condition in price_ranges:
            range_data = df[condition]
            if len(range_data) > 0:
                avg_ratio = range_data['money_flow_ratio'].mean()
                avg_net = range_data['net_money_flow'].mean()
                count = len(range_data)
                
                print(f"   • {range_name}: {count}天, 平均流入比例{avg_ratio:.3f}, 平均净流量{avg_net:+,.0f}")
        
        return correlations
    
    def analyze_cumulative_flow_trends(self, df):
        """分析累积资金流趋势"""
        print("\n📊 累积资金流趋势分析:")
        print("="*60)
        
        # 计算趋势指标
        start_cumulative = df['cumulative_money_flow'].iloc[0]
        end_cumulative = df['cumulative_money_flow'].iloc[-1]
        max_cumulative = df['cumulative_money_flow'].max()
        min_cumulative = df['cumulative_money_flow'].min()
        
        # 找到极值日期
        max_date = df.loc[df['cumulative_money_flow'].idxmax(), 'date'].strftime('%Y-%m-%d')
        min_date = df.loc[df['cumulative_money_flow'].idxmin(), 'date'].strftime('%Y-%m-%d')
        
        print(f"📈 累积流量趋势:")
        print(f"   • 期初累积: {start_cumulative:+,.0f}")
        print(f"   • 期末累积: {end_cumulative:+,.0f}")
        print(f"   • 净变化: {end_cumulative - start_cumulative:+,.0f}")
        print(f"   • 最高点: {max_cumulative:+,.0f} ({max_date})")
        print(f"   • 最低点: {min_cumulative:+,.0f} ({min_date})")
        print(f"   • 波动幅度: {max_cumulative - min_cumulative:,.0f}")
        
        # 分析趋势阶段
        df['cumulative_ma20'] = df['cumulative_money_flow'].rolling(20).mean()
        df['trend_direction'] = np.where(
            df['cumulative_money_flow'] > df['cumulative_ma20'], 
            '上升', '下降'
        )
        
        trend_changes = df['trend_direction'].value_counts()
        print(f"\n📊 趋势方向分布:")
        for direction, count in trend_changes.items():
            print(f"   • {direction}趋势: {count}天 ({count/len(df)*100:.1f}%)")
        
        return {
            'start_cumulative': start_cumulative,
            'end_cumulative': end_cumulative,
            'max_cumulative': max_cumulative,
            'min_cumulative': min_cumulative,
            'net_change': end_cumulative - start_cumulative
        }
    
    def generate_money_flow_signals(self, df):
        """生成资金流交易信号"""
        print("\n🎯 资金流交易信号分析:")
        print("="*60)
        
        # 定义信号条件
        strong_inflow = df['money_flow_ratio'] > 0.7
        strong_outflow = df['money_flow_ratio'] < 0.3
        high_intensity = df['money_flow_intensity'] > 1.5
        
        # 组合信号
        buy_signal = strong_inflow & high_intensity
        sell_signal = strong_outflow & high_intensity
        
        # 统计信号
        buy_days = len(df[buy_signal])
        sell_days = len(df[sell_signal])
        neutral_days = len(df) - buy_days - sell_days
        
        print(f"📊 交易信号统计:")
        print(f"   • 买入信号: {buy_days}天 ({buy_days/len(df)*100:.1f}%)")
        print(f"   • 卖出信号: {sell_days}天 ({sell_days/len(df)*100:.1f}%)")
        print(f"   • 中性信号: {neutral_days}天 ({neutral_days/len(df)*100:.1f}%)")
        
        # 分析信号效果
        if buy_days > 0:
            buy_data = df[buy_signal]
            avg_price_change_after_buy = buy_data['price_change'].shift(-1).mean()  # 次日价格变化
            print(f"   • 买入信号后平均价格变化: {avg_price_change_after_buy*100:+.2f}%")
        
        if sell_days > 0:
            sell_data = df[sell_signal]
            avg_price_change_after_sell = sell_data['price_change'].shift(-1).mean()  # 次日价格变化
            print(f"   • 卖出信号后平均价格变化: {avg_price_change_after_sell*100:+.2f}%")
        
        return {
            'buy_days': buy_days,
            'sell_days': sell_days,
            'neutral_days': neutral_days
        }
    
    def show_recent_data(self, df, days=10):
        """显示最近的数据"""
        print(f"\n📅 最近{days}天的资金流数据:")
        print("="*120)
        
        recent_data = df.tail(days)
        display_columns = ['date', 'close', 'volume', 'money_flow_in', 'money_flow_out', 
                          'money_flow_ratio', 'money_flow_intensity', 'cumulative_money_flow']
        
        # 格式化显示
        display_df = recent_data[display_columns].copy()
        display_df['date'] = display_df['date'].dt.strftime('%Y-%m-%d')
        
        # 数值格式化
        for col in ['money_flow_in', 'money_flow_out', 'cumulative_money_flow']:
            display_df[col] = display_df[col].apply(lambda x: f"{x:,.0f}")
        
        for col in ['money_flow_ratio', 'money_flow_intensity']:
            display_df[col] = display_df[col].apply(lambda x: f"{x:.3f}")
        
        print(display_df.to_string(index=False))
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 HK00023资金流数据库分析")
    print("="*60)
    
    # 创建分析器
    analyzer = MoneyFlowAnalyzer()
    
    # 连接数据库
    if not analyzer.connect_database():
        return
    
    # 加载数据
    df = analyzer.load_data()
    if df is None:
        analyzer.close_connection()
        return
    
    # 分析资金流模式
    flow_stats = analyzer.analyze_money_flow_patterns(df)
    
    # 分析价格与资金流相关性
    correlations = analyzer.analyze_price_flow_correlation(df)
    
    # 分析累积资金流趋势
    trend_stats = analyzer.analyze_cumulative_flow_trends(df)
    
    # 生成交易信号
    signal_stats = analyzer.generate_money_flow_signals(df)
    
    # 显示最近数据
    analyzer.show_recent_data(df, 10)
    
    # 关闭连接
    analyzer.close_connection()
    
    print("\n🎉 HK00023资金流分析完成!")
    print("💡 数据库包含完整的资金流数据，可用于策略优化")

if __name__ == "__main__":
    main()
