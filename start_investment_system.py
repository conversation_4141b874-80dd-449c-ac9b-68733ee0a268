#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
博弈论投资策略系统 - 主启动菜单
=================================

这是一个完整的投资策略系统启动界面，整合了所有重要的投资分析工具。

核心投资理论:
- 买入条件: X>0.4 且 Y>0.4 (资金流入比例>0.4 且 博弈论概率>0.4)
- 卖出条件: X<0.4 (资金流入比例<0.4)

作者: 博弈论投资策略团队
日期: 2025年7月
"""

import os
import sys
import subprocess
from datetime import datetime

def print_header():
    """打印系统标题"""
    print("=" * 80)
    print("🎯 博弈论投资策略系统 v2.0")
    print("=" * 80)
    print("📅 当前时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("📂 工作目录:", os.getcwd())
    print("=" * 80)

def print_menu():
    """打印主菜单"""
    print("\n🚀 主功能菜单:")
    print("-" * 50)
    
    print("\n📊 核心策略分析 (推荐新手):")
    print("1. 🏆 改进版网格+凯利策略 (年化30.60%)")
    print("2. 📈 优化版Y>0.4且X>0.4策略 (年化9.71%)")
    print("3. 🎯 HK2800 ETF策略测试 (年化46.67%)")
    print("4. 📋 Y=X策略分析")
    
    print("\n💰 投资收益计算:")
    print("5. 💵 3万元3年投资收益计算")
    print("6. 💎 10万元5年投资收益计算")
    print("7. 📊 简化版Y>X>0.4回测")
    
    print("\n🔧 系统工具:")
    print("8. 🔍 数据库状态检查")
    print("9. 🛠️ 策略优化分析")
    print("10. 🔄 数据转换工具")
    
    print("\n📋 系统信息:")
    print("11. 📄 查看重要文件清单")
    print("12. 📖 查看系统说明")
    print("13. 🚪 退出系统")
    
    print("-" * 50)

def run_script(script_name, description):
    """运行指定的Python脚本"""
    print(f"\n🚀 正在启动: {description}")
    print(f"📄 执行文件: {script_name}")
    print("-" * 50)
    
    if not os.path.exists(script_name):
        print(f"❌ 错误: 文件 {script_name} 不存在!")
        print("💡 请确保所有文件已正确复制到当前目录")
        return False
    
    try:
        # 使用subprocess运行脚本
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=False, 
                              text=True)
        
        if result.returncode == 0:
            print(f"\n✅ {description} 执行完成!")
        else:
            print(f"\n❌ {description} 执行出错!")
            
    except Exception as e:
        print(f"❌ 执行错误: {str(e)}")
        return False
    
    return True

def show_file_list():
    """显示重要文件清单"""
    print("\n📋 重要文件清单:")
    print("-" * 50)
    
    files = [
        ("improved_grid_kelly.py", "🏆 改进版网格+凯利策略 (最佳策略)"),
        ("optimized_y_x_04_backtest.py", "📈 优化版Y>0.4且X>0.4策略"),
        ("test_hk2800_strategy.py", "🎯 HK2800 ETF策略测试"),
        ("check_database_status.py", "🔍 数据库状态检查"),
        ("calculate_30k_3year_returns.py", "💵 3万元3年投资收益"),
        ("calculate_100k_5year.py", "💎 10万元5年投资收益"),
        ("strategy_optimization.py", "🛠️ 策略优化分析"),
        ("convert_to_hk2800.py", "🔄 数据转换工具"),
        ("y_equals_x_strategy.py", "📋 Y=X策略分析"),
        ("simple_y_x_04_backtest.py", "📊 简化版回测"),
    ]
    
    for filename, description in files:
        status = "✅" if os.path.exists(filename) else "❌"
        print(f"{status} {description}")
        print(f"   📄 {filename}")
    
    print("-" * 50)

def show_system_info():
    """显示系统信息"""
    print("\n📖 博弈论投资策略系统说明:")
    print("-" * 50)
    print("🎯 核心理论:")
    print("   • 买入条件: X>0.4 且 Y>0.4")
    print("   • 卖出条件: X<0.4")
    print("   • X: 资金流入比例")
    print("   • Y: 博弈论概率")
    
    print("\n🏆 最佳策略推荐:")
    print("   1. 改进版网格+凯利策略 (年化30.60%)")
    print("   2. HK2800 ETF策略 (年化46.67%)")
    print("   3. 优化版Y>X>0.4策略 (年化9.71%)")
    
    print("\n💡 使用建议:")
    print("   • 新手: 先运行策略1了解系统")
    print("   • 进阶: 使用策略2进行ETF投资")
    print("   • 保守: 使用策略3稳健投资")
    
    print("\n⚠️ 风险提示:")
    print("   • 投资有风险，入市需谨慎")
    print("   • 建议先小资金测试")
    print("   • 严格执行买卖条件")
    print("-" * 50)

def main():
    """主函数"""
    while True:
        print_header()
        print_menu()
        
        try:
            choice = input("\n请选择功能 (1-13): ").strip()
            
            if choice == "1":
                run_script("improved_grid_kelly.py", "改进版网格+凯利策略")
            elif choice == "2":
                run_script("optimized_y_x_04_backtest.py", "优化版Y>0.4且X>0.4策略")
            elif choice == "3":
                run_script("test_hk2800_strategy.py", "HK2800 ETF策略测试")
            elif choice == "4":
                run_script("y_equals_x_strategy.py", "Y=X策略分析")
            elif choice == "5":
                run_script("calculate_30k_3year_returns.py", "3万元3年投资收益计算")
            elif choice == "6":
                run_script("calculate_100k_5year.py", "10万元5年投资收益计算")
            elif choice == "7":
                run_script("simple_y_x_04_backtest.py", "简化版Y>X>0.4回测")
            elif choice == "8":
                run_script("check_database_status.py", "数据库状态检查")
            elif choice == "9":
                run_script("strategy_optimization.py", "策略优化分析")
            elif choice == "10":
                run_script("convert_to_hk2800.py", "数据转换工具")
            elif choice == "11":
                show_file_list()
            elif choice == "12":
                show_system_info()
            elif choice == "13":
                print("\n👋 感谢使用博弈论投资策略系统!")
                print("💡 投资有风险，入市需谨慎!")
                break
            else:
                print("\n❌ 无效选择，请输入1-13之间的数字")
            
            if choice not in ["11", "12", "13"]:
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"\n❌ 系统错误: {str(e)}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
