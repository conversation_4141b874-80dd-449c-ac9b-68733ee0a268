#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示真实资金流策略结果
=====================

显示基于数据库真实资金流数据的HK00023策略回测结果

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import glob
import os

def show_database_strategy_results():
    """显示真实资金流策略结果"""
    # 查找最新的真实资金流策略文件
    excel_files = glob.glob("HK00023真实资金流策略_*.xlsx")
    if not excel_files:
        print("❌ 未找到真实资金流策略文件")
        return
    
    # 获取最新文件
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"🏦 HK00023真实资金流策略: {latest_file}")
    
    try:
        # 读取所有工作表
        df_trades = pd.read_excel(latest_file, sheet_name='真实资金流交易记录')
        df_summary = pd.read_excel(latest_file, sheet_name='汇总统计')
        df_zone_analysis = pd.read_excel(latest_file, sheet_name='区域分析')
        df_advantage = pd.read_excel(latest_file, sheet_name='真实资金流优势')
        df_flow_analysis = pd.read_excel(latest_file, sheet_name='资金流分析')
        
        print(f"\n📊 HK00023真实资金流策略汇总:")
        print("="*90)
        
        # 显示关键统计
        for _, row in df_summary.iterrows():
            if pd.notna(row['数值']):
                print(f"{row['项目']}: {row['数值']}")
        
        print(f"\n📊 区域分析:")
        print("="*80)
        print(df_zone_analysis.to_string(index=False))
        
        print(f"\n📈 前15条真实资金流交易记录预览:")
        print("="*150)
        
        # 显示关键列
        preview_columns = ['交易序号', '开仓日期', '交易方向', '开仓价格', 'X值(真实)', 
                          '净利润', 'Y值', 'E值', '策略区域', '平仓原因', '真实流入', '真实流出']
        
        print(df_trades[preview_columns].head(15).to_string(index=False))
        
        print(f"\n📊 真实资金流策略详细分析:")
        print("="*80)
        
        # 按策略区域分析
        print(f"按策略区域分析:")
        for zone in df_trades['策略区域'].unique():
            zone_data = df_trades[df_trades['策略区域'] == zone]
            count = len(zone_data)
            total_profit = zone_data['净利润'].sum()
            avg_profit = zone_data['净利润'].mean()
            win_rate = len(zone_data[zone_data['净利润'] > 0]) / count * 100
            
            # 显示该区域的交易方向
            directions = zone_data['交易方向'].value_counts()
            direction_str = ", ".join([f"{dir}:{cnt}次" for dir, cnt in directions.items()])
            
            # 计算该区域的平均真实资金流
            avg_real_x = zone_data['X值(真实)'].mean()
            avg_flow_in = zone_data['真实流入'].mean()
            avg_flow_out = zone_data['真实流出'].mean()
            
            print(f"• {zone}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%, 方向({direction_str})")
            print(f"  平均真实X值: {avg_real_x:.3f}, 平均流入: {avg_flow_in:,.0f}, 平均流出: {avg_flow_out:,.0f}")
        
        # 按交易方向分析
        print(f"\n按交易方向分析:")
        for direction in df_trades['交易方向'].unique():
            dir_data = df_trades[df_trades['交易方向'] == direction]
            count = len(dir_data)
            total_profit = dir_data['净利润'].sum()
            avg_profit = dir_data['净利润'].mean()
            win_rate = len(dir_data[dir_data['净利润'] > 0]) / count * 100
            
            print(f"• {direction}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # 真实资金流vs估算资金流对比
        print(f"\n🎯 真实资金流 vs 估算资金流优势分析:")
        print("="*70)
        
        # 分析X值分布
        x_ranges = [
            ('强流入 (X>0.7)', df_trades[df_trades['X值(真实)'] > 0.7]),
            ('中等流入 (0.5<X≤0.7)', df_trades[(df_trades['X值(真实)'] > 0.5) & (df_trades['X值(真实)'] <= 0.7)]),
            ('中等流出 (0.3≤X≤0.5)', df_trades[(df_trades['X值(真实)'] >= 0.3) & (df_trades['X值(真实)'] <= 0.5)]),
            ('强流出 (X<0.3)', df_trades[df_trades['X值(真实)'] < 0.3])
        ]
        
        print(f"基于真实X值的交易分布:")
        for range_name, range_data in x_ranges:
            if len(range_data) > 0:
                count = len(range_data)
                total_profit = range_data['净利润'].sum()
                win_rate = len(range_data[range_data['净利润'] > 0]) / count * 100
                avg_x = range_data['X值(真实)'].mean()
                
                # 分析该区域的策略分布
                zone_dist = range_data['策略区域'].value_counts()
                main_zone = zone_dist.index[0] if len(zone_dist) > 0 else 'N/A'
                
                print(f"• {range_name}: {count}次, 盈亏{total_profit:+,.0f}港币, "
                      f"胜率{win_rate:.1f}%, 平均X值{avg_x:.3f}")
                print(f"  主要策略区域: {main_zone}")
        
        # E值分析
        print(f"\n按E值分析:")
        e_positive = df_trades[df_trades['E值'] > 0]
        e_negative = df_trades[df_trades['E值'] < 0]
        
        if len(e_positive) > 0:
            e_pos_profit = e_positive['净利润'].sum()
            e_pos_count = len(e_positive)
            e_pos_win_rate = len(e_positive[e_positive['净利润'] > 0]) / e_pos_count * 100
            e_pos_avg_x = e_positive['X值(真实)'].mean()
            print(f"• E>0: {e_pos_count}次, 总盈亏{e_pos_profit:+,.0f}港币, 胜率{e_pos_win_rate:.1f}%, 平均真实X值{e_pos_avg_x:.3f}")
        
        if len(e_negative) > 0:
            e_neg_profit = e_negative['净利润'].sum()
            e_neg_count = len(e_negative)
            e_neg_win_rate = len(e_negative[e_negative['净利润'] > 0]) / e_neg_count * 100
            e_neg_avg_x = e_negative['X值(真实)'].mean()
            print(f"• E<0: {e_neg_count}次, 总盈亏{e_neg_profit:+,.0f}港币, 胜率{e_neg_win_rate:.1f}%, 平均真实X值{e_neg_avg_x:.3f}")
        
        # 平仓原因分析
        print(f"\n按平仓原因分析:")
        exit_reasons = df_trades['平仓原因'].value_counts()
        for reason, count in exit_reasons.items():
            reason_data = df_trades[df_trades['平仓原因'] == reason]
            total_profit = reason_data['净利润'].sum()
            avg_profit = reason_data['净利润'].mean()
            win_rate = len(reason_data[reason_data['净利润'] > 0]) / count * 100
            print(f"• {reason}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # 资金流强度分析
        print(f"\n📊 资金流强度分析:")
        print("="*60)
        
        # 按资金流强度分类
        intensity_ranges = [
            ('高强度 (>2.0)', df_trades[df_trades['资金流强度'] > 2.0]),
            ('中强度 (1.0-2.0)', df_trades[(df_trades['资金流强度'] >= 1.0) & (df_trades['资金流强度'] <= 2.0)]),
            ('低强度 (<1.0)', df_trades[df_trades['资金流强度'] < 1.0])
        ]
        
        for range_name, range_data in intensity_ranges:
            if len(range_data) > 0:
                count = len(range_data)
                total_profit = range_data['净利润'].sum()
                win_rate = len(range_data[range_data['净利润'] > 0]) / count * 100
                avg_intensity = range_data['资金流强度'].mean()
                
                print(f"• {range_name}: {count}次, 盈亏{total_profit:+,.0f}港币, "
                      f"胜率{win_rate:.1f}%, 平均强度{avg_intensity:.2f}")
        
        # 最佳和最差交易分析
        print(f"\n🎯 最佳和最差交易分析:")
        print("="*60)
        
        best_trade = df_trades.loc[df_trades['净利润'].idxmax()]
        worst_trade = df_trades.loc[df_trades['净利润'].idxmin()]
        
        print(f"最佳交易:")
        print(f"• 交易{best_trade['交易序号']}: {best_trade['开仓日期']}, {best_trade['交易方向']}")
        print(f"  盈利{best_trade['净利润']:+.0f}港币, {best_trade['策略区域']}")
        print(f"  Y={best_trade['Y值']:.3f}, 真实X={best_trade['X值(真实)']:.3f}, E={best_trade['E值']:.3f}")
        print(f"  真实流入{best_trade['真实流入']:,.0f}, 真实流出{best_trade['真实流出']:,.0f}")
        print(f"  资金流强度{best_trade['资金流强度']:.2f}, 平仓原因: {best_trade['平仓原因']}")
        
        print(f"\n最差交易:")
        print(f"• 交易{worst_trade['交易序号']}: {worst_trade['开仓日期']}, {worst_trade['交易方向']}")
        print(f"  亏损{worst_trade['净利润']:+.0f}港币, {worst_trade['策略区域']}")
        print(f"  Y={worst_trade['Y值']:.3f}, 真实X={worst_trade['X值(真实)']:.3f}, E={worst_trade['E值']:.3f}")
        print(f"  真实流入{worst_trade['真实流入']:,.0f}, 真实流出{worst_trade['真实流出']:,.0f}")
        print(f"  资金流强度{worst_trade['资金流强度']:.2f}, 平仓原因: {worst_trade['平仓原因']}")
        
        # 真实资金流策略总结
        print(f"\n📊 真实资金流策略总结:")
        print("="*70)
        
        final_capital = df_trades['账户余额'].iloc[-1]
        total_return = (final_capital / 30000 - 1) * 100
        total_profit = df_trades['净利润'].sum()
        win_rate = len(df_trades[df_trades['净利润'] > 0]) / len(df_trades) * 100
        
        print(f"• 策略版本: 基于真实资金流数据")
        print(f"• 数据源: 本地数据库 (hk00023.db)")
        print(f"• 总收益率: {total_return:+.2f}%")
        print(f"• 总盈亏: {total_profit:+,.0f}港币")
        print(f"• 总交易次数: {len(df_trades)}次")
        print(f"• 胜率: {win_rate:.1f}%")
        print(f"• 最大单笔盈利: {df_trades['净利润'].max():+.0f}港币")
        print(f"• 最大单笔亏损: {df_trades['净利润'].min():+.0f}港币")
        print(f"• 平均每笔盈亏: {total_profit/len(df_trades):+.0f}港币")
        
        # 真实资金流数据统计
        print(f"\n📈 真实资金流数据统计:")
        print("="*60)
        
        avg_real_x = df_trades['X值(真实)'].mean()
        avg_flow_in = df_trades['真实流入'].mean()
        avg_flow_out = df_trades['真实流出'].mean()
        avg_intensity = df_trades['资金流强度'].mean()
        
        print(f"• 平均真实X值: {avg_real_x:.3f}")
        print(f"• 平均真实流入: {avg_flow_in:,.0f}")
        print(f"• 平均真实流出: {avg_flow_out:,.0f}")
        print(f"• 平均资金流强度: {avg_intensity:.2f}")
        
        # X值分布统计
        x_high = len(df_trades[df_trades['X值(真实)'] > 0.5])
        x_low = len(df_trades[df_trades['X值(真实)'] <= 0.5])
        print(f"• 真实净流入交易: {x_high}次 ({x_high/len(df_trades)*100:.1f}%)")
        print(f"• 真实净流出交易: {x_low}次 ({x_low/len(df_trades)*100:.1f}%)")
        
        print(f"\n💡 真实资金流策略Excel文件结构:")
        print("="*70)
        print(f"📊 工作表1: 真实资金流交易记录")
        print(f"   • {len(df_trades)}条详细交易记录")
        print(f"   • 包含真实流入、流出、强度数据")
        
        print(f"\n📈 工作表2: 汇总统计")
        print(f"   • 完整的财务统计")
        print(f"   • 真实资金流数据源标识")
        
        print(f"\n📊 工作表3: 区域分析")
        print(f"   • 各策略区域详细表现")
        print(f"   • 基于真实资金流的区域分类")
        
        print(f"\n🎯 工作表4: 真实资金流优势")
        print(f"   • 传统方法vs数据库方法对比")
        print(f"   • 真实资金流的优势说明")
        
        print(f"\n📋 工作表5: 资金流分析")
        print(f"   • 详细的资金流数据分析")
        print(f"   • 真实流入流出统计")
        
        print(f"\n🎉 真实资金流策略关键发现:")
        print("="*60)
        
        # 找出表现最好和最差的区域
        best_zone = df_zone_analysis.loc[df_zone_analysis['总盈亏'].idxmax()]
        worst_zone = df_zone_analysis.loc[df_zone_analysis['总盈亏'].idxmin()]
        
        print(f"✅ 最佳区域: {best_zone['策略区域']}")
        print(f"   {best_zone['交易次数']}次{best_zone['交易方向']}，盈利{best_zone['总盈亏']:+.0f}港币")
        
        print(f"❌ 最差区域: {worst_zone['策略区域']}")
        print(f"   {worst_zone['交易次数']}次{worst_zone['交易方向']}，亏损{worst_zone['总盈亏']:+.0f}港币")
        
        print(f"💡 真实资金流优势: 提供更准确的X值，减少估算误差")
        print(f"🎯 数据质量: 基于真实交易数据，信号更可靠")
        print(f"📊 策略精度: 区域分类更准确，但整体仍需优化")
        
        # 显示真实资金流优势
        print(f"\n📋 真实资金流优势详细:")
        print("="*70)
        print(df_advantage.to_string(index=False))
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")

if __name__ == "__main__":
    show_database_strategy_results()
