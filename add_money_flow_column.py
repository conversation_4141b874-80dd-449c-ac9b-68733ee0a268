#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为HK00023数据库添加实时资金流列
==============================

功能：
1. 连接到HK00023数据库
2. 添加实时资金流入流出列
3. 计算并更新资金流数据
4. 提供多种资金流计算方法

作者: Cosmoon NG
日期: 2025年7月
"""

import sqlite3
import pandas as pd
import yfinance as yf
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class MoneyFlowCalculator:
    def __init__(self, db_path="hk00023.db"):
        """初始化资金流计算器"""
        self.db_path = db_path
        self.conn = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            print(f"✅ 成功连接数据库: {self.db_path}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def check_existing_columns(self):
        """检查现有列结构"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("PRAGMA table_info(hk00023)")
            columns = cursor.fetchall()
            
            print("📊 当前数据库列结构:")
            for col in columns:
                print(f"   • {col[1]} ({col[2]})")
            
            # 检查是否已有资金流列
            existing_cols = [col[1] for col in columns]
            money_flow_cols = ['money_flow_in', 'money_flow_out', 'net_money_flow', 'money_flow_ratio']
            
            return existing_cols, money_flow_cols
            
        except Exception as e:
            print(f"❌ 检查列结构失败: {e}")
            return [], []
    
    def add_money_flow_columns(self):
        """添加资金流相关列"""
        try:
            cursor = self.conn.cursor()
            
            # 添加资金流入列
            cursor.execute("""
                ALTER TABLE hk00023 
                ADD COLUMN money_flow_in REAL DEFAULT 0
            """)
            
            # 添加资金流出列
            cursor.execute("""
                ALTER TABLE hk00023 
                ADD COLUMN money_flow_out REAL DEFAULT 0
            """)
            
            # 添加净资金流列
            cursor.execute("""
                ALTER TABLE hk00023 
                ADD COLUMN net_money_flow REAL DEFAULT 0
            """)
            
            # 添加资金流比例列
            cursor.execute("""
                ALTER TABLE hk00023 
                ADD COLUMN money_flow_ratio REAL DEFAULT 0.5
            """)
            
            # 添加资金流强度列
            cursor.execute("""
                ALTER TABLE hk00023 
                ADD COLUMN money_flow_intensity REAL DEFAULT 0
            """)
            
            # 添加累积资金流列
            cursor.execute("""
                ALTER TABLE hk00023 
                ADD COLUMN cumulative_money_flow REAL DEFAULT 0
            """)
            
            self.conn.commit()
            print("✅ 成功添加资金流相关列")
            return True
            
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("⚠️ 资金流列已存在，跳过添加")
                return True
            else:
                print(f"❌ 添加列失败: {e}")
                return False
        except Exception as e:
            print(f"❌ 添加列失败: {e}")
            return False
    
    def calculate_money_flow_methods(self, data):
        """计算多种资金流方法"""
        print("🧮 计算多种资金流指标...")
        
        # 方法1: 基于价量关系的资金流
        def method1_price_volume_flow(data):
            """基于价格变化和成交量的资金流"""
            price_change = (data['close'] - data['open']) / data['open']
            
            # 上涨时为流入，下跌时为流出
            flow_in = np.where(price_change > 0, data['volume'] * price_change, 0)
            flow_out = np.where(price_change < 0, data['volume'] * abs(price_change), 0)
            
            return flow_in, flow_out
        
        # 方法2: 基于典型价格的资金流 (Money Flow Index方法)
        def method2_typical_price_flow(data):
            """基于典型价格的资金流"""
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            raw_money_flow = typical_price * data['volume']
            
            # 比较典型价格确定流向
            price_up = typical_price > typical_price.shift(1)
            
            flow_in = np.where(price_up, raw_money_flow, 0)
            flow_out = np.where(~price_up, raw_money_flow, 0)
            
            return flow_in, flow_out
        
        # 方法3: 基于收盘价位置的资金流 (A/D Line方法)
        def method3_close_position_flow(data):
            """基于收盘价在当日区间位置的资金流"""
            # 计算收盘价在当日高低价区间的位置
            close_location_value = ((data['close'] - data['low']) - (data['high'] - data['close'])) / (data['high'] - data['low'])
            close_location_value = close_location_value.fillna(0)
            
            # 基于位置计算资金流
            money_flow_multiplier = close_location_value * data['volume']
            
            flow_in = np.where(money_flow_multiplier > 0, money_flow_multiplier, 0)
            flow_out = np.where(money_flow_multiplier < 0, abs(money_flow_multiplier), 0)
            
            return flow_in, flow_out
        
        # 方法4: 综合资金流 (结合多种方法)
        def method4_combined_flow(data):
            """综合多种方法的资金流"""
            flow_in_1, flow_out_1 = method1_price_volume_flow(data)
            flow_in_2, flow_out_2 = method2_typical_price_flow(data)
            flow_in_3, flow_out_3 = method3_close_position_flow(data)
            
            # 加权平均
            flow_in = (flow_in_1 * 0.4 + flow_in_2 * 0.3 + flow_in_3 * 0.3)
            flow_out = (flow_out_1 * 0.4 + flow_out_2 * 0.3 + flow_out_3 * 0.3)
            
            return flow_in, flow_out
        
        # 使用综合方法计算
        flow_in, flow_out = method4_combined_flow(data)
        
        # 计算净资金流
        net_flow = flow_in - flow_out
        
        # 计算资金流比例 (流入/(流入+流出))
        total_flow = flow_in + flow_out
        flow_ratio = np.where(total_flow > 0, flow_in / total_flow, 0.5)
        
        # 计算资金流强度
        flow_intensity = total_flow / data['volume'].rolling(20).mean()
        flow_intensity = flow_intensity.fillna(1.0)
        
        # 计算累积资金流
        cumulative_flow = net_flow.cumsum()
        
        return {
            'money_flow_in': flow_in,
            'money_flow_out': flow_out,
            'net_money_flow': net_flow,
            'money_flow_ratio': flow_ratio,
            'money_flow_intensity': flow_intensity,
            'cumulative_money_flow': cumulative_flow
        }
    
    def load_and_update_data(self):
        """加载数据并更新资金流"""
        try:
            # 从数据库读取现有数据
            df = pd.read_sql_query("SELECT * FROM hk00023 ORDER BY date", self.conn)
            print(f"📊 读取到 {len(df)} 条历史数据")
            
            if len(df) == 0:
                print("❌ 数据库中没有数据")
                return False
            
            # 确保数据类型正确
            df['date'] = pd.to_datetime(df['date'])
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 计算资金流
            money_flow_data = self.calculate_money_flow_methods(df)
            
            # 更新DataFrame
            for col, values in money_flow_data.items():
                df[col] = values
            
            # 处理无穷大和NaN值
            for col in money_flow_data.keys():
                df[col] = df[col].replace([np.inf, -np.inf], 0)
                df[col] = df[col].fillna(0)
            
            print("✅ 资金流计算完成")
            return df
            
        except Exception as e:
            print(f"❌ 加载和计算数据失败: {e}")
            return None
    
    def update_database(self, df):
        """更新数据库中的资金流数据"""
        try:
            print("💾 更新数据库中的资金流数据...")
            
            # 准备更新语句
            update_sql = """
                UPDATE hk00023 
                SET money_flow_in = ?,
                    money_flow_out = ?,
                    net_money_flow = ?,
                    money_flow_ratio = ?,
                    money_flow_intensity = ?,
                    cumulative_money_flow = ?
                WHERE date = ?
            """
            
            # 准备数据
            update_data = []
            for _, row in df.iterrows():
                update_data.append((
                    float(row['money_flow_in']),
                    float(row['money_flow_out']),
                    float(row['net_money_flow']),
                    float(row['money_flow_ratio']),
                    float(row['money_flow_intensity']),
                    float(row['cumulative_money_flow']),
                    row['date'].strftime('%Y-%m-%d')
                ))
            
            # 批量更新
            cursor = self.conn.cursor()
            cursor.executemany(update_sql, update_data)
            self.conn.commit()
            
            print(f"✅ 成功更新 {len(update_data)} 条记录的资金流数据")
            return True
            
        except Exception as e:
            print(f"❌ 更新数据库失败: {e}")
            return False
    
    def verify_update(self):
        """验证更新结果"""
        try:
            # 查询更新后的数据
            df = pd.read_sql_query("""
                SELECT date, close, volume, money_flow_in, money_flow_out, 
                       net_money_flow, money_flow_ratio, money_flow_intensity,
                       cumulative_money_flow
                FROM hk00023 
                ORDER BY date DESC 
                LIMIT 10
            """, self.conn)
            
            print("\n📊 最新10条记录的资金流数据:")
            print("="*120)
            print(df.to_string(index=False))
            
            # 统计信息
            total_records = pd.read_sql_query("SELECT COUNT(*) as count FROM hk00023", self.conn).iloc[0]['count']
            non_zero_flow = pd.read_sql_query("""
                SELECT COUNT(*) as count FROM hk00023 
                WHERE money_flow_in > 0 OR money_flow_out > 0
            """, self.conn).iloc[0]['count']
            
            print(f"\n📈 资金流数据统计:")
            print(f"   • 总记录数: {total_records}")
            print(f"   • 有资金流数据的记录: {non_zero_flow}")
            print(f"   • 数据完整率: {non_zero_flow/total_records*100:.1f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证更新失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            print("🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 HK00023数据库添加实时资金流列")
    print("="*60)
    
    # 创建资金流计算器
    calculator = MoneyFlowCalculator()
    
    # 连接数据库
    if not calculator.connect_database():
        return
    
    # 检查现有列
    existing_cols, money_flow_cols = calculator.check_existing_columns()
    
    # 添加资金流列
    if not calculator.add_money_flow_columns():
        calculator.close_connection()
        return
    
    # 加载数据并计算资金流
    df = calculator.load_and_update_data()
    if df is None:
        calculator.close_connection()
        return
    
    # 更新数据库
    if not calculator.update_database(df):
        calculator.close_connection()
        return
    
    # 验证更新结果
    calculator.verify_update()
    
    # 关闭连接
    calculator.close_connection()
    
    print("\n🎉 HK00023数据库资金流列添加完成!")
    print("📊 新增列:")
    print("   • money_flow_in: 资金流入")
    print("   • money_flow_out: 资金流出")
    print("   • net_money_flow: 净资金流")
    print("   • money_flow_ratio: 资金流入比例")
    print("   • money_flow_intensity: 资金流强度")
    print("   • cumulative_money_flow: 累积资金流")

if __name__ == "__main__":
    main()
