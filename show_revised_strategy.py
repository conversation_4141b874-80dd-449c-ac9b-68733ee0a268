#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示修订策略结果
===============

显示HK00023修订策略的回测结果
强亏损区改回买涨，其他区域止盈1%止损2%

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import glob
import os

def show_revised_strategy():
    """显示修订策略结果"""
    # 查找最新的修订策略文件
    excel_files = glob.glob("HK00023修订策略交易记录_*.xlsx")
    if not excel_files:
        print("❌ 未找到修订策略文件")
        return
    
    # 获取最新文件
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"🏦 HK00023修订策略交易记录: {latest_file}")
    
    try:
        # 读取所有工作表
        df_trades = pd.read_excel(latest_file, sheet_name='修订策略交易记录')
        df_summary = pd.read_excel(latest_file, sheet_name='汇总统计')
        df_strategy = pd.read_excel(latest_file, sheet_name='修订策略说明')
        df_mean_reversion = pd.read_excel(latest_file, sheet_name='均值回归分析')
        
        print(f"\n📊 HK00023修订策略汇总 (强亏损区改回买涨):")
        print("="*90)
        
        # 显示关键统计
        for _, row in df_summary.iterrows():
            if pd.notna(row['数值']):
                print(f"{row['项目']}: {row['数值']}")
        
        print(f"\n📈 前10条修订策略交易记录预览:")
        print("="*150)
        
        # 显示关键列
        preview_columns = ['交易序号', '开仓日期', '交易方向', '开仓价格', '均值回归中值', 
                          '价格偏离度%', '净利润', 'Y值', 'X值', 'E值', '策略区域', '平仓原因']
        
        print(df_trades[preview_columns].head(10).to_string(index=False))
        
        print(f"\n📊 修订策略详细分析:")
        print("="*80)
        
        # 按策略区域分析
        print(f"按策略区域分析 (强亏损区已改回买涨):")
        for zone in df_trades['策略区域'].unique():
            zone_data = df_trades[df_trades['策略区域'] == zone]
            count = len(zone_data)
            total_profit = zone_data['净利润'].sum()
            avg_profit = zone_data['净利润'].mean()
            win_rate = len(zone_data[zone_data['净利润'] > 0]) / count * 100
            
            # 显示该区域的交易方向
            directions = zone_data['交易方向'].value_counts()
            direction_str = ", ".join([f"{dir}:{cnt}次" for dir, cnt in directions.items()])
            
            print(f"• {zone}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%, 方向({direction_str})")
        
        # 按交易方向分析
        print(f"\n按交易方向分析:")
        for direction in df_trades['交易方向'].unique():
            dir_data = df_trades[df_trades['交易方向'] == direction]
            count = len(dir_data)
            total_profit = dir_data['净利润'].sum()
            avg_profit = dir_data['净利润'].mean()
            win_rate = len(dir_data[dir_data['净利润'] > 0]) / count * 100
            
            print(f"• {direction}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # E值分析
        print(f"\n按E值分析:")
        e_positive = df_trades[df_trades['E值'] > 0]
        e_negative = df_trades[df_trades['E值'] < 0]
        
        if len(e_positive) > 0:
            e_pos_profit = e_positive['净利润'].sum()
            e_pos_count = len(e_positive)
            e_pos_win_rate = len(e_positive[e_positive['净利润'] > 0]) / e_pos_count * 100
            print(f"• E>0: {e_pos_count}次, 总盈亏{e_pos_profit:+,.0f}港币, 胜率{e_pos_win_rate:.1f}%")
        
        if len(e_negative) > 0:
            e_neg_profit = e_negative['净利润'].sum()
            e_neg_count = len(e_negative)
            e_neg_win_rate = len(e_negative[e_negative['净利润'] > 0]) / e_neg_count * 100
            print(f"• E<0: {e_neg_count}次, 总盈亏{e_neg_profit:+,.0f}港币, 胜率{e_neg_win_rate:.1f}%")
        
        # 强亏损区买涨效果分析
        print(f"\n🎯 强亏损区买涨效果分析:")
        print("="*60)
        strong_loss_trades = df_trades[df_trades['策略区域'] == '强亏损区']
        if len(strong_loss_trades) > 0:
            sl_count = len(strong_loss_trades)
            sl_profit = strong_loss_trades['净利润'].sum()
            sl_avg = strong_loss_trades['净利润'].mean()
            sl_win_rate = len(strong_loss_trades[strong_loss_trades['净利润'] > 0]) / sl_count * 100
            sl_directions = strong_loss_trades['交易方向'].value_counts()
            
            print(f"• 强亏损区总交易: {sl_count}次")
            print(f"• 交易方向分布: {dict(sl_directions)}")
            print(f"• 总盈亏: {sl_profit:+,.0f}港币")
            print(f"• 平均盈亏: {sl_avg:+.0f}港币")
            print(f"• 胜率: {sl_win_rate:.1f}%")
            
            # 分析强亏损区的均值回归表现
            sl_avg_deviation = strong_loss_trades['价格偏离度%'].mean()
            sl_avg_ratio = strong_loss_trades['价格中值比'].mean()
            print(f"• 平均价格偏离度: {sl_avg_deviation:+.1f}%")
            print(f"• 平均价格中值比: {sl_avg_ratio:.3f}")
        
        # 其他区域做空效果分析
        print(f"\n🎯 其他区域做空效果分析 (止盈1%止损2%):")
        print("="*60)
        other_trades = df_trades[df_trades['策略区域'] == '其他区域']
        if len(other_trades) > 0:
            ot_count = len(other_trades)
            ot_profit = other_trades['净利润'].sum()
            ot_avg = other_trades['净利润'].mean()
            ot_win_rate = len(other_trades[other_trades['净利润'] > 0]) / ot_count * 100
            
            print(f"• 其他区域总交易: {ot_count}次")
            print(f"• 总盈亏: {ot_profit:+,.0f}港币")
            print(f"• 平均盈亏: {ot_avg:+.0f}港币")
            print(f"• 胜率: {ot_win_rate:.1f}%")
            
            # 分析止盈止损效果
            ot_take_profit = other_trades[other_trades['平仓原因'] == '止盈']
            ot_stop_loss = other_trades[other_trades['平仓原因'] == '止损']
            ot_expire = other_trades[other_trades['平仓原因'] == '到期平仓']
            
            if len(ot_take_profit) > 0:
                print(f"• 止盈交易: {len(ot_take_profit)}次, 盈利{ot_take_profit['净利润'].sum():+,.0f}港币")
            if len(ot_stop_loss) > 0:
                print(f"• 止损交易: {len(ot_stop_loss)}次, 亏损{ot_stop_loss['净利润'].sum():+,.0f}港币")
            if len(ot_expire) > 0:
                print(f"• 到期平仓: {len(ot_expire)}次, 盈亏{ot_expire['净利润'].sum():+,.0f}港币")
        
        # 平仓原因分析
        print(f"\n按平仓原因分析:")
        exit_reasons = df_trades['平仓原因'].value_counts()
        for reason, count in exit_reasons.items():
            reason_data = df_trades[df_trades['平仓原因'] == reason]
            total_profit = reason_data['净利润'].sum()
            avg_profit = reason_data['净利润'].mean()
            win_rate = len(reason_data[reason_data['净利润'] > 0]) / count * 100
            print(f"• {reason}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # 策略版本对比
        print(f"\n🔄 策略版本对比:")
        print("="*70)
        print(f"📊 策略演进历程:")
        print(f"   1. 原版策略: 强亏损区买涨，其他区域买跌(止盈2%止损1%)")
        print(f"   2. 最终策略: 强亏损区买跌，其他区域买跌(止盈2%止损1%)")
        print(f"   3. 修订策略: 强亏损区买涨，其他区域买跌(止盈1%止损2%) ✅")
        print(f"   4. 理论依据: 强亏损区低位反弹，其他区域保守做空")
        
        # 均值回归与博弈论结合分析
        print(f"\n📈 均值回归与博弈论结合分析:")
        print("="*70)
        
        # 分析不同偏离度下的策略效果
        deviation_ranges = [
            ('高估区 (>5%)', df_trades[df_trades['价格偏离度%'] > 5]),
            ('合理区 (-5%到5%)', df_trades[(df_trades['价格偏离度%'] >= -5) & (df_trades['价格偏离度%'] <= 5)]),
            ('低估区 (<-5%)', df_trades[df_trades['价格偏离度%'] < -5])
        ]
        
        for range_name, range_data in deviation_ranges:
            if len(range_data) > 0:
                count = len(range_data)
                total_profit = range_data['净利润'].sum()
                win_rate = len(range_data[range_data['净利润'] > 0]) / count * 100
                
                # 分析该区域的策略分布
                zone_dist = range_data['策略区域'].value_counts()
                direction_dist = range_data['交易方向'].value_counts()
                
                print(f"• {range_name}: {count}次, 盈亏{total_profit:+,.0f}港币, 胜率{win_rate:.1f}%")
                print(f"  区域分布: {dict(zone_dist)}")
                print(f"  方向分布: {dict(direction_dist)}")
        
        # 最佳和最差交易分析
        print(f"\n🎯 最佳和最差交易分析:")
        print("="*60)
        
        best_trade = df_trades.loc[df_trades['净利润'].idxmax()]
        worst_trade = df_trades.loc[df_trades['净利润'].idxmin()]
        
        print(f"最佳交易:")
        print(f"• 交易{best_trade['交易序号']}: {best_trade['交易方向']}, "
              f"盈利{best_trade['净利润']:+.0f}港币, {best_trade['策略区域']}")
        print(f"  Y={best_trade['Y值']:.3f}, X={best_trade['X值']:.3f}, E={best_trade['E值']:.3f}")
        print(f"  偏离度{best_trade['价格偏离度%']:+.1f}%, 平仓原因: {best_trade['平仓原因']}")
        
        print(f"\n最差交易:")
        print(f"• 交易{worst_trade['交易序号']}: {worst_trade['交易方向']}, "
              f"亏损{worst_trade['净利润']:+.0f}港币, {worst_trade['策略区域']}")
        print(f"  Y={worst_trade['Y值']:.3f}, X={worst_trade['X值']:.3f}, E={worst_trade['E值']:.3f}")
        print(f"  偏离度{worst_trade['价格偏离度%']:+.1f}%, 平仓原因: {worst_trade['平仓原因']}")
        
        print(f"\n💡 修订策略Excel文件结构:")
        print("="*70)
        print(f"📊 工作表1: 修订策略交易记录")
        print(f"   • 100条详细交易记录")
        print(f"   • 强亏损区改回买涨策略")
        print(f"   • 其他区域止盈1%止损2%")
        print(f"   • 包含均值回归和博弈论指标")
        
        print(f"\n📈 工作表2: 汇总统计")
        print(f"   • 完整的财务和交易统计")
        print(f"   • 做多做空分布统计")
        
        print(f"\n🎯 工作表3: 修订策略说明")
        print(f"   • 强亏损区买涨策略详解")
        print(f"   • 其他区域保守做空策略")
        print(f"   • 各区域操作指引")
        
        print(f"\n📋 工作表4: 均值回归分析")
        print(f"   • 均值回归理论应用")
        print(f"   • 价格偏离度分析")
        
        print(f"\n🎉 修订策略特点:")
        print(f"✅ 强亏损区改回买涨，捕捉低位反弹")
        print(f"✅ 其他区域保守做空，止盈1%止损2%")
        print(f"✅ 博弈论与均值回归理论结合")
        print(f"✅ 包含完整的100条交易记录")
        print(f"✅ 详细的策略说明和分析指导")
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")

if __name__ == "__main__":
    show_revised_strategy()
