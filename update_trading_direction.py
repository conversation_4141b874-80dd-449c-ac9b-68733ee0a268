#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据X、Y值更新test表的交易方向
============================

策略规则：
- 高值盈利区 (Y>0.43, X>0.43): 买涨
- 控股商控制区 (0.333<Y<0.4): 观望
- 强亏损区 (Y<0.25或X<0.25): 买跌
- 其他区域: 买跌

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

def update_trading_direction():
    """根据X、Y值更新交易方向"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("📊 根据X、Y值更新test表的交易方向")
        print("="*60)
        print("🔍 策略规则:")
        print("   • 高值盈利区 (Y>0.43, X>0.43): 买涨")
        print("   • 控股商控制区 (0.333<Y<0.4): 观望")
        print("   • 强亏损区 (Y<0.25或X<0.25): 买跌")
        print("   • 其他区域: 买跌")
        print("="*60)
        
        # 1. 更新高值盈利区为买涨
        print("\n1️⃣ 更新高值盈利区为买涨...")
        cursor.execute("""
            UPDATE test 
            SET 交易方向 = '买涨'
            WHERE `控制系数` > 0.43 AND `资金流比例` > 0.43
        """)
        high_profit_rows = cursor.rowcount
        print(f"✅ 更新了 {high_profit_rows} 条高值盈利区记录为买涨")
        
        # 2. 更新控股商控制区为观望
        print("\n2️⃣ 更新控股商控制区为观望...")
        cursor.execute("""
            UPDATE test 
            SET 交易方向 = '观望'
            WHERE `控制系数` > 0.333 AND `控制系数` < 0.4
        """)
        control_rows = cursor.rowcount
        print(f"✅ 更新了 {control_rows} 条控股商控制区记录为观望")
        
        # 3. 更新强亏损区为买跌
        print("\n3️⃣ 更新强亏损区为买跌...")
        cursor.execute("""
            UPDATE test 
            SET 交易方向 = '买跌'
            WHERE (`控制系数` < 0.25 OR `资金流比例` < 0.25) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4)
        """)
        strong_loss_rows = cursor.rowcount
        print(f"✅ 更新了 {strong_loss_rows} 条强亏损区记录为买跌")
        
        # 4. 更新其他区域为买跌
        print("\n4️⃣ 更新其他区域为买跌...")
        cursor.execute("""
            UPDATE test 
            SET 交易方向 = '买跌'
            WHERE NOT (`控制系数` > 0.43 AND `资金流比例` > 0.43) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
              AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)
        """)
        other_rows = cursor.rowcount
        print(f"✅ 更新了 {other_rows} 条其他区域记录为买跌")
        
        # 提交事务
        connection.commit()
        
        # 5. 验证更新结果
        print("\n5️⃣ 验证交易方向更新结果...")
        cursor.execute("""
            SELECT 交易序号, `控制系数`, `资金流比例`, 交易方向,
                   CASE 
                       WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                       ELSE '其他区域'
                   END AS 策略区域
            FROM test 
            ORDER BY 交易序号 
            LIMIT 20
        """)
        
        results = cursor.fetchall()
        
        print(f"\n📊 交易方向验证 (前20条记录):")
        print("-" * 80)
        print(f"{'序号':<4} {'Y值(控制系数)':<12} {'X值(资金流比例)':<14} {'策略区域':<12} {'交易方向':<8}")
        print("-" * 80)
        
        for record in results:
            trade_id, y_val, x_val, direction, strategy = record
            print(f"{trade_id:<4} {float(y_val):<12.3f} {float(x_val):<14.3f} {strategy:<12} {direction:<8}")
        
        # 6. 统计各交易方向数量
        print("\n6️⃣ 统计各交易方向数量...")
        cursor.execute("""
            SELECT 交易方向, 
                   COUNT(*) as 数量,
                   ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM test), 1) as 百分比
            FROM test 
            GROUP BY 交易方向
            ORDER BY 数量 DESC
        """)
        
        direction_stats = cursor.fetchall()
        
        print(f"\n📈 交易方向统计:")
        print("-" * 40)
        print(f"{'交易方向':<8} {'数量':<6} {'百分比':<8}")
        print("-" * 40)
        
        for direction, count, percentage in direction_stats:
            print(f"{direction:<8} {count:<6} {float(percentage):<7.1f}%")
        
        # 7. 按策略区域统计交易方向
        print("\n7️⃣ 按策略区域统计交易方向...")
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区'
                    WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                    WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                    ELSE '其他区域'
                END AS 策略区域,
                交易方向,
                COUNT(*) as 数量
            FROM test 
            GROUP BY 策略区域, 交易方向
            ORDER BY 策略区域, 交易方向
        """)
        
        zone_direction_stats = cursor.fetchall()
        
        print(f"\n📊 按策略区域的交易方向分布:")
        print("-" * 50)
        print(f"{'策略区域':<12} {'交易方向':<8} {'数量':<6}")
        print("-" * 50)
        
        for zone, direction, count in zone_direction_stats:
            print(f"{zone:<12} {direction:<8} {count:<6}")
        
        # 8. 检查是否有异常
        print("\n8️⃣ 检查交易方向异常...")
        cursor.execute("""
            SELECT 交易序号, `控制系数`, `资金流比例`, 交易方向,
                   CASE 
                       WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '应该买涨'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '应该观望'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '应该买跌'
                       ELSE '应该买跌'
                   END AS 应该方向
            FROM test 
            WHERE (
                (`控制系数` > 0.43 AND `资金流比例` > 0.43 AND 交易方向 != '买涨') OR
                (`控制系数` > 0.333 AND `控制系数` < 0.4 AND 交易方向 != '观望') OR
                ((`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) AND 交易方向 != '买跌') OR
                (NOT (`控制系数` > 0.43 AND `资金流比例` > 0.43) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND 交易方向 != '买跌')
            )
        """)
        
        anomalies = cursor.fetchall()
        
        if anomalies:
            print(f"⚠️ 发现 {len(anomalies)} 条异常记录:")
            for anomaly in anomalies:
                trade_id, y_val, x_val, actual_direction, should_direction = anomaly
                print(f"   交易{trade_id}: Y={float(y_val):.3f}, X={float(x_val):.3f}, 实际={actual_direction}, {should_direction}")
        else:
            print("✅ 所有交易方向都正确，无异常")
        
        connection.close()
        print(f"\n🎉 交易方向更新完成!")
        print(f"📊 总更新记录: {high_profit_rows + control_rows + strong_loss_rows + other_rows} 条")
        
    except Exception as e:
        print(f"❌ 更新交易方向失败: {e}")

if __name__ == "__main__":
    update_trading_direction()
