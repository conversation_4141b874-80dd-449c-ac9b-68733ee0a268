#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用yfinance获取HK00023数据
===========================

从雅虎财经获取HK00023的历史数据，并重新计算Y和X值
然后运行Cosmoon博弈论策略回测

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class YFinanceDataFetcher:
    def __init__(self):
        """初始化yfinance数据获取器"""
        self.symbol = "0023.HK"  # HK00023在雅虎财经的代码
        self.data = None
        
    def fetch_data(self, period="20y"):
        """获取历史数据"""
        print(f"📊 从雅虎财经获取 {self.symbol} 数据...")
        
        try:
            # 获取股票数据
            ticker = yf.Ticker(self.symbol)
            
            # 获取历史数据
            self.data = ticker.history(period=period, interval="1d")
            
            if self.data.empty:
                print("❌ 未获取到数据")
                return False
            
            # 数据预处理
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            # 确保有必要的列
            required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in self.data.columns:
                    print(f"❌ 缺少必要列: {col}")
                    return False
            
            print(f"✅ 数据获取成功:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data)} 条")
            print(f"   • 价格范围: {self.data['close'].min():.2f} - {self.data['close'].max():.2f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据获取失败: {e}")
            return False
    
    def calculate_technical_indicators(self):
        """计算技术指标"""
        print("🔢 计算技术指标...")
        
        # 移动平均线
        self.data['ma_5'] = self.data['close'].rolling(window=5).mean()
        self.data['ma_10'] = self.data['close'].rolling(window=10).mean()
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        
        # RSI
        def calculate_rsi(prices, window=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi / 100  # 转换为0-1范围
        
        self.data['rsi'] = calculate_rsi(self.data['close'])
        
        # 布林带
        self.data['bb_middle'] = self.data['close'].rolling(window=20).mean()
        bb_std = self.data['close'].rolling(window=20).std()
        self.data['bb_upper'] = self.data['bb_middle'] + (bb_std * 2)
        self.data['bb_lower'] = self.data['bb_middle'] - (bb_std * 2)
        self.data['bb_position'] = (self.data['close'] - self.data['bb_lower']) / (self.data['bb_upper'] - self.data['bb_lower'])
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        print("✅ 技术指标计算完成")
    
    def calculate_y_values(self):
        """重新计算Y值 - 基于Cosmoon的博弈论理论"""
        print("🎯 计算Y值 (控股商托价概率)...")
        
        # 方法1: 基于价格相对于移动平均线的位置
        # Y值表示价格高于均值的概率，即控股商托价的概率
        
        # 计算价格相对于不同周期均线的位置
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        price_vs_ma60 = self.data['close'] / self.data['ma_60']
        
        # 计算趋势强度
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        
        # 基础Y值：价格相对于均线的位置
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),  # 价格高于均线
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))  # 价格低于均线
        
        # 趋势调整：上升趋势增加Y值，下降趋势减少Y值
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        
        # 成交量调整：高成交量增强信号
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))
        
        # 布林带位置调整
        bb_adjustment = 0.1 * (self.data['bb_position'] - 0.5)
        
        # 综合Y值
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment + bb_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        
        # 填充初始NaN值
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        print(f"   ✅ Y值计算完成，范围: {self.data['y_probability'].min():.3f} - {self.data['y_probability'].max():.3f}")
    
    def calculate_x_values(self):
        """重新计算X值 - 资金流入比例"""
        print("💰 计算X值 (资金流入比例)...")
        
        # 方法1: 基于价格变化和成交量的资金流向
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        
        # 计算当日资金流向强度
        money_flow = self.data['volume'] * price_change
        
        # 滚动计算资金流入比例
        window = 20
        
        def calculate_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            if total_flow == 0:
                return 0.5
            
            return inflows / total_flow
        
        # 计算滚动资金流入比例
        self.data['base_x'] = money_flow.rolling(window=window).apply(calculate_inflow_ratio, raw=False)
        
        # RSI调整
        rsi_adjustment = 0.3 * (self.data['rsi'] - 0.5)
        
        # 价格动量调整
        momentum = self.data['close'].pct_change(5).fillna(0)
        momentum_adjustment = 0.2 * np.tanh(momentum * 10)
        
        # 综合X值
        self.data['inflow_ratio'] = self.data['base_x'] + rsi_adjustment + momentum_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        
        # 填充初始NaN值
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        print(f"   ✅ X值计算完成，范围: {self.data['inflow_ratio'].min():.3f} - {self.data['inflow_ratio'].max():.3f}")
    
    def analyze_strategy_zones(self):
        """分析策略区域分布"""
        print("\n🎯 Cosmoon策略区域分析:")
        print("-" * 50)
        
        # 计算E值
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        # 标记策略区域
        high_profit = ((self.data['y_probability'] > 0.5) & (self.data['inflow_ratio'] > 0.5)).sum()
        control_zone = ((self.data['y_probability'] > 0.333) & (self.data['y_probability'] < 0.4)).sum()
        strong_loss = ((self.data['y_probability'] < 0.25) | (self.data['inflow_ratio'] < 0.25)).sum()
        positive_e = (self.data['e_value'] > 0).sum()
        
        total = len(self.data)
        
        print(f"📊 策略区域分布:")
        print(f"   • 高值盈利区 (Y>0.5, X>0.5): {high_profit} 天 ({high_profit/total*100:.1f}%)")
        print(f"   • 控股商控制区 (0.333<Y<0.4): {control_zone} 天 ({control_zone/total*100:.1f}%)")
        print(f"   • 强亏损区 (Y<0.25或X<0.25): {strong_loss} 天 ({strong_loss/total*100:.1f}%)")
        print(f"   • E>0 (理论盈利): {positive_e} 天 ({positive_e/total*100:.1f}%)")
        
        # 显示最近数据样本
        print(f"\n📈 最近10天数据样本:")
        recent = self.data.tail(10)[['date', 'close', 'y_probability', 'inflow_ratio', 'e_value']]
        for _, row in recent.iterrows():
            date_str = row['date'].strftime('%Y-%m-%d')
            print(f"   {date_str}: 价格={row['close']:.2f}, Y={row['y_probability']:.3f}, X={row['inflow_ratio']:.3f}, E={row['e_value']:.3f}")
    
    def save_data(self):
        """保存数据到CSV"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"hk00023_yfinance_data_{timestamp}.csv"
        
        # 选择关键列保存
        save_columns = ['date', 'open', 'high', 'low', 'close', 'volume', 
                       'y_probability', 'inflow_ratio', 'e_value', 'rsi', 'bb_position']
        
        self.data[save_columns].to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"\n💾 数据已保存: {filename}")
        return filename

def run_cosmoon_strategy_with_yf_data(data):
    """使用yfinance数据运行Cosmoon策略"""
    print(f"\n🚀 开始Cosmoon凯利公式策略回测 (yfinance数据)...")
    print("="*70)
    
    # 策略参数
    initial_capital = 8000
    current_cash = initial_capital
    current_positions = []
    trades = []
    
    # 凯利公式参数
    kelly_win_rate = 0.6  # 调整胜率预期
    kelly_win_ratio = 2
    kelly_loss_ratio = 1
    max_position_ratio = 0.25  # 降低最大仓位
    
    def calculate_kelly_position(win_rate, win_ratio, loss_ratio):
        b = win_ratio / loss_ratio
        p = win_rate
        q = 1 - p
        kelly_fraction = (b * p - q) / b
        return max(0, min(kelly_fraction, max_position_ratio))
    
    total_trades = 0
    winning_trades = 0
    losing_trades = 0
    
    for i, row in data.iterrows():
        date = row['date']
        price = row['close']
        y_val = row['y_probability']
        x_val = row['inflow_ratio']
        
        # 策略信号判断
        high_profit_signal = (y_val > 0.5 and x_val > 0.5)
        control_zone = (0.333 < y_val < 0.4)
        strong_loss_signal = (y_val < 0.25 or x_val < 0.25)
        
        # 检查止盈止损
        positions_to_close = []
        for j, position in enumerate(current_positions):
            if position['direction'] == 'LONG':
                profit_pct = (price - position['entry_price']) / position['entry_price']
                if profit_pct >= 0.04:  # 止盈4%
                    positions_to_close.append((j, 'TAKE_PROFIT'))
                elif profit_pct <= -0.02:  # 止损2%
                    positions_to_close.append((j, 'STOP_LOSS'))
            else:  # SHORT
                profit_pct = (position['entry_price'] - price) / position['entry_price']
                if profit_pct >= 0.04:  # 止盈4%
                    positions_to_close.append((j, 'TAKE_PROFIT'))
                elif profit_pct <= -0.02:  # 止损2%
                    positions_to_close.append((j, 'STOP_LOSS'))
        
        # 平仓处理
        for j, reason in reversed(positions_to_close):
            position = current_positions[j]
            
            if position['direction'] == 'LONG':
                profit = (price - position['entry_price']) * position['shares']
            else:
                profit = (position['entry_price'] - price) * position['shares']
            
            current_cash += position['shares'] * price
            
            if profit > 0:
                winning_trades += 1
            else:
                losing_trades += 1
            
            total_trades += 1
            del current_positions[j]
        
        # 开仓逻辑
        if high_profit_signal and not control_zone and len(current_positions) < 2:
            # 高值盈利区：买涨
            kelly_fraction = calculate_kelly_position(kelly_win_rate, kelly_win_ratio, kelly_loss_ratio)
            position_value = current_cash * kelly_fraction
            shares = position_value / price
            
            if shares > 0:
                current_cash -= position_value
                current_positions.append({
                    'entry_date': date,
                    'entry_price': price,
                    'shares': shares,
                    'direction': 'LONG'
                })
        
        elif strong_loss_signal and len(current_positions) < 2:
            # 强亏损区：买涨（低位反弹）
            kelly_fraction = calculate_kelly_position(kelly_win_rate * 0.8, kelly_win_ratio, kelly_loss_ratio) * 0.5
            position_value = current_cash * kelly_fraction
            shares = position_value / price
            
            if shares > 0:
                current_cash -= position_value
                current_positions.append({
                    'entry_date': date,
                    'entry_price': price,
                    'shares': shares,
                    'direction': 'LONG'
                })
        
        elif not control_zone and not high_profit_signal and not strong_loss_signal and len(current_positions) < 1:
            # 其他区域：买跌
            kelly_fraction = calculate_kelly_position(kelly_win_rate * 0.7, kelly_win_ratio, kelly_loss_ratio) * 0.3
            position_value = current_cash * kelly_fraction
            shares = position_value / price
            
            if shares > 0:
                current_cash -= position_value
                current_positions.append({
                    'entry_date': date,
                    'entry_price': price,
                    'shares': shares,
                    'direction': 'SHORT'
                })
    
    # 最终清仓
    final_price = data['close'].iloc[-1]
    for position in current_positions:
        current_cash += position['shares'] * final_price
        total_trades += 1
    
    # 计算结果
    final_value = current_cash
    total_return = (final_value / initial_capital - 1) * 100
    years = len(data) / 252
    annual_return = (final_value / initial_capital) ** (1/years) - 1
    
    print(f"\n✅ yfinance数据回测完成!")
    print(f"📊 交易统计:")
    print(f"   • 总交易次数: {total_trades}")
    print(f"   • 盈利交易: {winning_trades}")
    print(f"   • 亏损交易: {losing_trades}")
    print(f"   • 实际胜率: {winning_trades/total_trades*100:.1f}%" if total_trades > 0 else "   • 胜率: N/A")
    
    print(f"\n💰 收益统计:")
    print(f"   • 初始资金: {initial_capital:,} 港币")
    print(f"   • 最终资金: {final_value:,.0f} 港币")
    print(f"   • 总收益: {final_value - initial_capital:+,.0f} 港币")
    print(f"   • 总收益率: {total_return:+.2f}%")
    print(f"   • 年化收益率: {annual_return*100:.2f}%")

def main():
    """主函数"""
    print("🎯 Cosmoon博弈论策略 - yfinance数据回测")
    print("="*60)
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 创建数据获取器
    fetcher = YFinanceDataFetcher()
    
    # 获取数据
    if not fetcher.fetch_data():
        return
    
    # 计算技术指标
    fetcher.calculate_technical_indicators()
    
    # 计算Y和X值
    fetcher.calculate_y_values()
    fetcher.calculate_x_values()
    
    # 分析策略区域
    fetcher.analyze_strategy_zones()
    
    # 保存数据
    fetcher.save_data()
    
    # 运行策略回测
    run_cosmoon_strategy_with_yf_data(fetcher.data)
    
    print(f"\n🎉 yfinance数据分析完成!")
    print(f"💡 数据更准确，策略更可靠！")

if __name__ == "__main__":
    main()
