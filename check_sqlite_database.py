#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查SQLite数据库状态
==================

检查所有SQLite数据库文件的存在和内容

作者: Cosmoon NG
日期: 2025年7月
"""

import sqlite3
import pandas as pd
import os
import glob

def check_database_files():
    """检查数据库文件"""
    print("🔍 检查SQLite数据库文件状态:")
    print("="*50)
    
    # 查找所有数据库文件
    db_files = glob.glob("*.db")
    
    if not db_files:
        print("❌ 未找到任何数据库文件")
        return []
    
    print(f"📁 找到 {len(db_files)} 个数据库文件:")
    for db_file in db_files:
        file_size = os.path.getsize(db_file)
        print(f"   • {db_file}: {file_size:,} 字节")
    
    return db_files

def check_database_content(db_path):
    """检查数据库内容"""
    print(f"\n🔍 检查数据库: {db_path}")
    print("="*60)
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 检查表
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        if not tables:
            print("❌ 数据库中没有表")
            conn.close()
            return
        
        print(f"📊 数据库表: {[table[0] for table in tables]}")
        
        # 检查hk00023表
        if ('hk00023',) in tables:
            # 检查表结构
            cursor.execute("PRAGMA table_info(hk00023)")
            columns = cursor.fetchall()
            
            print(f"\n📋 hk00023表结构:")
            for col in columns:
                print(f"   • {col[1]} ({col[2]})")
            
            # 检查数据量
            cursor.execute("SELECT COUNT(*) FROM hk00023")
            count = cursor.fetchone()[0]
            print(f"\n📊 总记录数: {count:,}")
            
            if count > 0:
                # 检查日期范围
                cursor.execute("SELECT MIN(date), MAX(date) FROM hk00023")
                date_range = cursor.fetchone()
                print(f"📅 日期范围: {date_range[0]} 至 {date_range[1]}")
                
                # 检查是否有资金流数据
                money_flow_columns = ['money_flow_in', 'money_flow_out', 'money_flow_ratio']
                existing_columns = [col[1] for col in columns]
                
                has_money_flow = all(col in existing_columns for col in money_flow_columns)
                print(f"💰 资金流数据: {'✅ 存在' if has_money_flow else '❌ 缺失'}")
                
                if has_money_flow:
                    # 检查资金流数据统计
                    cursor.execute("""
                        SELECT 
                            AVG(money_flow_in) as avg_in,
                            AVG(money_flow_out) as avg_out,
                            AVG(money_flow_ratio) as avg_ratio,
                            COUNT(*) as total_records
                        FROM hk00023
                        WHERE money_flow_in IS NOT NULL
                    """)
                    flow_stats = cursor.fetchone()
                    
                    print(f"📈 资金流统计:")
                    print(f"   • 平均流入: {flow_stats[0]:,.0f}")
                    print(f"   • 平均流出: {flow_stats[1]:,.0f}")
                    print(f"   • 平均比例: {flow_stats[2]:.3f}")
                    print(f"   • 有效记录: {flow_stats[3]:,}")
                
                # 显示最新几条记录
                sample_columns = "date, close, volume"
                if has_money_flow:
                    sample_columns += ", money_flow_in, money_flow_out, money_flow_ratio"
                
                df_sample = pd.read_sql_query(f"""
                    SELECT {sample_columns}
                    FROM hk00023 
                    ORDER BY date DESC 
                    LIMIT 3
                """, conn)
                
                print(f"\n📋 最新3条记录:")
                print(df_sample.to_string(index=False))
                
        else:
            print("❌ 未找到hk00023表")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")

def main():
    """主函数"""
    print("🏦 HK00023 SQLite数据库状态检查")
    print("="*50)
    
    # 检查数据库文件
    db_files = check_database_files()
    
    # 检查每个数据库的内容
    for db_file in db_files:
        check_database_content(db_file)
    
    print(f"\n🎯 检查完成!")
    
    if not db_files:
        print("💡 建议运行 create_hk00023_20year_database.py 创建数据库")
    else:
        print("💡 如果数据不完整，建议重新运行创建脚本")

if __name__ == "__main__":
    main()
