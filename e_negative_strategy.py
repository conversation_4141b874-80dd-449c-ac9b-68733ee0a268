#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
E<0时的赚钱策略
===============

专门针对E<0情况的博弈论策略
包含反向操作、波动套利、时间窗口等多种方法

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class ENegativeStrategy:
    def __init__(self):
        """初始化E<0策略"""
        self.symbol = "0023.HK"
        self.data = None
        
        # 策略参数
        self.strategy_params = {
            'initial_capital': 30000,
            'max_position_ratio': 0.1,  # 降低仓位风险
            'max_total_positions': 2,   # 减少同时持仓
            
            # E<0专用参数
            'e_threshold': 0.05,        # E值阈值
            'volatility_threshold': 0.02, # 波动率阈值
            'reversal_factor': 0.8,     # 反向操作强度
            
            # 止盈止损（更保守）
            'take_profit': 0.02,        # 止盈2%
            'stop_loss': 0.01,          # 止损1%
            'volatility_take_profit': 0.03, # 波动套利止盈3%
            'volatility_stop_loss': 0.015,  # 波动套利止损1.5%
            
            'transaction_cost': 0.0025,
        }
        
        self.trades = []
        self.current_positions = []
    
    def fetch_and_prepare_data(self):
        """获取并准备数据"""
        print(f"📊 获取东亚银行数据并分析E<0策略...")
        
        ticker = yf.Ticker(self.symbol)
        self.data = ticker.history(period="20y", interval="1d")
        
        if self.data.empty:
            return False
        
        self.data.reset_index(inplace=True)
        self.data.columns = [col.lower() for col in self.data.columns]
        
        # 计算技术指标
        self._calculate_indicators()
        
        # 计算Y和X值
        self._calculate_y_x_values()
        
        # 计算E值
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        # 分析E值分布
        self._analyze_e_distribution()
        
        return True
    
    def _calculate_indicators(self):
        """计算技术指标"""
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        
        # 波动率
        self.data['returns'] = self.data['close'].pct_change()
        self.data['volatility'] = self.data['returns'].rolling(window=20).std()
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        self.data['bb_middle'] = self.data['close'].rolling(window=20).mean()
        bb_std = self.data['close'].rolling(window=20).std()
        self.data['bb_upper'] = self.data['bb_middle'] + (bb_std * 2)
        self.data['bb_lower'] = self.data['bb_middle'] - (bb_std * 2)
        self.data['bb_position'] = (self.data['close'] - self.data['bb_lower']) / (self.data['bb_upper'] - self.data['bb_lower'])
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
    
    def _calculate_y_x_values(self):
        """计算Y和X值"""
        # Y值计算
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
        
        # 趋势和成交量调整
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        # X值计算
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        rsi_adjustment = 0.3 * (self.data['rsi'] / 100 - 0.5)
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
    
    def _analyze_e_distribution(self):
        """分析E值分布"""
        print(f"\n📊 E值分布分析:")
        print("-" * 50)
        
        e_positive = (self.data['e_value'] > 0).sum()
        e_negative = (self.data['e_value'] < 0).sum()
        e_near_zero = (abs(self.data['e_value']) < self.strategy_params['e_threshold']).sum()
        
        total = len(self.data)
        
        print(f"   • E>0: {e_positive} 天 ({e_positive/total*100:.1f}%)")
        print(f"   • E<0: {e_negative} 天 ({e_negative/total*100:.1f}%)")
        print(f"   • E≈0: {e_near_zero} 天 ({e_near_zero/total*100:.1f}%)")
        print(f"   • E值范围: {self.data['e_value'].min():.3f} - {self.data['e_value'].max():.3f}")
        print(f"   • E值平均: {self.data['e_value'].mean():.3f}")
        
        # 分析E<0时的价格表现
        e_negative_data = self.data[self.data['e_value'] < 0]
        if len(e_negative_data) > 0:
            avg_return = e_negative_data['returns'].mean()
            volatility = e_negative_data['returns'].std()
            print(f"\n📉 E<0时期分析:")
            print(f"   • 平均日收益率: {avg_return*100:.3f}%")
            print(f"   • 波动率: {volatility*100:.2f}%")
            print(f"   • 最大单日涨幅: {e_negative_data['returns'].max()*100:.2f}%")
            print(f"   • 最大单日跌幅: {e_negative_data['returns'].min()*100:.2f}%")
    
    def determine_strategy(self, row):
        """确定E<0时的策略"""
        e_val = row['e_value']
        volatility = row['volatility']
        bb_position = row['bb_position']
        rsi = row['rsi']
        
        if e_val < -0.1:
            # 强烈E<0：反向操作策略
            if bb_position > 0.8 and rsi > 70:
                return 'REVERSE_SHORT'  # 反向做空
            elif bb_position < 0.2 and rsi < 30:
                return 'REVERSE_LONG'   # 反向做多
                
        elif -0.1 <= e_val < 0:
            # 轻微E<0：波动套利策略
            if volatility > self.strategy_params['volatility_threshold']:
                if bb_position > 0.7:
                    return 'VOLATILITY_SHORT'
                elif bb_position < 0.3:
                    return 'VOLATILITY_LONG'
                    
        elif 0 <= e_val < self.strategy_params['e_threshold']:
            # E接近0：观望或小仓位试探
            return 'WAIT'
            
        return 'NO_ACTION'
    
    def calculate_position_size(self, strategy_type, current_cash):
        """根据策略类型计算仓位大小"""
        base_ratio = self.strategy_params['max_position_ratio']
        
        if strategy_type.startswith('REVERSE'):
            # 反向操作：更小仓位
            return current_cash * base_ratio * self.strategy_params['reversal_factor']
        elif strategy_type.startswith('VOLATILITY'):
            # 波动套利：中等仓位
            return current_cash * base_ratio * 0.6
        else:
            return current_cash * base_ratio * 0.3
    
    def check_stop_conditions(self, position, current_price):
        """检查止盈止损"""
        entry_price = position['entry_price']
        direction = position['direction']
        strategy_type = position['strategy_type']
        
        if direction == 'LONG':
            profit_pct = (current_price - entry_price) / entry_price
        else:  # SHORT
            profit_pct = (entry_price - current_price) / entry_price
        
        # 根据策略类型选择止盈止损
        if strategy_type.startswith('VOLATILITY'):
            take_profit = self.strategy_params['volatility_take_profit']
            stop_loss = self.strategy_params['volatility_stop_loss']
        else:
            take_profit = self.strategy_params['take_profit']
            stop_loss = self.strategy_params['stop_loss']
        
        if profit_pct >= take_profit:
            return 'TAKE_PROFIT'
        elif profit_pct <= -stop_loss:
            return 'STOP_LOSS'
        
        return None
    
    def run_backtest(self):
        """运行E<0策略回测"""
        print(f"\n🚀 开始E<0赚钱策略回测...")
        print("="*70)
        print(f"💰 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"🎯 专门应对E<0的策略")
        print("="*70)
        
        current_cash = self.strategy_params['initial_capital']
        winning_trades = 0
        losing_trades = 0
        
        # 统计各策略使用次数
        strategy_counts = {}
        
        for i, row in self.data.iterrows():
            date = row['date']
            price = row['close']
            e_val = row['e_value']
            
            # 检查止盈止损
            positions_to_close = []
            for j, position in enumerate(self.current_positions):
                stop_reason = self.check_stop_conditions(position, price)
                if stop_reason:
                    positions_to_close.append((j, stop_reason))
            
            # 平仓处理
            for j, reason in reversed(positions_to_close):
                position = self.current_positions[j]
                
                if position['direction'] == 'LONG':
                    profit = (price - position['entry_price']) * position['shares']
                    exit_value = position['shares'] * price
                else:  # SHORT
                    profit = (position['entry_price'] - price) * position['shares']
                    exit_value = position['shares'] * price
                
                current_cash += exit_value - position['cost']
                
                trade_record = {
                    'date': date,
                    'direction': position['direction'],
                    'strategy_type': position['strategy_type'],
                    'entry_price': position['entry_price'],
                    'exit_price': price,
                    'shares': position['shares'],
                    'profit': profit,
                    'profit_pct': profit / (position['shares'] * position['entry_price']) * 100,
                    'reason': reason,
                    'e_value': e_val
                }
                self.trades.append(trade_record)
                
                if profit > 0:
                    winning_trades += 1
                else:
                    losing_trades += 1
                
                del self.current_positions[j]
            
            # 开仓逻辑
            if len(self.current_positions) < self.strategy_params['max_total_positions']:
                strategy = self.determine_strategy(row)
                
                if strategy != 'NO_ACTION' and strategy != 'WAIT':
                    position_value = self.calculate_position_size(strategy, current_cash)
                    transaction_cost = position_value * self.strategy_params['transaction_cost']
                    net_value = position_value - transaction_cost
                    shares = net_value / price
                    
                    if shares > 0 and position_value > 1000:
                        current_cash -= position_value
                        
                        direction = 'LONG' if 'LONG' in strategy else 'SHORT'
                        
                        new_position = {
                            'entry_date': date,
                            'entry_price': price,
                            'shares': shares,
                            'direction': direction,
                            'strategy_type': strategy,
                            'cost': transaction_cost,
                            'e_value': e_val
                        }
                        self.current_positions.append(new_position)
                        
                        # 统计策略使用次数
                        strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
        
        # 最终清仓
        final_price = self.data['close'].iloc[-1]
        for position in self.current_positions:
            if position['direction'] == 'LONG':
                profit = (final_price - position['entry_price']) * position['shares']
            else:
                profit = (position['entry_price'] - final_price) * position['shares']
            
            current_cash += position['shares'] * final_price
            
            if profit > 0:
                winning_trades += 1
            else:
                losing_trades += 1
        
        # 计算结果
        final_value = current_cash
        total_return = (final_value / self.strategy_params['initial_capital'] - 1) * 100
        years = len(self.data) / 252
        annual_return = (final_value / self.strategy_params['initial_capital']) ** (1/years) - 1
        
        total_trades = len(self.trades)
        win_rate = winning_trades / (winning_trades + losing_trades) * 100 if (winning_trades + losing_trades) > 0 else 0
        
        print(f"\n✅ E<0策略回测完成!")
        print(f"📊 交易统计:")
        print(f"   • 总交易次数: {total_trades}")
        print(f"   • 盈利交易: {winning_trades}")
        print(f"   • 亏损交易: {losing_trades}")
        print(f"   • 实际胜率: {win_rate:.1f}%")
        
        print(f"\n💰 收益统计:")
        print(f"   • 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"   • 最终资金: {final_value:,.0f} 港币")
        print(f"   • 总收益: {final_value - self.strategy_params['initial_capital']:+,.0f} 港币")
        print(f"   • 总收益率: {total_return:+.2f}%")
        print(f"   • 年化收益率: {annual_return*100:.2f}%")
        
        # 分析各策略效果
        if strategy_counts:
            print(f"\n🎯 各策略使用统计:")
            for strategy, count in strategy_counts.items():
                strategy_trades = [t for t in self.trades if t['strategy_type'] == strategy]
                if strategy_trades:
                    strategy_profit = sum(t['profit'] for t in strategy_trades)
                    strategy_win_rate = len([t for t in strategy_trades if t['profit'] > 0]) / len(strategy_trades) * 100
                    print(f"   • {strategy}: {count}次, 胜率{strategy_win_rate:.1f}%, 盈亏{strategy_profit:+.0f}港币")
        
        # 分析E<0时期的交易效果
        e_negative_trades = [t for t in self.trades if t['e_value'] < 0]
        if e_negative_trades:
            e_negative_profit = sum(t['profit'] for t in e_negative_trades)
            e_negative_count = len(e_negative_trades)
            e_negative_win_rate = len([t for t in e_negative_trades if t['profit'] > 0]) / e_negative_count * 100
            
            print(f"\n📉 E<0时期交易分析:")
            print(f"   • E<0交易次数: {e_negative_count}")
            print(f"   • E<0胜率: {e_negative_win_rate:.1f}%")
            print(f"   • E<0总盈亏: {e_negative_profit:+,.0f} 港币")
            print(f"   • E<0平均盈亏: {e_negative_profit/e_negative_count:+.0f} 港币/次")

def main():
    """主函数"""
    print("🎯 E<0时的赚钱策略")
    print("="*60)
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("💡 专门应对E<0的博弈论策略")
    
    strategy = ENegativeStrategy()
    
    if not strategy.fetch_and_prepare_data():
        print("❌ 数据获取失败")
        return
    
    strategy.run_backtest()
    
    print(f"\n🎉 E<0策略回测完成!")
    print(f"💡 证明E<0时也有赚钱机会！")

if __name__ == "__main__":
    main()
