"""
博弈论投资策略 - 系统控制器
统一管理MT5交易、数据收集、分析监控
"""

import schedule
import time
import threading
from datetime import datetime, timedelta
import json
import os
from trading_data_manager import TradingDataManager, MT5DataCollector
from trading_analyzer import TradingAnalyzer
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('system_controller.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SystemController:
    def __init__(self, config_file='system_config.json'):
        """
        初始化系统控制器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = self.load_config(config_file)
        self.data_manager = None
        self.data_collector = None
        self.analyzer = None
        self.running = False
        
        self.initialize_components()
    
    def load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            "database": {
                "host": "localhost",
                "database": "game_theory_trading",
                "user": "your_username",
                "password": "your_password",
                "charset": "utf8mb4"
            },
            "trading": {
                "symbol": "HSI50",
                "lot_size": 0.01,
                "magic_number": 20250713,
                "data_collection_interval": 300,  # 5分钟
                "analysis_interval": 1800,        # 30分钟
                "risk_check_interval": 600        # 10分钟
            },
            "notifications": {
                "email_enabled": False,
                "email_smtp": "smtp.gmail.com",
                "email_port": 587,
                "email_user": "<EMAIL>",
                "email_password": "your_password",
                "email_recipients": ["<EMAIL>"]
            },
            "system": {
                "auto_start": True,
                "log_level": "INFO",
                "backup_enabled": True,
                "backup_interval": 86400  # 24小时
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    # 合并配置
                    default_config.update(user_config)
            except Exception as e:
                logger.error(f"❌ 加载配置文件失败: {e}")
        else:
            # 创建默认配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)
            logger.info(f"✅ 创建默认配置文件: {config_file}")
        
        return default_config
    
    def initialize_components(self):
        """初始化系统组件"""
        try:
            # 初始化数据管理器
            self.data_manager = TradingDataManager(self.config['database'])
            
            # 初始化数据收集器
            self.data_collector = MT5DataCollector(self.data_manager)
            
            # 初始化分析器
            self.analyzer = TradingAnalyzer(self.data_manager)
            
            logger.info("✅ 系统组件初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 系统组件初始化失败: {e}")
            raise
    
    def start_data_collection(self):
        """启动数据收集"""
        logger.info("🔄 启动数据收集...")
        
        try:
            # 收集市场数据
            self.data_collector.collect_market_data()
            
            # 收集账户信息
            self.data_collector.collect_account_info()
            
            # 记录系统日志
            self.data_manager.log_system_event(
                'INFO', 'DATA_COLLECTOR', '数据收集完成'
            )
            
        except Exception as e:
            logger.error(f"❌ 数据收集失败: {e}")
            self.data_manager.log_system_event(
                'ERROR', 'DATA_COLLECTOR', f'数据收集失败: {e}'
            )
    
    def start_analysis(self):
        """启动数据分析"""
        logger.info("📊 启动数据分析...")
        
        try:
            # 分析策略性能
            performance, _ = self.analyzer.analyze_strategy_performance(7)
            
            if performance:
                logger.info(f"📈 策略性能: 胜率{performance['win_rate']:.1f}%, 总盈亏{performance['total_profit']:+.2f}")
                
                # 记录性能到数据库
                self.data_manager.log_system_event(
                    'INFO', 'ANALYZER', '性能分析完成', performance
                )
            
            # 创建性能仪表板
            self.analyzer.create_performance_dashboard(7)
            
        except Exception as e:
            logger.error(f"❌ 数据分析失败: {e}")
            self.data_manager.log_system_event(
                'ERROR', 'ANALYZER', f'数据分析失败: {e}'
            )
    
    def check_risks(self):
        """检查风险"""
        logger.info("⚠️ 检查风险...")
        
        try:
            risks = self.data_manager.check_risk_conditions()
            
            if risks:
                logger.warning(f"⚠️ 发现{len(risks)}个风险警报")
                
                # 发送通知
                if self.config['notifications']['email_enabled']:
                    self.send_risk_notification(risks)
                
                # 记录风险日志
                for risk in risks:
                    self.data_manager.log_system_event(
                        'WARNING', 'RISK_MONITOR', risk['description'], risk
                    )
            else:
                logger.info("✅ 风险检查正常")
            
        except Exception as e:
            logger.error(f"❌ 风险检查失败: {e}")
            self.data_manager.log_system_event(
                'ERROR', 'RISK_MONITOR', f'风险检查失败: {e}'
            )
    
    def send_risk_notification(self, risks):
        """发送风险通知"""
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            # 构建邮件内容
            subject = f"博弈论策略风险警报 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            body = "检测到以下风险:\n\n"
            for risk in risks:
                body += f"• {risk['description']}\n"
                body += f"  风险等级: {risk['level']}\n"
                body += f"  当前值: {risk['current_value']}\n"
                body += f"  阈值: {risk['threshold_value']}\n\n"
            
            # 发送邮件
            msg = MIMEMultipart()
            msg['From'] = self.config['notifications']['email_user']
            msg['Subject'] = subject
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(
                self.config['notifications']['email_smtp'],
                self.config['notifications']['email_port']
            )
            server.starttls()
            server.login(
                self.config['notifications']['email_user'],
                self.config['notifications']['email_password']
            )
            
            for recipient in self.config['notifications']['email_recipients']:
                msg['To'] = recipient
                server.send_message(msg)
            
            server.quit()
            logger.info("✅ 风险通知邮件发送成功")
            
        except Exception as e:
            logger.error(f"❌ 发送风险通知失败: {e}")
    
    def generate_daily_report(self):
        """生成每日报告"""
        logger.info("📄 生成每日报告...")
        
        try:
            # 生成报告
            report = self.data_manager.generate_daily_report()
            
            # 导出Excel
            filename = self.analyzer.export_analysis_to_excel(
                f"daily_report_{datetime.now().strftime('%Y%m%d')}.xlsx"
            )
            
            # 记录日志
            self.data_manager.log_system_event(
                'INFO', 'REPORT_GENERATOR', f'每日报告生成完成: {filename}', report
            )
            
            logger.info(f"✅ 每日报告生成完成: {filename}")
            
        except Exception as e:
            logger.error(f"❌ 生成每日报告失败: {e}")
            self.data_manager.log_system_event(
                'ERROR', 'REPORT_GENERATOR', f'每日报告生成失败: {e}'
            )
    
    def setup_scheduler(self):
        """设置定时任务"""
        logger.info("⏰ 设置定时任务...")
        
        # 数据收集 - 每5分钟
        schedule.every(self.config['trading']['data_collection_interval']).seconds.do(
            self.start_data_collection
        )
        
        # 数据分析 - 每30分钟
        schedule.every(self.config['trading']['analysis_interval']).seconds.do(
            self.start_analysis
        )
        
        # 风险检查 - 每10分钟
        schedule.every(self.config['trading']['risk_check_interval']).seconds.do(
            self.check_risks
        )
        
        # 每日报告 - 每天18:00
        schedule.every().day.at("18:00").do(self.generate_daily_report)
        
        logger.info("✅ 定时任务设置完成")
    
    def run_scheduler(self):
        """运行定时任务"""
        logger.info("🚀 启动定时任务调度器...")
        
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"❌ 调度器运行错误: {e}")
                time.sleep(10)
    
    def start(self):
        """启动系统"""
        logger.info("🚀 启动博弈论投资策略系统...")
        
        try:
            # 设置定时任务
            self.setup_scheduler()
            
            # 启动标志
            self.running = True
            
            # 立即执行一次数据收集和分析
            self.start_data_collection()
            self.start_analysis()
            self.check_risks()
            
            # 启动调度器线程
            scheduler_thread = threading.Thread(target=self.run_scheduler)
            scheduler_thread.daemon = True
            scheduler_thread.start()
            
            logger.info("✅ 系统启动成功")
            
            # 主循环
            try:
                while self.running:
                    time.sleep(60)  # 每分钟检查一次
                    
            except KeyboardInterrupt:
                logger.info("🛑 接收到停止信号")
                self.stop()
                
        except Exception as e:
            logger.error(f"❌ 系统启动失败: {e}")
            self.stop()
    
    def stop(self):
        """停止系统"""
        logger.info("🛑 停止博弈论投资策略系统...")
        
        self.running = False
        
        # 关闭数据库连接
        if self.data_manager:
            self.data_manager.close()
        
        logger.info("✅ 系统已停止")
    
    def status(self):
        """获取系统状态"""
        status_info = {
            'running': self.running,
            'config_loaded': self.config is not None,
            'database_connected': self.data_manager and self.data_manager.connection and self.data_manager.connection.is_connected(),
            'last_data_collection': None,
            'last_analysis': None,
            'last_risk_check': None
        }
        
        return status_info

def main():
    """主函数"""
    print("🚀 博弈论投资策略系统控制器")
    print("="*50)
    
    # 创建系统控制器
    controller = SystemController()
    
    try:
        # 启动系统
        controller.start()
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
    except Exception as e:
        print(f"❌ 系统错误: {e}")
    finally:
        controller.stop()

if __name__ == "__main__":
    main()
