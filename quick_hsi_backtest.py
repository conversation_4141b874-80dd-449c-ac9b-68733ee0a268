#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速恒生指数回测 - 博弈论策略
============================

简化版回测脚本，如果数据库连接有问题可以使用模拟数据

作者: 博弈论投资策略团队
日期: 2025年7月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import pymysql
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def test_database_connection():
    """测试数据库连接"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        # 检查可用的表
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        
        print("✅ 数据库连接成功")
        print(f"📊 可用表: {', '.join(tables)}")
        
        # 检查数据
        for table in ['hk00023', 'hk2800']:
            if table in tables:
                cursor.execute(f"""
                    SELECT COUNT(*) as total,
                           MIN(date) as start_date,
                           MAX(date) as end_date,
                           COUNT(CASE WHEN y_probability IS NOT NULL THEN 1 END) as y_count,
                           COUNT(CASE WHEN inflow_ratio IS NOT NULL THEN 1 END) as x_count
                    FROM {table}
                """)
                result = cursor.fetchone()
                print(f"📈 {table}: {result[0]}条记录, {result[1]} 至 {result[2]}")
                print(f"   Y值: {result[3]}条, X值: {result[4]}条")
        
        connection.close()
        return True, db_config
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False, None

def load_real_data(db_config):
    """加载真实数据"""
    try:
        connection = pymysql.connect(**db_config)
        
        # 尝试加载hk00023数据
        query = """
            SELECT 
                date,
                close as price,
                y_probability,
                inflow_ratio as x_ratio
            FROM hk00023 
            WHERE date >= DATE_SUB(CURDATE(), INTERVAL 25 YEAR)
            AND y_probability IS NOT NULL 
            AND inflow_ratio IS NOT NULL
            ORDER BY date ASC
        """
        
        data = pd.read_sql(query, connection)
        
        if data.empty:
            # 尝试hk2800
            query = query.replace('hk00023', 'hk2800')
            data = pd.read_sql(query, connection)
            
            if not data.empty:
                print("✅ 使用HK2800数据")
            else:
                print("❌ 未找到可用数据")
                return None
        else:
            print("✅ 使用HK00023数据")
        
        connection.close()
        
        data['date'] = pd.to_datetime(data['date'])
        print(f"📊 加载了 {len(data)} 条数据")
        print(f"📅 期间: {data['date'].min()} 至 {data['date'].max()}")
        
        return data
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def generate_simulation_data():
    """生成模拟数据用于测试"""
    print("🔧 生成模拟恒生指数数据...")
    
    # 生成25年的交易日数据
    start_date = datetime.now() - timedelta(days=25*365)
    dates = pd.date_range(start=start_date, end=datetime.now(), freq='B')  # 工作日
    
    np.random.seed(42)  # 固定随机种子
    
    # 模拟价格走势（带趋势的随机游走）
    n_days = len(dates)
    returns = np.random.normal(0.0003, 0.02, n_days)  # 日收益率
    
    # 添加长期趋势
    trend = np.linspace(0, 0.5, n_days)  # 25年50%的总趋势
    returns += trend / n_days
    
    # 计算价格
    initial_price = 15000  # 起始价格
    prices = [initial_price]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # 生成Y和X指标
    # Y值：基于价格动量和波动率
    price_momentum = pd.Series(prices).pct_change(20).fillna(0)
    volatility = pd.Series(prices).pct_change().rolling(20).std().fillna(0.02)
    
    # Y值在0.2-0.8之间，与动量正相关
    y_values = 0.5 + 0.3 * np.tanh(price_momentum * 10) + np.random.normal(0, 0.1, n_days)
    y_values = np.clip(y_values, 0.1, 0.9)
    
    # X值：资金流入比例，与Y值相关但有独立性
    x_values = 0.6 * y_values + 0.4 * np.random.uniform(0.1, 0.9, n_days)
    x_values = np.clip(x_values, 0.1, 0.9)
    
    # 创建DataFrame
    data = pd.DataFrame({
        'date': dates,
        'price': prices,
        'y_probability': y_values,
        'x_ratio': x_values
    })
    
    print(f"✅ 生成了 {len(data)} 条模拟数据")
    print(f"📅 期间: {data['date'].min()} 至 {data['date'].max()}")
    print(f"📊 价格范围: {data['price'].min():.0f} - {data['price'].max():.0f}")
    
    return data

def run_backtest(data, initial_capital=100000):
    """运行博弈论策略回测"""
    print(f"\n🚀 开始博弈论策略回测...")
    print(f"💰 初始资金: {initial_capital:,.0f} 港币")
    
    # 策略参数
    y_threshold = 0.4
    x_threshold = 0.4
    position_size = 0.8  # 80%仓位
    transaction_cost = 0.001  # 0.1%交易成本
    
    # 初始化
    cash = initial_capital
    position = 0  # 持仓数量
    trades = []
    portfolio_values = []
    
    for i, row in data.iterrows():
        date = row['date']
        price = row['price']
        y_val = row['y_probability']
        x_val = row['x_ratio']
        
        # 当前组合价值
        position_value = position * price
        total_value = cash + position_value
        
        # 交易信号
        buy_signal = (y_val > y_threshold and x_val > x_threshold and position == 0)
        sell_signal = (x_val < x_threshold and position > 0)
        
        # 执行交易
        if buy_signal:
            # 买入
            buy_amount = cash * position_size
            cost = buy_amount * transaction_cost
            net_amount = buy_amount - cost
            position = net_amount / price
            cash -= buy_amount
            
            trades.append({
                'date': date,
                'action': 'BUY',
                'price': price,
                'amount': buy_amount,
                'y_val': y_val,
                'x_val': x_val
            })
            
        elif sell_signal:
            # 卖出
            sell_amount = position * price
            cost = sell_amount * transaction_cost
            net_amount = sell_amount - cost
            cash += net_amount
            position = 0
            
            trades.append({
                'date': date,
                'action': 'SELL',
                'price': price,
                'amount': sell_amount,
                'y_val': y_val,
                'x_val': x_val
            })
        
        # 记录组合价值
        current_total = cash + (position * price)
        portfolio_values.append({
            'date': date,
            'total_value': current_total,
            'cash': cash,
            'position_value': position * price,
            'price': price
        })
    
    # 最终清仓
    if position > 0:
        final_value = position * data['price'].iloc[-1]
        cash += final_value * (1 - transaction_cost)
    
    # 转换为DataFrame
    trades_df = pd.DataFrame(trades)
    portfolio_df = pd.DataFrame(portfolio_values)
    
    return trades_df, portfolio_df, cash

def analyze_results(trades_df, portfolio_df, final_cash, initial_capital):
    """分析回测结果"""
    print(f"\n📊 回测结果分析")
    print("="*50)
    
    # 基本统计
    total_return = (final_cash / initial_capital - 1) * 100
    
    # 计算年化收益率
    start_date = portfolio_df['date'].iloc[0]
    end_date = portfolio_df['date'].iloc[-1]
    years = (end_date - start_date).days / 365.25
    annual_return = (final_cash / initial_capital) ** (1/years) - 1
    
    # 最大回撤
    portfolio_df['cumulative_return'] = portfolio_df['total_value'] / initial_capital
    portfolio_df['running_max'] = portfolio_df['cumulative_return'].expanding().max()
    portfolio_df['drawdown'] = (portfolio_df['cumulative_return'] - portfolio_df['running_max']) / portfolio_df['running_max']
    max_drawdown = portfolio_df['drawdown'].min() * 100
    
    # 交易统计
    buy_trades = len(trades_df[trades_df['action'] == 'BUY'])
    sell_trades = len(trades_df[trades_df['action'] == 'SELL'])
    
    # 买入持有对比
    buy_hold_return = (portfolio_df['price'].iloc[-1] / portfolio_df['price'].iloc[0] - 1) * 100
    buy_hold_annual = (portfolio_df['price'].iloc[-1] / portfolio_df['price'].iloc[0]) ** (1/years) - 1
    
    print(f"📈 总体表现:")
    print(f"   • 回测期间: {years:.1f} 年")
    print(f"   • 最终资金: {final_cash:,.0f} 港币")
    print(f"   • 总收益: {final_cash - initial_capital:,.0f} 港币")
    print(f"   • 总收益率: {total_return:.2f}%")
    print(f"   • 年化收益率: {annual_return*100:.2f}%")
    print(f"   • 最大回撤: {max_drawdown:.2f}%")
    
    print(f"\n📊 交易统计:")
    print(f"   • 买入次数: {buy_trades}")
    print(f"   • 卖出次数: {sell_trades}")
    print(f"   • 交易频率: {(buy_trades + sell_trades) / years:.1f} 次/年")
    
    print(f"\n📊 策略对比:")
    print(f"   • 买入持有收益率: {buy_hold_return:.2f}%")
    print(f"   • 买入持有年化收益: {buy_hold_annual*100:.2f}%")
    print(f"   • 策略超额收益: {total_return - buy_hold_return:.2f}%")
    
    # 创建简单图表
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 1, 1)
    plt.plot(portfolio_df['date'], portfolio_df['total_value'], label='策略组合', linewidth=2)
    plt.plot(portfolio_df['date'], 
             portfolio_df['price'] * initial_capital / portfolio_df['price'].iloc[0],
             label='买入持有', linewidth=2, alpha=0.7)
    plt.title('组合价值对比')
    plt.ylabel('价值 (港币)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 1, 2)
    plt.fill_between(portfolio_df['date'], portfolio_df['drawdown']*100, 0, 
                     color='red', alpha=0.3, label='回撤')
    plt.title('策略回撤')
    plt.ylabel('回撤 (%)')
    plt.xlabel('日期')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"quick_hsi_backtest_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n💾 图表已保存: {filename}")
    
    return {
        'total_return': total_return,
        'annual_return': annual_return * 100,
        'max_drawdown': max_drawdown,
        'trades': buy_trades + sell_trades,
        'years': years
    }

def main():
    """主函数"""
    print("🎯 快速恒生指数博弈论策略回测")
    print("="*50)
    
    # 测试数据库连接
    db_connected, db_config = test_database_connection()
    
    if db_connected:
        print("\n📊 尝试加载真实数据...")
        data = load_real_data(db_config)
        
        if data is None:
            print("⚠️ 真实数据加载失败，使用模拟数据")
            data = generate_simulation_data()
    else:
        print("⚠️ 数据库连接失败，使用模拟数据")
        data = generate_simulation_data()
    
    # 运行回测
    trades_df, portfolio_df, final_cash = run_backtest(data)
    
    # 分析结果
    results = analyze_results(trades_df, portfolio_df, final_cash, 100000)
    
    print(f"\n🎉 快速回测完成!")
    print(f"💡 年化收益率: {results['annual_return']:.2f}%")
    print(f"💡 最大回撤: {results['max_drawdown']:.2f}%")

if __name__ == "__main__":
    main()
