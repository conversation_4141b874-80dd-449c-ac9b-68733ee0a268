{"database": {"host": "************", "database": "game_theory_trading", "user": "root", "password": "", "charset": "utf8mb4", "port": 3306}, "trading": {"symbol": "HSI50", "lot_size": 0.01, "magic_number": 20250713, "data_collection_interval": 300, "analysis_interval": 1800, "risk_check_interval": 600, "timeframes": ["M10", "M30", "H1"], "max_data_points": 1000}, "strategy_parameters": {"y_buy_threshold": 0.55, "x_buy_threshold": 0.5, "y_sell_threshold": 0.45, "x_sell_threshold": 0.4, "y_strong_sell_threshold": 0.332, "x_strong_sell_threshold": 0.332, "stop_loss": 50, "take_profit": 100, "max_positions": 1, "lookback_period": 30}, "risk_management": {"max_daily_loss": 1000, "max_drawdown": 2000, "max_consecutive_losses": 5, "position_size_limit": 0.1, "margin_level_warning": 200, "margin_level_critical": 150}, "notifications": {"email_enabled": false, "email_smtp": "smtp.gmail.com", "email_port": 587, "email_user": "<EMAIL>", "email_password": "your_app_password", "email_recipients": ["<EMAIL>", "<EMAIL>"], "wechat_enabled": false, "wechat_webhook": "", "telegram_enabled": false, "telegram_bot_token": "", "telegram_chat_id": ""}, "system": {"auto_start": true, "log_level": "INFO", "log_file": "trading_system.log", "backup_enabled": true, "backup_interval": 86400, "backup_path": "./backups/", "data_retention_days": 365, "performance_monitoring": true, "debug_mode": false}, "analysis": {"default_analysis_period": 30, "performance_metrics": ["total_return", "win_rate", "profit_factor", "max_drawdown", "sharpe_ratio", "calmar_ratio"], "chart_themes": "plotly_white", "export_formats": ["html", "png", "xlsx"], "auto_generate_reports": true, "report_schedule": "daily"}, "mt5_integration": {"connection_timeout": 30, "retry_attempts": 3, "retry_delay": 5, "data_validation": true, "sync_interval": 60}, "security": {"api_key_required": false, "api_key": "", "ip_whitelist": [], "rate_limiting": false, "encryption_enabled": false}, "features": {"real_time_monitoring": true, "automated_trading": false, "backtesting": true, "optimization": true, "machine_learning": false, "sentiment_analysis": false}}