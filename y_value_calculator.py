#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Y值（控制系数）计算器
==================

详细展示Y值的完整计算过程：
1. 基础Y值计算
2. 趋势调整
3. 成交量调整
4. 最终Y值合成

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def calculate_y_value_detailed(df):
    """
    详细计算Y值的每个步骤
    
    参数:
    df: DataFrame，包含 Close, Volume 列
    
    返回:
    DataFrame，包含Y值计算的所有中间步骤
    """
    
    print("🧮 开始计算Y值（控制系数）...")
    
    # 第一步：计算移动平均线
    print("\n📊 第一步：计算移动平均线")
    df['MA20'] = df['Close'].rolling(window=20).mean()
    df['MA60'] = df['Close'].rolling(window=60).mean()
    print(f"   • MA20: 20日移动平均线")
    print(f"   • MA60: 60日移动平均线")
    
    # 第二步：计算价格相对位置
    print("\n📈 第二步：计算价格相对MA20位置")
    df['Price_vs_MA20'] = df['Close'] / df['MA20']
    print(f"   • Price_vs_MA20 = Close / MA20")
    print(f"   • 范围: {df['Price_vs_MA20'].min():.3f} - {df['Price_vs_MA20'].max():.3f}")
    
    # 第三步：计算基础Y值
    print("\n🔢 第三步：计算基础Y值")
    df['Base_Y'] = pd.Series(index=df.index)
    
    # 价格高于均线的情况
    mask_above = df['Price_vs_MA20'] >= 1
    df.loc[mask_above, 'Base_Y'] = 0.5 + 0.4 * np.tanh((df.loc[mask_above, 'Price_vs_MA20'] - 1) * 3)
    
    # 价格低于均线的情况
    mask_below = df['Price_vs_MA20'] < 1
    df.loc[mask_below, 'Base_Y'] = 0.5 - 0.4 * np.tanh((1 - df.loc[mask_below, 'Price_vs_MA20']) * 3)
    
    print(f"   • 高于均线时: Base_Y = 0.5 + 0.4 × tanh((Price_vs_MA20 - 1) × 3)")
    print(f"   • 低于均线时: Base_Y = 0.5 - 0.4 × tanh((1 - Price_vs_MA20) × 3)")
    print(f"   • Base_Y范围: {df['Base_Y'].min():.3f} - {df['Base_Y'].max():.3f}")
    
    # 第四步：计算趋势调整
    print("\n📊 第四步：计算趋势调整")
    df['MA_Trend'] = df['MA20'] / df['MA60']
    df['Trend_Adjustment'] = 0.1 * np.tanh((df['MA_Trend'] - 1) * 2)
    
    print(f"   • MA_Trend = MA20 / MA60")
    print(f"   • Trend_Adjustment = 0.1 × tanh((MA_Trend - 1) × 2)")
    print(f"   • 趋势调整范围: {df['Trend_Adjustment'].min():.3f} - {df['Trend_Adjustment'].max():.3f}")
    
    # 第五步：计算成交量调整
    print("\n📈 第五步：计算成交量调整")
    df['Volume_MA20'] = df['Volume'].rolling(window=20).mean()
    df['Volume_Ratio'] = df['Volume'] / df['Volume_MA20']
    df['Volume_Adjustment'] = 0.05 * np.tanh((df['Volume_Ratio'] - 1))
    
    print(f"   • Volume_Ratio = Volume / Volume_MA20")
    print(f"   • Volume_Adjustment = 0.05 × tanh((Volume_Ratio - 1))")
    print(f"   • 成交量调整范围: {df['Volume_Adjustment'].min():.3f} - {df['Volume_Adjustment'].max():.3f}")
    
    # 第六步：合成最终Y值
    print("\n🎯 第六步：合成最终Y值")
    df['Y_Raw'] = df['Base_Y'] + df['Trend_Adjustment'] + df['Volume_Adjustment']
    df['Y_Value'] = df['Y_Raw'].clip(0.1, 0.9)
    
    print(f"   • Y_Raw = Base_Y + Trend_Adjustment + Volume_Adjustment")
    print(f"   • Y_Value = CLIP(Y_Raw, 0.1, 0.9)")
    print(f"   • 最终Y值范围: {df['Y_Value'].min():.3f} - {df['Y_Value'].max():.3f}")
    
    # 处理NaN值
    df = df.fillna(method='ffill').fillna(method='bfill')
    
    print(f"\n✅ Y值计算完成！")
    
    return df

def analyze_y_components(df):
    """分析Y值各组成部分的贡献"""
    print("\n📊 Y值组成部分分析:")
    print("="*50)
    
    # 计算各部分的统计信息
    components = ['Base_Y', 'Trend_Adjustment', 'Volume_Adjustment']
    
    for comp in components:
        if comp in df.columns:
            mean_val = df[comp].mean()
            std_val = df[comp].std()
            min_val = df[comp].min()
            max_val = df[comp].max()
            
            print(f"\n{comp}:")
            print(f"   • 平均值: {mean_val:.4f}")
            print(f"   • 标准差: {std_val:.4f}")
            print(f"   • 范围: {min_val:.4f} - {max_val:.4f}")
    
    # 计算相关性
    print(f"\n📈 Y值与价格相关性:")
    if 'Y_Value' in df.columns and 'Close' in df.columns:
        correlation = df['Y_Value'].corr(df['Close'])
        print(f"   • Y值与收盘价相关性: {correlation:.4f}")

def create_y_value_chart(df, save_path=None):
    """创建Y值分析图表"""
    print("\n📊 生成Y值分析图表...")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(3, 2, figsize=(15, 12))
    fig.suptitle('Y值（控制系数）详细分析', fontsize=16, fontweight='bold')
    
    # 取最近500个数据点用于绘图
    plot_data = df.tail(500).copy()
    
    # 1. 价格与MA20对比
    ax1 = axes[0, 0]
    ax1.plot(plot_data.index, plot_data['Close'], label='收盘价', linewidth=1)
    ax1.plot(plot_data.index, plot_data['MA20'], label='MA20', linewidth=1)
    ax1.set_title('价格与MA20对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Price_vs_MA20比率
    ax2 = axes[0, 1]
    ax2.plot(plot_data.index, plot_data['Price_vs_MA20'], color='orange', linewidth=1)
    ax2.axhline(y=1, color='red', linestyle='--', alpha=0.7)
    ax2.set_title('价格/MA20比率')
    ax2.grid(True, alpha=0.3)
    
    # 3. 基础Y值
    ax3 = axes[1, 0]
    ax3.plot(plot_data.index, plot_data['Base_Y'], color='green', linewidth=1)
    ax3.axhline(y=0.5, color='red', linestyle='--', alpha=0.7)
    ax3.set_title('基础Y值')
    ax3.grid(True, alpha=0.3)
    
    # 4. 趋势调整
    ax4 = axes[1, 1]
    ax4.plot(plot_data.index, plot_data['Trend_Adjustment'], color='blue', linewidth=1)
    ax4.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax4.set_title('趋势调整')
    ax4.grid(True, alpha=0.3)
    
    # 5. 成交量调整
    ax5 = axes[2, 0]
    ax5.plot(plot_data.index, plot_data['Volume_Adjustment'], color='purple', linewidth=1)
    ax5.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax5.set_title('成交量调整')
    ax5.grid(True, alpha=0.3)
    
    # 6. 最终Y值
    ax6 = axes[2, 1]
    ax6.plot(plot_data.index, plot_data['Y_Value'], color='red', linewidth=2)
    ax6.axhline(y=0.4, color='green', linestyle='--', alpha=0.7, label='高值盈利区')
    ax6.axhline(y=0.333, color='orange', linestyle='--', alpha=0.7, label='控制区下限')
    ax6.axhline(y=0.25, color='red', linestyle='--', alpha=0.7, label='强亏损区')
    ax6.set_title('最终Y值')
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ 图表已保存至: {save_path}")
    
    plt.show()

def demonstrate_y_calculation():
    """演示Y值计算过程"""
    print("🎯 Y值计算演示")
    print("="*50)
    
    # 创建示例数据
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    # 模拟价格数据
    base_price = 20000
    price_changes = np.random.normal(0, 0.02, 100)
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    # 模拟成交量数据
    volumes = np.random.lognormal(15, 0.5, 100)
    
    # 创建DataFrame
    df = pd.DataFrame({
        'Date': dates,
        'Close': prices,
        'Volume': volumes
    })
    
    df.set_index('Date', inplace=True)
    
    # 计算Y值
    df = calculate_y_value_detailed(df)
    
    # 分析结果
    analyze_y_components(df)
    
    # 显示最近几天的详细数据
    print(f"\n📋 最近5天的Y值计算详情:")
    print("-" * 100)
    columns = ['Close', 'MA20', 'Price_vs_MA20', 'Base_Y', 'Trend_Adjustment', 
               'Volume_Adjustment', 'Y_Value']
    
    recent_data = df[columns].tail(5)
    for col in columns:
        if col in recent_data.columns:
            recent_data[col] = recent_data[col].round(4)
    
    print(recent_data.to_string())
    
    # 创建图表
    create_y_value_chart(df, 'Y值计算演示图表.png')
    
    return df

if __name__ == "__main__":
    print("🧮 Y值（控制系数）计算器")
    print("="*50)
    print("📊 这个工具将详细展示Y值的计算过程")
    print("🔢 包括：基础Y值、趋势调整、成交量调整、最终合成")
    
    # 运行演示
    df = demonstrate_y_calculation()
    
    print(f"\n🎉 Y值计算演示完成！")
    print(f"💡 Y值反映了市场的控制程度：")
    print(f"   • Y > 0.4: 高控制度（适合买涨）")
    print(f"   • 0.333 < Y < 0.4: 控股商控制区（观望）")
    print(f"   • Y < 0.25: 低控制度（适合买跌）")
