#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在test表中添加profit价格和loss价格列
=================================

为每条记录添加：
- profit价格：止盈价格
- loss价格：止损价格

根据交易方向和策略区域计算

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

def add_profit_loss_price_columns():
    """在test表中添加profit价格和loss价格列"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("📊 在test表中添加profit价格和loss价格列")
        print("="*60)
        
        # 1. 添加profit价格列
        print("1️⃣ 添加profit价格列...")
        try:
            cursor.execute("""
                ALTER TABLE test 
                ADD COLUMN `profit价格` DECIMAL(10,2) DEFAULT 0 
                COMMENT '止盈价格'
            """)
            print("✅ 成功添加profit价格列")
        except:
            print("ℹ️ profit价格列已存在，跳过添加")
        
        # 2. 添加loss价格列
        print("\n2️⃣ 添加loss价格列...")
        try:
            cursor.execute("""
                ALTER TABLE test 
                ADD COLUMN `loss价格` DECIMAL(10,2) DEFAULT 0 
                COMMENT '止损价格'
            """)
            print("✅ 成功添加loss价格列")
        except:
            print("ℹ️ loss价格列已存在，跳过添加")
        
        # 3. 更新高值盈利区买涨的profit和loss价格
        print("\n3️⃣ 更新高值盈利区买涨的profit和loss价格...")
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 1.02,
                `loss价格` = close * 0.99
            WHERE `控制系数` > 0.43 AND `资金流比例` > 0.43 AND 交易方向 = '买涨'
        """)
        high_profit_rows = cursor.rowcount
        print(f"✅ 更新了 {high_profit_rows} 条高值盈利区买涨记录")
        
        # 4. 更新强亏损区买跌的profit和loss价格
        print("\n4️⃣ 更新强亏损区买跌的profit和loss价格...")
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 0.98,
                `loss价格` = close * 1.01
            WHERE (`控制系数` < 0.25 OR `资金流比例` < 0.25) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4)
              AND 交易方向 = '买跌'
        """)
        strong_loss_rows = cursor.rowcount
        print(f"✅ 更新了 {strong_loss_rows} 条强亏损区买跌记录")
        
        # 5. 更新其他区域买跌的profit和loss价格
        print("\n5️⃣ 更新其他区域买跌的profit和loss价格...")
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 0.99,
                `loss价格` = close * 1.02
            WHERE NOT (`控制系数` > 0.43 AND `资金流比例` > 0.43) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
              AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)
              AND 交易方向 = '买跌'
        """)
        other_rows = cursor.rowcount
        print(f"✅ 更新了 {other_rows} 条其他区域买跌记录")
        
        # 6. 更新控股商控制区观望的profit和loss价格（设为0）
        print("\n6️⃣ 更新控股商控制区观望的profit和loss价格...")
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = 0,
                `loss价格` = 0
            WHERE `控制系数` > 0.333 AND `控制系数` < 0.4 AND 交易方向 = '观望'
        """)
        control_rows = cursor.rowcount
        print(f"✅ 更新了 {control_rows} 条控股商控制区观望记录")
        
        # 提交事务
        connection.commit()
        
        # 7. 验证更新结果
        print("\n7️⃣ 验证profit和loss价格更新结果...")
        cursor.execute("""
            SELECT 交易序号, 开仓日期, close, 平仓价格, 交易方向, `profit价格`, `loss价格`,
                   CASE 
                       WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                       ELSE '其他区域'
                   END AS 策略区域,
                   CASE 
                       WHEN 交易方向 = '观望' THEN '观望'
                       WHEN 交易方向 = '买涨' AND 平仓价格 >= `profit价格` THEN '止盈'
                       WHEN 交易方向 = '买涨' AND 平仓价格 <= `loss价格` THEN '止损'
                       WHEN 交易方向 = '买跌' AND 平仓价格 <= `profit价格` THEN '止盈'
                       WHEN 交易方向 = '买跌' AND 平仓价格 >= `loss价格` THEN '止损'
                       ELSE '到期平仓'
                   END AS 实际结果
            FROM test 
            ORDER BY 交易序号 
            LIMIT 20
        """)
        
        results = cursor.fetchall()
        
        print(f"\n📊 profit和loss价格验证 (前20条记录):")
        print("-" * 140)
        print(f"{'序号':<4} {'日期':<12} {'策略区域':<12} {'方向':<6} {'开仓价':<8} {'平仓价':<8} "
              f"{'profit价格':<10} {'loss价格':<9} {'实际结果':<10}")
        print("-" * 140)
        
        for record in results:
            trade_id, date, open_price, close_price, direction, profit_price, loss_price, zone, result = record
            print(f"{trade_id:<4} {date:<12} {zone:<12} {direction:<6} {float(open_price):<8.2f} {float(close_price):<8.2f} "
                  f"{float(profit_price):<10.2f} {float(loss_price):<9.2f} {result:<10}")
        
        # 8. 统计各种结果
        print("\n8️⃣ 统计profit和loss价格结果...")
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区'
                    WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                    WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                    ELSE '其他区域'
                END AS 策略区域,
                交易方向,
                COUNT(*) as 总次数,
                SUM(CASE 
                    WHEN 交易方向 = '观望' THEN 0
                    WHEN 交易方向 = '买涨' AND 平仓价格 >= `profit价格` THEN 1
                    WHEN 交易方向 = '买跌' AND 平仓价格 <= `profit价格` THEN 1
                    ELSE 0
                END) as 止盈次数,
                SUM(CASE 
                    WHEN 交易方向 = '观望' THEN 0
                    WHEN 交易方向 = '买涨' AND 平仓价格 <= `loss价格` THEN 1
                    WHEN 交易方向 = '买跌' AND 平仓价格 >= `loss价格` THEN 1
                    ELSE 0
                END) as 止损次数,
                AVG(`profit价格`) as 平均profit价格,
                AVG(`loss价格`) as 平均loss价格
            FROM test 
            GROUP BY 策略区域, 交易方向
            ORDER BY 策略区域, 交易方向
        """)
        
        summary_results = cursor.fetchall()
        
        print(f"\n📈 按策略区域统计profit和loss价格:")
        print("-" * 120)
        print(f"{'策略区域':<12} {'方向':<6} {'总次数':<6} {'止盈次数':<8} {'止损次数':<8} "
              f"{'平均profit价格':<12} {'平均loss价格':<12}")
        print("-" * 120)
        
        for record in summary_results:
            zone, direction, total, profit_count, loss_count, avg_profit, avg_loss = record
            profit_rate = profit_count / total * 100 if total > 0 else 0
            loss_rate = loss_count / total * 100 if total > 0 else 0
            
            print(f"{zone:<12} {direction:<6} {total:<6} {profit_count:<8} {loss_count:<8} "
                  f"{float(avg_profit):<12.2f} {float(avg_loss):<12.2f}")
        
        # 9. 检查数据完整性
        print("\n9️⃣ 检查数据完整性...")
        cursor.execute("""
            SELECT COUNT(*) as total_records,
                   SUM(CASE WHEN `profit价格` = 0 AND `loss价格` = 0 AND 交易方向 = '观望' THEN 1 ELSE 0 END) as 观望记录,
                   SUM(CASE WHEN `profit价格` > 0 AND `loss价格` > 0 AND 交易方向 != '观望' THEN 1 ELSE 0 END) as 有效交易记录,
                   SUM(CASE WHEN (`profit价格` = 0 OR `loss价格` = 0) AND 交易方向 != '观望' THEN 1 ELSE 0 END) as 异常记录
            FROM test
        """)
        
        integrity_result = cursor.fetchone()
        total, observe_records, valid_records, anomaly_records = integrity_result
        
        print(f"📊 数据完整性检查:")
        print(f"   • 总记录数: {total}")
        print(f"   • 观望记录: {observe_records} (profit和loss价格为0)")
        print(f"   • 有效交易记录: {valid_records} (profit和loss价格>0)")
        print(f"   • 异常记录: {anomaly_records}")
        
        if anomaly_records == 0:
            print("✅ 所有记录的profit和loss价格都正确")
        else:
            print(f"⚠️ 发现 {anomaly_records} 条异常记录")
        
        connection.close()
        print(f"\n🎉 profit价格和loss价格列添加完成!")
        print(f"📊 总更新记录: {high_profit_rows + strong_loss_rows + other_rows + control_rows} 条")
        
    except Exception as e:
        print(f"❌ 添加profit和loss价格列失败: {e}")

if __name__ == "__main__":
    add_profit_loss_price_columns()
