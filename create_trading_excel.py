#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成交易记录Excel文件
====================

为Cosmoon策略生成包含20条买卖记录的Excel文件
包含详细的交易信息、Y值、X值、E值等

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def get_hk00023_data():
    """获取东亚银行数据"""
    print("📊 获取东亚银行(HK00023)数据...")
    
    ticker = yf.Ticker("0023.HK")
    data = ticker.history(period="1y", interval="1d")  # 获取1年数据
    
    if data.empty:
        print("❌ 数据获取失败")
        return None
    
    data.reset_index(inplace=True)
    data.columns = [col.lower() for col in data.columns]
    
    return data

def calculate_indicators(data):
    """计算技术指标和Y、X值"""
    print("🔢 计算技术指标...")
    
    # 移动平均线
    data['ma_20'] = data['close'].rolling(window=20).mean()
    data['ma_60'] = data['close'].rolling(window=60).mean()
    
    # RSI
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    data['rsi'] = 100 - (100 / (1 + rs))
    
    # 成交量指标
    data['volume_ma'] = data['volume'].rolling(window=20).mean()
    data['volume_ratio'] = data['volume'] / data['volume_ma']
    
    # Y值计算
    price_vs_ma20 = data['close'] / data['ma_20']
    base_y = np.where(price_vs_ma20 >= 1, 
                     0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                     0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
    
    ma_trend = (data['ma_20'] / data['ma_60']).fillna(1)
    trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
    volume_adjustment = 0.05 * np.tanh((data['volume_ratio'] - 1))
    
    data['y_probability'] = base_y + trend_adjustment + volume_adjustment
    data['y_probability'] = np.clip(data['y_probability'], 0.1, 0.9)
    data['y_probability'].fillna(0.5, inplace=True)
    
    # X值计算
    price_change = (data['close'] - data['open']) / data['open']
    money_flow = data['volume'] * price_change
    
    def calc_inflow_ratio(flows):
        if len(flows) == 0 or flows.isna().all():
            return 0.5
        flows = flows.dropna()
        if len(flows) == 0:
            return 0.5
        
        inflows = flows[flows > 0].sum()
        outflows = abs(flows[flows < 0].sum())
        total_flow = inflows + outflows
        
        return inflows / total_flow if total_flow > 0 else 0.5
    
    base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
    rsi_adjustment = 0.3 * (data['rsi'] / 100 - 0.5)
    
    data['inflow_ratio'] = base_x + rsi_adjustment
    data['inflow_ratio'] = np.clip(data['inflow_ratio'], 0.1, 0.9)
    data['inflow_ratio'].fillna(0.5, inplace=True)
    
    # E值计算
    data['e_value'] = (8 * data['inflow_ratio'] * data['y_probability'] - 
                      3 * data['inflow_ratio'] - 3 * data['y_probability'] + 1)
    
    # 策略区域
    conditions = [
        (data['y_probability'] > 0.5) & (data['inflow_ratio'] > 0.5),  # 高值盈利区
        (data['y_probability'] > 0.333) & (data['y_probability'] < 0.4),  # 控股商控制区
        (data['y_probability'] < 0.25) | (data['inflow_ratio'] < 0.25),  # 强亏损区
    ]
    
    choices = ['高值盈利区', '控股商控制区', '强亏损区']
    data['strategy_zone'] = np.select(conditions, choices, default='其他区域')
    
    return data

def generate_trading_signals(data, num_trades=20):
    """生成20条交易信号"""
    print(f"🎯 生成{num_trades}条交易信号...")
    
    trades = []
    capital = 30000  # 初始资金30000港币
    current_cash = capital
    
    # 选择最近的数据进行交易模拟
    recent_data = data.tail(100).copy()  # 取最近100天数据
    
    trade_count = 0
    i = 20  # 从第20天开始，确保有足够的历史数据
    
    while trade_count < num_trades and i < len(recent_data) - 5:
        row = recent_data.iloc[i]
        
        # 确定交易策略
        zone = row['strategy_zone']
        y_val = row['y_probability']
        x_val = row['inflow_ratio']
        e_val = row['e_value']
        price = row['close']
        date = row['date']
        
        # 交易逻辑
        action = None
        direction = None
        reason = None
        
        if zone == '高值盈利区':
            action = '买入'
            direction = '做多'
            reason = f'Y={y_val:.3f}>0.5且X={x_val:.3f}>0.5，E={e_val:.3f}>0'
        elif zone == '强亏损区':
            action = '买入'
            direction = '做多'
            reason = f'Y={y_val:.3f}<0.25或X={x_val:.3f}<0.25，低位反弹机会'
        elif zone == '其他区域':
            action = '卖出'
            direction = '做空'
            reason = f'其他区域，Y={y_val:.3f}，X={x_val:.3f}，做空策略'
        elif zone == '控股商控制区':
            # 跳过控股商控制区
            i += 1
            continue
        
        if action:
            # 计算仓位
            position_ratio = 0.15  # 15%仓位
            position_value = current_cash * position_ratio
            shares = int(position_value / price / 100) * 100  # 整手交易
            actual_value = shares * price
            
            if shares > 0:
                # 模拟持仓3-7天
                holding_days = np.random.randint(3, 8)
                exit_index = min(i + holding_days, len(recent_data) - 1)
                exit_row = recent_data.iloc[exit_index]
                exit_price = exit_row['close']
                exit_date = exit_row['date']
                
                # 计算盈亏
                if direction == '做多':
                    profit = (exit_price - price) * shares
                    profit_pct = (exit_price - price) / price * 100
                else:  # 做空
                    profit = (price - exit_price) * shares
                    profit_pct = (price - exit_price) / price * 100
                
                # 交易成本
                transaction_cost = actual_value * 0.0025 * 2  # 买卖各0.25%
                net_profit = profit - transaction_cost
                
                # 更新现金
                current_cash += net_profit
                
                # 记录交易
                trade_record = {
                    '交易序号': trade_count + 1,
                    '开仓日期': date.strftime('%Y-%m-%d'),
                    '平仓日期': exit_date.strftime('%Y-%m-%d'),
                    '持仓天数': holding_days,
                    '股票代码': 'HK00023',
                    '股票名称': '东亚银行',
                    '交易方向': direction,
                    '开仓价格': round(price, 2),
                    '平仓价格': round(exit_price, 2),
                    '交易股数': shares,
                    '交易金额': round(actual_value, 0),
                    '毛利润': round(profit, 0),
                    '交易成本': round(transaction_cost, 0),
                    '净利润': round(net_profit, 0),
                    '收益率%': round(profit_pct, 2),
                    'Y值': round(y_val, 3),
                    'X值': round(x_val, 3),
                    'E值': round(e_val, 3),
                    '策略区域': zone,
                    '交易理由': reason,
                    '账户余额': round(current_cash, 0)
                }
                
                trades.append(trade_record)
                trade_count += 1
                
                print(f"交易{trade_count}: {date.strftime('%m-%d')} {direction} {shares}股 @ {price:.2f}, 盈亏: {net_profit:+.0f}")
        
        i += np.random.randint(2, 6)  # 随机间隔2-5天
    
    return trades

def create_excel_file(trades):
    """创建Excel文件"""
    print("📄 创建Excel文件...")
    
    # 创建DataFrame
    df = pd.DataFrame(trades)
    
    # 计算汇总统计
    total_trades = len(df)
    winning_trades = len(df[df['净利润'] > 0])
    losing_trades = len(df[df['净利润'] < 0])
    win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
    total_profit = df['净利润'].sum()
    total_return = (df['账户余额'].iloc[-1] / 30000 - 1) * 100 if len(df) > 0 else 0
    
    # 创建汇总信息
    summary_data = {
        '项目': ['初始资金', '最终资金', '总盈亏', '总收益率%', '总交易次数', '盈利次数', '亏损次数', '胜率%', '平均每笔盈亏'],
        '数值': [30000, df['账户余额'].iloc[-1] if len(df) > 0 else 30000, total_profit, round(total_return, 2), 
                total_trades, winning_trades, losing_trades, round(win_rate, 1), round(total_profit/total_trades, 0) if total_trades > 0 else 0]
    }
    summary_df = pd.DataFrame(summary_data)
    
    # 创建Excel文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"Cosmoon策略交易记录_{timestamp}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 写入交易记录
        df.to_excel(writer, sheet_name='交易记录', index=False)
        
        # 写入汇总统计
        summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
        
        # 获取工作簿和工作表
        workbook = writer.book
        worksheet1 = writer.sheets['交易记录']
        worksheet2 = writer.sheets['汇总统计']
        
        # 设置列宽
        column_widths = {
            'A': 8, 'B': 12, 'C': 12, 'D': 8, 'E': 10, 'F': 10, 'G': 8, 'H': 10, 'I': 10, 'J': 10,
            'K': 12, 'L': 10, 'M': 10, 'N': 10, 'O': 10, 'P': 8, 'Q': 8, 'R': 8, 'S': 12, 'T': 30, 'U': 12
        }
        
        for col, width in column_widths.items():
            worksheet1.column_dimensions[col].width = width
        
        worksheet2.column_dimensions['A'].width = 15
        worksheet2.column_dimensions['B'].width = 15
    
    print(f"✅ Excel文件已创建: {filename}")
    return filename

def main():
    """主函数"""
    print("📊 Cosmoon策略交易记录生成器")
    print("="*50)
    print("🎯 生成20条买卖交易记录")
    print("💰 初始资金: 30,000港币")
    print("🏦 标的: 东亚银行(HK00023)")
    
    # 获取数据
    data = get_hk00023_data()
    if data is None:
        return
    
    # 计算指标
    data = calculate_indicators(data)
    
    # 生成交易信号
    trades = generate_trading_signals(data, num_trades=20)
    
    if not trades:
        print("❌ 未生成交易记录")
        return
    
    # 创建Excel文件
    filename = create_excel_file(trades)
    
    print(f"\n🎉 完成！")
    print(f"📄 文件名: {filename}")
    print(f"📊 包含 {len(trades)} 条交易记录")
    print(f"💡 请打开Excel文件查看详细交易信息")

if __name__ == "__main__":
    main()
