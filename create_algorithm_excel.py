#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建包含完整算法的Excel文件
=========================

详细展示：
1. X值计算过程（资金流比例）
2. Y值计算过程（控制系数）
3. E值计算过程（核心公式）
4. 策略区域划分
5. 交易决策逻辑
6. 完整的计算公式

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def create_algorithm_excel():
    """创建包含完整算法的Excel文件"""
    print("📊 创建包含完整算法的Excel文件...")
    
    # 1. 获取恒生指数数据（最近1年作为示例）
    print("📈 获取恒生指数数据...")
    ticker = yf.Ticker("^HSI")
    data = ticker.history(period="1y", interval="1d")
    
    if data.empty:
        print("❌ 数据获取失败")
        return
    
    data.reset_index(inplace=True)
    data.columns = [col.lower() for col in data.columns]

    # 处理时区问题
    if 'date' in data.columns:
        data['date'] = pd.to_datetime(data['date']).dt.tz_localize(None)
    
    # 2. 计算所有技术指标和算法步骤
    print("🧮 计算算法步骤...")
    
    # 基础指标
    data['MA20'] = data['close'].rolling(window=20).mean()
    data['MA60'] = data['close'].rolling(window=60).mean()
    
    # RSI计算
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    data['RSI'] = 100 - (100 / (1 + rs))
    
    # 成交量指标
    data['Volume_MA'] = data['volume'].rolling(window=20).mean()
    data['Volume_Ratio'] = data['volume'] / data['Volume_MA']
    
    # Y值计算步骤
    data['Price_vs_MA20'] = data['close'] / data['MA20']
    data['Base_Y'] = np.where(data['Price_vs_MA20'] >= 1, 
                             0.5 + 0.4 * np.tanh((data['Price_vs_MA20'] - 1) * 3),
                             0.5 - 0.4 * np.tanh((1 - data['Price_vs_MA20']) * 3))
    
    data['MA_Trend'] = (data['MA20'] / data['MA60']).fillna(1)
    data['Trend_Adjustment'] = 0.1 * np.tanh((data['MA_Trend'] - 1) * 2)
    data['Volume_Adjustment'] = 0.05 * np.tanh((data['Volume_Ratio'] - 1))
    
    data['Y_Raw'] = data['Base_Y'] + data['Trend_Adjustment'] + data['Volume_Adjustment']
    data['Y_Value'] = np.clip(data['Y_Raw'], 0.1, 0.9)
    data['Y_Value'].fillna(0.5, inplace=True)
    
    # X值计算步骤
    data['Price_Change'] = (data['close'] - data['open']) / data['open']
    data['Money_Flow'] = data['volume'] * data['Price_Change']
    
    # 计算资金流入流出比例
    def calc_inflow_ratio(flows):
        if len(flows) == 0 or flows.isna().all():
            return 0.5
        flows = flows.dropna()
        if len(flows) == 0:
            return 0.5
        
        inflows = flows[flows > 0].sum()
        outflows = abs(flows[flows < 0].sum())
        total_flow = inflows + outflows
        
        return inflows / total_flow if total_flow > 0 else 0.5
    
    data['Base_X'] = data['Money_Flow'].rolling(window=20).apply(calc_inflow_ratio, raw=False)
    data['RSI_Adjustment'] = 0.3 * (data['RSI'] / 100 - 0.5)
    data['X_Raw'] = data['Base_X'] + data['RSI_Adjustment']
    data['X_Value'] = np.clip(data['X_Raw'], 0.1, 0.9)
    data['X_Value'].fillna(0.5, inplace=True)
    
    # E值计算
    data['E_Value'] = (8 * data['X_Value'] * data['Y_Value'] - 
                      3 * data['X_Value'] - 3 * data['Y_Value'] + 1)
    
    # 策略区域分类
    conditions = [
        (data['Y_Value'] > 0.4) & (data['X_Value'] > 0.4),  # 高值盈利区
        (data['Y_Value'] > 0.333) & (data['Y_Value'] < 0.4),  # 控股商控制区
        (data['Y_Value'] < 0.25) | (data['X_Value'] < 0.25),  # 强亏损区
    ]
    
    choices = ['高值盈利区', '控股商控制区', '强亏损区']
    data['Strategy_Zone'] = np.select(conditions, choices, default='其他区域')
    
    # 交易决策
    data['Trading_Action'] = data['Strategy_Zone'].map({
        '高值盈利区': '买涨',
        '控股商控制区': '观望',
        '强亏损区': '买跌',
        '其他区域': '买跌'
    })
    
    # 止盈止损设置
    data['Take_Profit_Pct'] = data['Strategy_Zone'].map({
        '高值盈利区': 1.6,
        '控股商控制区': 0,
        '强亏损区': 0.8,
        '其他区域': 1.0
    })
    
    data['Stop_Loss_Pct'] = data['Strategy_Zone'].map({
        '高值盈利区': 0.8,
        '控股商控制区': 0,
        '强亏损区': 1.6,
        '其他区域': 2.0
    })
    
    # 3. 创建Excel文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"Cosmoon算法详解_{timestamp}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        
        # 主数据表
        main_data = data[['date', 'open', 'high', 'low', 'close', 'volume',
                         'MA20', 'MA60', 'RSI', 'Volume_MA', 'Volume_Ratio',
                         'Price_vs_MA20', 'Base_Y', 'MA_Trend', 'Trend_Adjustment',
                         'Volume_Adjustment', 'Y_Raw', 'Y_Value',
                         'Price_Change', 'Money_Flow', 'Base_X', 'RSI_Adjustment',
                         'X_Raw', 'X_Value', 'E_Value', 'Strategy_Zone',
                         'Trading_Action', 'Take_Profit_Pct', 'Stop_Loss_Pct']].copy()
        
        # 格式化数值
        numeric_columns = ['open', 'high', 'low', 'close', 'MA20', 'MA60', 'RSI',
                          'Volume_Ratio', 'Price_vs_MA20', 'Base_Y', 'MA_Trend',
                          'Trend_Adjustment', 'Volume_Adjustment', 'Y_Raw', 'Y_Value',
                          'Price_Change', 'Base_X', 'RSI_Adjustment', 'X_Raw',
                          'X_Value', 'E_Value', 'Take_Profit_Pct', 'Stop_Loss_Pct']
        
        for col in numeric_columns:
            if col in main_data.columns:
                main_data[col] = main_data[col].round(4)
        
        main_data.to_excel(writer, sheet_name='完整算法数据', index=False)
        
        # 算法说明表
        algorithm_explanation = pd.DataFrame({
            '步骤': [
                '1. 基础数据',
                '1.1 开盘价',
                '1.2 最高价',
                '1.3 最低价',
                '1.4 收盘价',
                '1.5 成交量',
                '',
                '2. 技术指标',
                '2.1 MA20',
                '2.2 MA60',
                '2.3 RSI',
                '2.4 Volume_MA',
                '2.5 Volume_Ratio',
                '',
                '3. Y值计算（控制系数）',
                '3.1 Price_vs_MA20',
                '3.2 Base_Y',
                '3.3 MA_Trend',
                '3.4 Trend_Adjustment',
                '3.5 Volume_Adjustment',
                '3.6 Y_Raw',
                '3.7 Y_Value',
                '',
                '4. X值计算（资金流比例）',
                '4.1 Price_Change',
                '4.2 Money_Flow',
                '4.3 Base_X',
                '4.4 RSI_Adjustment',
                '4.5 X_Raw',
                '4.6 X_Value',
                '',
                '5. E值计算',
                '5.1 E_Value',
                '',
                '6. 策略区域',
                '6.1 高值盈利区',
                '6.2 控股商控制区',
                '6.3 强亏损区',
                '6.4 其他区域',
                '',
                '7. 交易决策',
                '7.1 买涨',
                '7.2 买跌',
                '7.3 观望',
                '',
                '8. 风险控制',
                '8.1 止盈百分比',
                '8.2 止损百分比'
            ],
            '计算公式': [
                '从yfinance获取恒生指数数据',
                'Open价格',
                'High价格',
                'Low价格',
                'Close价格',
                'Volume成交量',
                '',
                '计算技术分析指标',
                'close.rolling(20).mean()',
                'close.rolling(60).mean()',
                '14日相对强弱指标',
                'volume.rolling(20).mean()',
                'volume / Volume_MA',
                '',
                '控制系数Y的计算过程',
                'close / MA20',
                'IF(Price_vs_MA20>=1, 0.5+0.4*tanh((Price_vs_MA20-1)*3), 0.5-0.4*tanh((1-Price_vs_MA20)*3))',
                'MA20 / MA60',
                '0.1 * tanh((MA_Trend - 1) * 2)',
                '0.05 * tanh((Volume_Ratio - 1))',
                'Base_Y + Trend_Adjustment + Volume_Adjustment',
                'CLIP(Y_Raw, 0.1, 0.9)',
                '',
                '资金流比例X的计算过程',
                '(close - open) / open',
                'volume * Price_Change',
                '20日资金流入流出比例',
                '0.3 * (RSI / 100 - 0.5)',
                'Base_X + RSI_Adjustment',
                'CLIP(X_Raw, 0.1, 0.9)',
                '',
                'Cosmoon核心公式',
                '8 * X_Value * Y_Value - 3 * X_Value - 3 * Y_Value + 1',
                '',
                '基于X、Y值的区域划分',
                'Y > 0.4 AND X > 0.4',
                '0.333 < Y < 0.4',
                'Y < 0.25 OR X < 0.25',
                '其他情况',
                '',
                '根据策略区域的交易决策',
                '高值盈利区执行',
                '强亏损区和其他区域执行',
                '控股商控制区执行',
                '',
                '风险管理参数',
                '高值盈利区1.6%, 强亏损区0.8%, 其他区域1.0%',
                '高值盈利区0.8%, 强亏损区1.6%, 其他区域2.0%'
            ],
            '说明': [
                '恒生指数历史数据',
                '每日开盘价格',
                '每日最高价格',
                '每日最低价格',
                '每日收盘价格',
                '每日成交量',
                '',
                '用于计算X、Y值的基础指标',
                '20日移动平均线',
                '60日移动平均线',
                '衡量超买超卖状态',
                '20日平均成交量',
                '当日成交量相对平均值的比例',
                '',
                '反映市场控制程度的系数',
                '价格相对20日均线的位置',
                '基础Y值，基于价格与均线关系',
                '均线趋势，反映中长期走势',
                '基于趋势的调整系数',
                '基于成交量的调整系数',
                '未限制的Y值',
                '最终Y值，限制在0.1-0.9之间',
                '',
                '反映资金流向的比例',
                '当日价格变化百分比',
                '资金流量=成交量×价格变化',
                '20日内资金流入占总流量的比例',
                '基于RSI的调整',
                '未限制的X值',
                '最终X值，限制在0.1-0.9之间',
                '',
                '您的核心策略公式',
                '综合X、Y值的策略指标',
                '',
                '将市场状态分为四个区域',
                '适合做多的高盈利概率区域',
                '控股商可能操控的观望区域',
                '适合做空的强亏损区域',
                '其他市场状态',
                '',
                '基于区域的具体操作',
                '预期上涨时买入',
                '预期下跌时卖空',
                '不确定时观望',
                '',
                '控制风险的关键参数',
                '达到目标盈利时平仓',
                '达到最大亏损时止损'
            ]
        })
        
        algorithm_explanation.to_excel(writer, sheet_name='算法说明', index=False)
        
        # 策略区域统计
        zone_stats = data.groupby('Strategy_Zone').agg({
            'E_Value': ['count', 'mean', 'min', 'max'],
            'X_Value': 'mean',
            'Y_Value': 'mean'
        }).round(4)
        
        zone_stats.to_excel(writer, sheet_name='策略区域统计')
        
        # 公式详解表
        formula_details = pd.DataFrame({
            '公式名称': [
                'Y值基础公式',
                'Y值趋势调整',
                'Y值成交量调整',
                'Y值最终公式',
                'X值基础公式',
                'X值RSI调整',
                'X值最终公式',
                'E值核心公式',
                '高值盈利区条件',
                '控股商控制区条件',
                '强亏损区条件',
                '买涨止盈价格',
                '买涨止损价格',
                '买跌止盈价格',
                '买跌止损价格'
            ],
            'Excel公式': [
                '=IF(Price_vs_MA20>=1, 0.5+0.4*TANH((Price_vs_MA20-1)*3), 0.5-0.4*TANH((1-Price_vs_MA20)*3))',
                '=0.1*TANH((MA_Trend-1)*2)',
                '=0.05*TANH((Volume_Ratio-1))',
                '=MAX(0.1, MIN(0.9, Base_Y+Trend_Adjustment+Volume_Adjustment))',
                '=资金流入/(资金流入+资金流出)',
                '=0.3*(RSI/100-0.5)',
                '=MAX(0.1, MIN(0.9, Base_X+RSI_Adjustment))',
                '=8*X_Value*Y_Value-3*X_Value-3*Y_Value+1',
                '=AND(Y_Value>0.4, X_Value>0.4)',
                '=AND(Y_Value>0.333, Y_Value<0.4)',
                '=OR(Y_Value<0.25, X_Value<0.25)',
                '=开仓价格*(1+止盈百分比/100)',
                '=开仓价格*(1-止损百分比/100)',
                '=开仓价格*(1-止盈百分比/100)',
                '=开仓价格*(1+止损百分比/100)'
            ],
            '参数说明': [
                'Price_vs_MA20: 收盘价/MA20',
                'MA_Trend: MA20/MA60',
                'Volume_Ratio: 当日成交量/20日平均成交量',
                '限制Y值在0.1-0.9之间',
                '20日内资金流入流出比例',
                'RSI: 14日相对强弱指标',
                '限制X值在0.1-0.9之间',
                'Cosmoon博弈论核心公式',
                '双高值区域，适合买涨',
                '控股商可能操控区域',
                '双低值或单低值区域',
                '买涨策略的盈利目标',
                '买涨策略的亏损限制',
                '买跌策略的盈利目标',
                '买跌策略的亏损限制'
            ]
        })
        
        formula_details.to_excel(writer, sheet_name='公式详解', index=False)
        
        # 最近30天详细数据
        recent_data = main_data.tail(30).copy()
        recent_data.to_excel(writer, sheet_name='最近30天数据', index=False)
        
    print(f"✅ 算法Excel文件已创建: {filename}")
    
    # 显示统计信息
    print(f"\n📊 算法统计信息:")
    print(f"• 数据期间: {data['date'].min().strftime('%Y-%m-%d')} 至 {data['date'].max().strftime('%Y-%m-%d')}")
    print(f"• 总记录数: {len(data)}")
    print(f"• Y值范围: {data['Y_Value'].min():.3f} - {data['Y_Value'].max():.3f}")
    print(f"• X值范围: {data['X_Value'].min():.3f} - {data['X_Value'].max():.3f}")
    print(f"• E值范围: {data['E_Value'].min():.3f} - {data['E_Value'].max():.3f}")
    
    print(f"\n📊 策略区域分布:")
    zone_counts = data['Strategy_Zone'].value_counts()
    for zone, count in zone_counts.items():
        percentage = count / len(data) * 100
        print(f"• {zone}: {count}天 ({percentage:.1f}%)")
    
    print(f"\n📊 交易决策分布:")
    action_counts = data['Trading_Action'].value_counts()
    for action, count in action_counts.items():
        percentage = count / len(data) * 100
        print(f"• {action}: {count}天 ({percentage:.1f}%)")
    
    return filename

if __name__ == "__main__":
    print("🧮 Cosmoon算法Excel生成器")
    print("="*50)
    print("📊 将创建包含以下内容的Excel文件:")
    print("   • 完整算法数据")
    print("   • 算法说明")
    print("   • 策略区域统计")
    print("   • 公式详解")
    print("   • 最近30天数据")
    
    filename = create_algorithm_excel()
    
    print(f"\n🎉 Excel文件创建完成!")
    print(f"📁 文件名: {filename}")
    print(f"💡 您可以用Excel打开查看完整的算法计算过程")
