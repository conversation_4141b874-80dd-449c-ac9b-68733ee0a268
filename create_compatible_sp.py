#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建兼容hkhsi50表结构的存储过程
===============================

根据您的表结构创建适配的存储过程：
- Date, Open, High, Low, Close, Volume
- MoneyFlowRatio, Midprice, FullY, Controller, E

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

# 数据库配置
db_config = {
    'host': '************',
    'user': 'root',
    'password': '',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def check_table_structure():
    """检查hkhsi50表结构"""
    print("🔍 检查hkhsi50表结构...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        cursor.execute("DESCRIBE hkhsi50")
        columns = cursor.fetchall()
        
        print(f"📋 当前表结构:")
        for col in columns:
            print(f"   • {col[0]} ({col[1]}) - {col[2]} {col[3]} {col[4]} {col[5]}")
        
        conn.close()
        return [col[0] for col in columns]
        
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return []

def add_missing_columns():
    """添加存储过程需要的列"""
    print("\n🔧 添加存储过程需要的列...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 需要添加的列
        columns_to_add = [
            ('Midprice', 'DECIMAL(15,4)', 'COMMENT "中位价格"'),
            ('Controller', 'INT', 'COMMENT "控股商标识: 0=散户, 1=控股商"'),
            ('i', 'INT AUTO_INCREMENT', 'COMMENT "行号"')
        ]
        
        for col_name, col_type, comment in columns_to_add:
            try:
                # 检查列是否已存在
                cursor.execute(f"""
                    SELECT COUNT(*) FROM information_schema.COLUMNS 
                    WHERE TABLE_SCHEMA='finance' AND TABLE_NAME='hkhsi50' 
                    AND COLUMN_NAME='{col_name}'
                """)
                exists = cursor.fetchone()[0] > 0
                
                if not exists:
                    if col_name == 'i':
                        # 添加自增列需要特殊处理
                        cursor.execute(f"ALTER TABLE hkhsi50 ADD COLUMN {col_name} {col_type} {comment}")
                        cursor.execute("ALTER TABLE hkhsi50 ADD INDEX idx_i (i)")
                    else:
                        cursor.execute(f"ALTER TABLE hkhsi50 ADD COLUMN {col_name} {col_type} {comment}")
                    print(f"✅ 添加列: {col_name}")
                else:
                    print(f"ℹ️  列已存在: {col_name}")
                    
            except Exception as e:
                print(f"❌ 添加列 {col_name} 失败: {e}")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 添加列失败: {e}")
        return False

def initialize_data():
    """初始化数据"""
    print("\n📊 初始化数据...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 计算Midprice（中位价格）
        cursor.execute("""
            UPDATE hkhsi50 SET Midprice = (High + Low) / 2 
            WHERE Midprice IS NULL
        """)
        midprice_updated = cursor.rowcount
        
        # 计算Controller（基于Close vs Midprice的历史中位数）
        # 首先获取记录数
        cursor.execute("SELECT COUNT(*) FROM hkhsi50 WHERE Midprice IS NOT NULL")
        count = cursor.fetchone()[0]

        if count > 0:
            # 计算中位数位置
            median_position = count // 2

            # 获取中位数
            cursor.execute("""
                SELECT Midprice FROM hkhsi50
                WHERE Midprice IS NOT NULL
                ORDER BY Midprice
                LIMIT 1 OFFSET %s
            """, (median_position,))
        
            result = cursor.fetchone()
        else:
            result = None

        if result:
            historical_median = result[0]
            
            # 更新Controller字段
            cursor.execute("""
                UPDATE hkhsi50 SET Controller = CASE 
                    WHEN Close > %s THEN 1  -- 控股商控制
                    WHEN Close < %s THEN 0  -- 散户控制
                    ELSE 0                  -- 默认散户
                END
                WHERE Controller IS NULL
            """, (historical_median, historical_median))
            
            controller_updated = cursor.rowcount
            
            print(f"✅ 数据初始化完成:")
            print(f"   • Midprice更新: {midprice_updated} 条")
            print(f"   • Controller更新: {controller_updated} 条")
            print(f"   • 历史中位数: {historical_median:.2f}")
        else:
            print("❌ 无法计算历史中位数")
            return False
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 初始化数据失败: {e}")
        return False

def create_compatible_stored_procedure():
    """创建兼容的存储过程"""
    print("\n🔧 创建兼容的存储过程...")
    
    # 分别执行SQL语句
    sqls = [
        "DROP PROCEDURE IF EXISTS sp_stock_analysis_with_row_coefficients_v2",
        """
        CREATE PROCEDURE sp_stock_analysis_with_row_coefficients_v2(IN tablename VARCHAR(64))
        BEGIN
            DECLARE stmt_sql TEXT;

            -- 0. 确保临时表不存在
            DROP TEMPORARY TABLE IF EXISTS temp_results;

            -- 1. 创建临时表存储计算结果
            SET stmt_sql = CONCAT('
                CREATE TEMPORARY TABLE temp_results AS
                SELECT
                    Date,
                    ROW_NUMBER() OVER (ORDER BY Date) as row_num,
                    SUM(CASE WHEN Controller = ''1'' THEN 1 ELSE 0 END) OVER (ORDER BY Date) /
                    ROW_NUMBER() OVER (ORDER BY Date) AS row_control_coefficient
                FROM
                    `', tablename, '`
                ORDER BY Date
            ');
            PREPARE stmt FROM stmt_sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            -- 2. 更新原表的Full_Y字段
            SET stmt_sql = CONCAT('
                UPDATE `', tablename, '` t
                JOIN temp_results tr ON t.Date = tr.Date
                SET t.Full_Y = tr.row_control_coefficient
            ');
            PREPARE stmt FROM stmt_sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            -- 3. 重新计算E值
            SET stmt_sql = CONCAT('
                UPDATE `', tablename, '`
                SET E = 8 * MoneyFlowRatio * Full_Y - 3 * MoneyFlowRatio - 3 * Full_Y + 1
                WHERE MoneyFlowRatio IS NOT NULL AND Full_Y IS NOT NULL
            ');
            PREPARE stmt FROM stmt_sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            -- 4. 输出结果
            SET stmt_sql = CONCAT('
                SELECT
                    Date,
                    Close,
                    Midprice,
                    Controller,
                    MoneyFlowRatio,
                    Full_Y,
                    E,
                    CASE
                        WHEN Full_Y > 0.4 AND MoneyFlowRatio > 0.4 THEN ''高值盈利区''
                        WHEN Full_Y > 0.333 AND Full_Y < 0.4 THEN ''控股商控制区''
                        WHEN Full_Y < 0.25 OR MoneyFlowRatio < 0.25 THEN ''强亏损区''
                        ELSE ''其他区域''
                    END AS 策略区域
                FROM
                    `', tablename, '`
                ORDER BY
                    Date DESC
                LIMIT 10
            ');
            PREPARE stmt FROM stmt_sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            -- 5. 清理临时表
            DROP TEMPORARY TABLE IF EXISTS temp_results;

            SELECT CONCAT('存储过程执行完成，已更新表: ', tablename) AS 结果;

        END
        """
    ]
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 执行创建存储过程的SQL
        for sql in sqls:
            sql = sql.strip()
            if sql:
                cursor.execute(sql)
        
        conn.commit()
        print("✅ 兼容存储过程创建成功")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建存储过程失败: {e}")
        return False

def test_compatible_procedure():
    """测试兼容的存储过程"""
    print("\n🧪 测试兼容存储过程...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 调用存储过程
        cursor.execute("CALL sp_stock_analysis_with_row_coefficients_v2('hkhsi50')")

        # 获取结果
        results = []
        try:
            for result in cursor.stored_results():
                rows = result.fetchall()
                results.extend(rows)
        except Exception as e:
            print(f"⚠️  获取存储过程结果时出错: {e}")
            # 直接获取结果
            rows = cursor.fetchall()
            results.extend(rows)
            
            # 显示前几行结果
            if len(rows) > 0:
                print(f"📊 存储过程输出结果:")
                if len(rows[0]) == 8:  # 策略分析结果
                    print(f"{'日期':<12} {'收盘价':<10} {'中位价':<10} {'控股商':<6} {'资金流':<8} {'FullY':<8} {'E值':<8} {'策略区域':<12}")
                    print("-" * 90)
                    for row in rows[:5]:  # 只显示前5行
                        print(f"{str(row[0]):<12} {row[1]:<10.2f} {row[2]:<10.2f} {row[3]:<6} {row[4]:<8.3f} {row[5]:<8.3f} {row[6]:<8.3f} {row[7]:<12}")
                else:
                    for row in rows:
                        print(f"   • {row}")
        
        conn.commit()
        
        # 验证更新结果
        cursor.execute("""
            SELECT COUNT(*) as 总记录数,
                   COUNT(CASE WHEN Full_Y IS NOT NULL THEN 1 END) as FullY非空,
                   COUNT(CASE WHEN E IS NOT NULL THEN 1 END) as E非空,
                   AVG(Full_Y) as FullY平均值,
                   AVG(E) as E平均值
            FROM hkhsi50
        """)
        
        stats = cursor.fetchone()
        print(f"\n📈 更新统计:")
        print(f"   • 总记录数: {stats[0]}")
        print(f"   • FullY非空: {stats[1]}")
        print(f"   • E非空: {stats[2]}")
        print(f"   • FullY平均值: {stats[3]:.4f}")
        print(f"   • E平均值: {stats[4]:.4f}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试存储过程失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 创建兼容hkhsi50表结构的存储过程")
    print("="*60)
    print("📋 目标表结构:")
    print("   • Date, Open, High, Low, Close, Volume")
    print("   • MoneyFlowRatio, Midprice, FullY, Controller, E")
    print("="*60)
    
    # 1. 检查表结构
    columns = check_table_structure()
    if not columns:
        return
    
    # 2. 添加缺失的列
    if not add_missing_columns():
        return
    
    # 3. 初始化数据
    if not initialize_data():
        return
    
    # 4. 创建兼容的存储过程
    if not create_compatible_stored_procedure():
        return
    
    # 5. 测试存储过程
    if test_compatible_procedure():
        print("\n🎉 兼容存储过程创建和测试完成！")
        print("💡 现在可以使用:")
        print("   CALL sp_stock_analysis_with_row_coefficients_v2('hkhsi50');")
    else:
        print("\n❌ 存储过程测试失败")

if __name__ == "__main__":
    main()
