"""
博弈论投资策略系统 - 依赖安装脚本
自动检查和安装所需的Python包
"""

import subprocess
import sys
import importlib

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name.replace('-', '_')
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """主函数"""
    print("🚀 博弈论投资策略系统 - 依赖安装")
    print("="*50)
    
    # 必需的包列表
    required_packages = [
        ("schedule", "schedule"),
        ("mysql-connector-python", "mysql.connector"),
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("matplotlib", "matplotlib"),
        ("plotly", "plotly"),
        ("MetaTrader5", "MetaTrader5"),
        ("openpyxl", "openpyxl"),  # Excel支持
        ("seaborn", "seaborn"),    # 图表美化
    ]
    
    # 可选的包列表
    optional_packages = [
        ("jupyter", "jupyter"),
        ("notebook", "notebook"),
        ("ipywidgets", "ipywidgets"),
    ]
    
    print("📦 检查必需依赖...")
    missing_required = []
    
    for package_name, import_name in required_packages:
        if check_package(package_name, import_name):
            print(f"✅ {package_name} 已安装")
        else:
            print(f"❌ {package_name} 未安装")
            missing_required.append(package_name)
    
    print("\n📦 检查可选依赖...")
    missing_optional = []
    
    for package_name, import_name in optional_packages:
        if check_package(package_name, import_name):
            print(f"✅ {package_name} 已安装")
        else:
            print(f"⚠️ {package_name} 未安装 (可选)")
            missing_optional.append(package_name)
    
    # 安装缺失的必需包
    if missing_required:
        print(f"\n🔧 安装缺失的必需依赖...")
        for package in missing_required:
            print(f"正在安装 {package}...")
            if install_package(package):
                print(f"✅ {package} 安装成功")
            else:
                print(f"❌ {package} 安装失败")
    
    # 询问是否安装可选包
    if missing_optional:
        print(f"\n❓ 是否安装可选依赖? (y/n)")
        choice = input().lower().strip()
        
        if choice in ['y', 'yes', '是']:
            for package in missing_optional:
                print(f"正在安装 {package}...")
                if install_package(package):
                    print(f"✅ {package} 安装成功")
                else:
                    print(f"❌ {package} 安装失败")
    
    print("\n🎯 安装完成！")
    
    # 最终检查
    print("\n📋 最终检查...")
    all_good = True
    
    for package_name, import_name in required_packages:
        if check_package(package_name, import_name):
            print(f"✅ {package_name}")
        else:
            print(f"❌ {package_name}")
            all_good = False
    
    if all_good:
        print("\n🎉 所有必需依赖都已安装！")
        print("现在可以运行博弈论投资策略系统了。")
        print("\n使用方法:")
        print("python system_controller_fixed.py")
    else:
        print("\n⚠️ 仍有依赖未安装，请手动安装:")
        for package_name, import_name in required_packages:
            if not check_package(package_name, import_name):
                print(f"pip install {package_name}")

if __name__ == "__main__":
    main()
