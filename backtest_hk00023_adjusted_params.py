#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调整参数后测试HK00023东亚银行20年历史数据
=====================================

调整后的策略规则：
- 高值盈利区 (Y>0.43, X>0.43): 买涨，止盈+4%，止损-2%
- 控股商控制区 (0.333<Y<0.4): 观望
- 强亏损区 (Y<0.25或X<0.25): 买跌，止盈+2%，止损-4%
- 其他区域: 买跌，止盈+2%，止损-4%

初始资金: 30,000港币
股票: HK00023 (东亚银行)
期间: 20年历史数据

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class HK00023AdjustedBacktester:
    def __init__(self):
        """初始化调整参数的HK00023回测器"""
        self.symbol = "0023.HK"  # 东亚银行
        self.initial_capital = 30000
        self.transaction_cost_rate = 0.0025  # 0.25%交易成本
        
        # 策略参数
        self.high_profit_y = 0.43
        self.high_profit_x = 0.43
        self.control_zone_min = 0.333
        self.control_zone_max = 0.4
        self.strong_loss_y = 0.25
        self.strong_loss_x = 0.25
        
    def fetch_20year_data(self):
        """获取20年历史数据"""
        try:
            print("📊 获取HK00023东亚银行20年历史数据...")
            
            # 计算20年前的日期
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)
            
            print(f"   • 开始日期: {start_date.strftime('%Y-%m-%d')}")
            print(f"   • 结束日期: {end_date.strftime('%Y-%m-%d')}")
            
            # 获取数据
            ticker = yf.Ticker(self.symbol)
            data = ticker.history(start=start_date, end=end_date)
            
            if data.empty:
                print("❌ 无法获取数据")
                return None
            
            print(f"✅ 成功获取 {len(data)} 条历史数据")
            print(f"   • 价格范围: {data['Close'].min():.2f} - {data['Close'].max():.2f}港币")
            
            return data
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None
    
    def calculate_xy_values(self, data):
        """计算X、Y值"""
        try:
            print("\n🧮 计算X、Y值...")
            
            # 计算价格变动
            data['Price_Change'] = data['Close'].pct_change()
            data['Volume_Change'] = data['Volume'].pct_change()
            
            # 计算资金流入流出（简化版本）
            data['Money_Flow'] = data['Close'] * data['Volume']
            data['Money_Flow_Change'] = data['Money_Flow'].pct_change()
            
            # 计算Y值（控制系数）- 使用价格波动率的倒数作为控制系数
            window = 20  # 20日窗口
            data['Price_Volatility'] = data['Price_Change'].rolling(window=window).std()
            data['Y'] = 1 / (1 + data['Price_Volatility'] * 10)  # 归一化到0-1
            
            # 计算X值（资金流比例）- 使用成交量相对强度
            data['Volume_MA'] = data['Volume'].rolling(window=window).mean()
            data['Volume_Ratio'] = data['Volume'] / data['Volume_MA']
            data['X'] = np.tanh(data['Volume_Ratio'] - 1) * 0.5 + 0.5  # 归一化到0-1
            
            # 计算E值
            data['E'] = 8 * data['X'] * data['Y'] - 3 * data['X'] - 3 * data['Y'] + 1
            
            # 删除NaN值
            data = data.dropna()
            
            print(f"✅ 计算完成，有效数据: {len(data)} 条")
            print(f"   • Y值范围: {data['Y'].min():.3f} - {data['Y'].max():.3f}")
            print(f"   • X值范围: {data['X'].min():.3f} - {data['X'].max():.3f}")
            print(f"   • E值范围: {data['E'].min():.3f} - {data['E'].max():.3f}")
            
            return data
            
        except Exception as e:
            print(f"❌ 计算X、Y值失败: {e}")
            return None
    
    def determine_strategy(self, y_val, x_val):
        """确定策略区域和交易方向（调整后参数）"""
        if y_val > self.high_profit_y and x_val > self.high_profit_x:
            return '高值盈利区', '买涨', 0.04, 0.02  # 止盈4%, 止损2%
        elif self.control_zone_min < y_val < self.control_zone_max:
            return '控股商控制区', '观望', 0, 0
        elif y_val < self.strong_loss_y or x_val < self.strong_loss_x:
            return '强亏损区', '买跌', 0.02, 0.04  # 止盈2%, 止损4%
        else:
            return '其他区域', '买跌', 0.02, 0.04  # 止盈2%, 止损4%
    
    def run_backtest(self, data):
        """运行调整参数后的回测"""
        try:
            print("\n🎯 运行调整参数后的20年回测...")
            print("📊 新参数:")
            print("   • 高值盈利区 (买涨): 止盈+4%, 止损-2%")
            print("   • 强亏损区 (买跌): 止盈+2%, 止损-4%")
            print("   • 其他区域 (买跌): 止盈+2%, 止损-4%")
            
            current_capital = self.initial_capital
            position = 0  # 当前持仓
            entry_price = 0
            entry_date = None
            strategy_zone = None
            direction = None
            profit_target = 0
            loss_limit = 0
            
            trades = []
            daily_capital = []
            
            for i, (date, row) in enumerate(data.iterrows()):
                current_price = row['Close']
                y_val = row['Y']
                x_val = row['X']
                e_val = row['E']
                
                # 如果没有持仓，考虑开仓
                if position == 0:
                    zone, trade_direction, take_profit_pct, stop_loss_pct = self.determine_strategy(y_val, x_val)
                    
                    if trade_direction != '观望':
                        # 计算仓位大小（固定100股）
                        shares = 100
                        position_value = shares * current_price
                        
                        if current_capital >= position_value:
                            # 开仓
                            position = shares if trade_direction == '买涨' else -shares
                            entry_price = current_price
                            entry_date = date
                            strategy_zone = zone
                            direction = trade_direction
                            
                            # 计算止盈止损价格
                            if trade_direction == '买涨':
                                profit_target = entry_price * (1 + take_profit_pct)  # 止盈价格
                                loss_limit = entry_price * (1 - stop_loss_pct)      # 止损价格
                            else:  # 买跌
                                profit_target = entry_price * (1 - take_profit_pct)  # 止盈价格
                                loss_limit = entry_price * (1 + stop_loss_pct)      # 止损价格
                            
                            current_capital -= position_value
                
                # 如果有持仓，检查平仓条件
                elif position != 0:
                    should_close = False
                    close_reason = ""
                    
                    if direction == '买涨':
                        if current_price >= profit_target:
                            should_close = True
                            close_reason = "止盈"
                        elif current_price <= loss_limit:
                            should_close = True
                            close_reason = "止损"
                    else:  # 买跌
                        if current_price <= profit_target:
                            should_close = True
                            close_reason = "止盈"
                        elif current_price >= loss_limit:
                            should_close = True
                            close_reason = "止损"
                    
                    # 平仓
                    if should_close:
                        shares = abs(position)
                        close_value = shares * current_price
                        
                        # 计算盈亏
                        if direction == '买涨':
                            gross_profit = (current_price - entry_price) * shares
                        else:  # 买跌
                            gross_profit = (entry_price - current_price) * shares
                        
                        # 扣除交易成本
                        transaction_cost = (shares * entry_price + close_value) * self.transaction_cost_rate
                        net_profit = gross_profit - transaction_cost
                        
                        current_capital += close_value + net_profit
                        
                        # 记录交易
                        trades.append({
                            'entry_date': entry_date,
                            'exit_date': date,
                            'strategy_zone': strategy_zone,
                            'direction': direction,
                            'entry_price': entry_price,
                            'exit_price': current_price,
                            'profit_target': profit_target,
                            'loss_limit': loss_limit,
                            'shares': shares,
                            'gross_profit': gross_profit,
                            'net_profit': net_profit,
                            'close_reason': close_reason,
                            'capital_after': current_capital,
                            'y_val': y_val,
                            'x_val': x_val,
                            'e_val': e_val
                        })
                        
                        # 重置持仓
                        position = 0
                        entry_price = 0
                        entry_date = None
                
                # 记录每日资金
                total_value = current_capital
                if position != 0:
                    total_value += abs(position) * current_price
                daily_capital.append(total_value)
            
            print(f"✅ 调整参数回测完成")
            print(f"   • 总交易次数: {len(trades)}")
            print(f"   • 最终资金: {current_capital:,.0f}港币")
            print(f"   • 总收益: {current_capital - self.initial_capital:+,.0f}港币")
            print(f"   • 总收益率: {(current_capital / self.initial_capital - 1) * 100:+.2f}%")
            
            return trades, daily_capital
            
        except Exception as e:
            print(f"❌ 回测失败: {e}")
            return None, None
    
    def analyze_results(self, trades):
        """分析调整参数后的回测结果"""
        if not trades:
            print("❌ 没有交易记录")
            return
        
        print(f"\n📊 调整参数后详细交易分析:")
        print("="*120)
        
        # 转换为DataFrame
        df = pd.DataFrame(trades)
        
        # 显示前20笔交易
        print(f"前20笔交易记录:")
        print("-" * 120)
        print(f"{'序号':<4} {'策略区域':<12} {'方向':<6} {'开仓价':<8} {'平仓价':<8} "
              f"{'止盈价':<8} {'止损价':<8} {'结果':<8} {'净利润':<8}")
        print("-" * 120)
        
        for i, trade in enumerate(trades[:20]):
            print(f"{i+1:<4} {trade['strategy_zone']:<12} {trade['direction']:<6} "
                  f"{trade['entry_price']:<8.2f} {trade['exit_price']:<8.2f} "
                  f"{trade['profit_target']:<8.2f} {trade['loss_limit']:<8.2f} "
                  f"{trade['close_reason']:<8} {trade['net_profit']:<8.0f}")
        
        # 统计分析
        print(f"\n📈 调整参数后统计分析:")
        print("-" * 80)
        
        total_trades = len(trades)
        winning_trades = len(df[df['net_profit'] > 0])
        losing_trades = len(df[df['net_profit'] < 0])
        
        print(f"• 总交易次数: {total_trades}")
        print(f"• 盈利交易: {winning_trades} ({winning_trades/total_trades*100:.1f}%)")
        print(f"• 亏损交易: {losing_trades} ({losing_trades/total_trades*100:.1f}%)")
        print(f"• 胜率: {winning_trades/total_trades*100:.1f}%")
        
        print(f"• 总盈利: {df['net_profit'].sum():+,.0f}港币")
        print(f"• 平均盈利: {df['net_profit'].mean():+.0f}港币/笔")
        print(f"• 最大盈利: {df['net_profit'].max():+.0f}港币")
        print(f"• 最大亏损: {df['net_profit'].min():+.0f}港币")
        
        # 按策略区域分析
        print(f"\n📊 调整参数后按策略区域分析:")
        print("-" * 80)
        
        for zone in df['strategy_zone'].unique():
            zone_data = df[df['strategy_zone'] == zone]
            count = len(zone_data)
            total_profit = zone_data['net_profit'].sum()
            avg_profit = zone_data['net_profit'].mean()
            take_profits = len(zone_data[zone_data['close_reason'] == '止盈'])
            stop_losses = len(zone_data[zone_data['close_reason'] == '止损'])
            
            # 获取该区域的参数
            if zone == '高值盈利区':
                params = "止盈+4%, 止损-2%"
            elif zone == '控股商控制区':
                params = "观望"
            else:
                params = "止盈+2%, 止损-4%"
            
            print(f"• {zone} ({params}):")
            print(f"  - 交易次数: {count}")
            print(f"  - 总盈亏: {total_profit:+,.0f}港币")
            print(f"  - 平均盈亏: {avg_profit:+.0f}港币")
            print(f"  - 止盈: {take_profits}次, 止损: {stop_losses}次")
            if count > 0:
                print(f"  - 胜率: {take_profits/count*100:.1f}%")
        
        # 对比原参数效果
        print(f"\n🔍 参数调整效果对比:")
        print("-" * 60)
        print(f"• 原参数结果: -31.31% (-9,392港币)")
        print(f"• 新参数结果: {(df['net_profit'].sum()/30000)*100:+.2f}% ({df['net_profit'].sum():+,.0f}港币)")
        improvement = df['net_profit'].sum() - (-9392)
        print(f"• 改进效果: {improvement:+,.0f}港币")
        
        return df

def main():
    """主函数"""
    print("🏦 HK00023东亚银行20年历史回测 (调整参数)")
    print("="*60)
    print("📊 调整后策略参数:")
    print("   • 高值盈利区 (Y>0.43, X>0.43): 买涨，止盈+4%，止损-2%")
    print("   • 控股商控制区 (0.333<Y<0.4): 观望")
    print("   • 强亏损区 (Y<0.25或X<0.25): 买跌，止盈+2%，止损-4%")
    print("   • 其他区域: 买跌，止盈+2%，止损-4%")
    print("   • 初始资金: 30,000港币")
    
    # 创建回测器
    backtester = HK00023AdjustedBacktester()
    
    # 获取20年历史数据
    data = backtester.fetch_20year_data()
    if data is None:
        return
    
    # 计算X、Y值
    data = backtester.calculate_xy_values(data)
    if data is None:
        return
    
    # 运行回测
    trades, daily_capital = backtester.run_backtest(data)
    if trades is None:
        return
    
    # 分析结果
    backtester.analyze_results(trades)
    
    print(f"\n🎉 HK00023东亚银行20年调整参数回测完成!")

if __name__ == "__main__":
    main()
