"""
博弈论投资策略 - 系统控制器 (修复版)
统一管理MT5交易、数据收集、分析监控
"""

import time
import threading
from datetime import datetime, timedelta
import json
import os
import logging

# 尝试导入可选依赖
try:
    import schedule
    SCHEDULE_AVAILABLE = True
except ImportError:
    SCHEDULE_AVAILABLE = False
    print("⚠️ schedule模块未安装，将使用简单的定时器")

try:
    from trading_data_manager import TradingDataManager, MT5DataCollector
    from trading_analyzer import TradingAnalyzer
    from mt5_trade_monitor import MT5TradeMonitor
    MODULES_AVAILABLE = True
except ImportError as e:
    MODULES_AVAILABLE = False
    print(f"⚠️ 导入模块失败: {e}")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('system_controller.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SystemController:
    def __init__(self, config_file='system_config.json'):
        """
        初始化系统控制器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = self.load_config(config_file)
        self.data_manager = None
        self.data_collector = None
        self.analyzer = None
        self.trade_monitor = None
        self.running = False
        self.last_data_collection = None
        self.last_analysis = None
        self.last_risk_check = None
        
        if MODULES_AVAILABLE:
            self.initialize_components()
        else:
            logger.error("❌ 必要模块未安装，请检查依赖")
    
    def load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            "database": {
                "host": "localhost",
                "database": "game_theory_trading",
                "user": "your_username",
                "password": "your_password",
                "charset": "utf8mb4"
            },
            "trading": {
                "symbol": "HSI50",
                "lot_size": 0.01,
                "magic_number": 20250713,
                "data_collection_interval": 300,  # 5分钟
                "analysis_interval": 1800,        # 30分钟
                "risk_check_interval": 600        # 10分钟
            },
            "notifications": {
                "email_enabled": False,
                "email_smtp": "smtp.gmail.com",
                "email_port": 587,
                "email_user": "<EMAIL>",
                "email_password": "your_password",
                "email_recipients": ["<EMAIL>"]
            },
            "system": {
                "auto_start": True,
                "log_level": "INFO",
                "backup_enabled": True,
                "backup_interval": 86400  # 24小时
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    # 合并配置
                    self.merge_config(default_config, user_config)
            except Exception as e:
                logger.error(f"❌ 加载配置文件失败: {e}")
        else:
            # 创建默认配置文件
            try:
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4, ensure_ascii=False)
                logger.info(f"✅ 创建默认配置文件: {config_file}")
            except Exception as e:
                logger.error(f"❌ 创建配置文件失败: {e}")
        
        return default_config
    
    def merge_config(self, default, user):
        """递归合并配置"""
        for key, value in user.items():
            if key in default:
                if isinstance(value, dict) and isinstance(default[key], dict):
                    self.merge_config(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value
    
    def initialize_components(self):
        """初始化系统组件"""
        try:
            # 初始化数据管理器
            self.data_manager = TradingDataManager(self.config['database'])
            
            # 初始化数据收集器
            self.data_collector = MT5DataCollector(self.data_manager)
            
            # 初始化分析器
            self.analyzer = TradingAnalyzer(self.data_manager)

            # 初始化交易监控器
            try:
                self.trade_monitor = MT5TradeMonitor(self.data_manager)
                logger.info("✅ MT5交易监控器初始化成功")
            except Exception as e:
                logger.warning(f"⚠️ MT5交易监控器初始化失败: {e}")
                self.trade_monitor = None

            logger.info("✅ 系统组件初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 系统组件初始化失败: {e}")
            raise
    
    def start_data_collection(self):
        """启动数据收集"""
        logger.info("🔄 启动数据收集...")
        
        try:
            if not self.data_collector:
                logger.error("❌ 数据收集器未初始化")
                return
            
            # 收集市场数据
            self.data_collector.collect_market_data()
            
            # 收集账户信息
            self.data_collector.collect_account_info()
            
            # 记录系统日志
            self.data_manager.log_system_event(
                'INFO', 'DATA_COLLECTOR', '数据收集完成'
            )
            
            self.last_data_collection = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ 数据收集失败: {e}")
            if self.data_manager:
                self.data_manager.log_system_event(
                    'ERROR', 'DATA_COLLECTOR', f'数据收集失败: {e}'
                )
    
    def start_analysis(self):
        """启动数据分析"""
        logger.info("📊 启动数据分析...")
        
        try:
            if not self.analyzer:
                logger.error("❌ 分析器未初始化")
                return
            
            # 分析策略性能
            performance, _ = self.analyzer.analyze_strategy_performance(7)
            
            if performance:
                logger.info(f"📈 策略性能: 胜率{performance['win_rate']:.1f}%, 总盈亏{performance['total_profit']:+.2f}")
                
                # 记录性能到数据库
                self.data_manager.log_system_event(
                    'INFO', 'ANALYZER', '性能分析完成', performance
                )
            
            # 创建性能仪表板
            self.analyzer.create_performance_dashboard(7)
            
            self.last_analysis = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ 数据分析失败: {e}")
            if self.data_manager:
                self.data_manager.log_system_event(
                    'ERROR', 'ANALYZER', f'数据分析失败: {e}'
                )
    
    def check_risks(self):
        """检查风险"""
        logger.info("⚠️ 检查风险...")
        
        try:
            if not self.data_manager:
                logger.error("❌ 数据管理器未初始化")
                return
            
            risks = self.data_manager.check_risk_conditions()
            
            if risks:
                logger.warning(f"⚠️ 发现{len(risks)}个风险警报")
                
                # 发送通知
                if self.config['notifications']['email_enabled']:
                    self.send_risk_notification(risks)
                
                # 记录风险日志
                for risk in risks:
                    self.data_manager.log_system_event(
                        'WARNING', 'RISK_MONITOR', risk['description'], risk
                    )
            else:
                logger.info("✅ 风险检查正常")
            
            self.last_risk_check = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ 风险检查失败: {e}")
            if self.data_manager:
                self.data_manager.log_system_event(
                    'ERROR', 'RISK_MONITOR', f'风险检查失败: {e}'
                )
    
    def send_risk_notification(self, risks):
        """发送风险通知"""
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            # 构建邮件内容
            subject = f"博弈论策略风险警报 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            body = "检测到以下风险:\n\n"
            for risk in risks:
                body += f"• {risk['description']}\n"
                body += f"  风险等级: {risk['level']}\n"
                body += f"  当前值: {risk['current_value']}\n"
                body += f"  阈值: {risk['threshold_value']}\n\n"
            
            # 发送邮件
            msg = MIMEMultipart()
            msg['From'] = self.config['notifications']['email_user']
            msg['Subject'] = subject
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(
                self.config['notifications']['email_smtp'],
                self.config['notifications']['email_port']
            )
            server.starttls()
            server.login(
                self.config['notifications']['email_user'],
                self.config['notifications']['email_password']
            )
            
            for recipient in self.config['notifications']['email_recipients']:
                msg['To'] = recipient
                server.send_message(msg)
            
            server.quit()
            logger.info("✅ 风险通知邮件发送成功")
            
        except Exception as e:
            logger.error(f"❌ 发送风险通知失败: {e}")
    
    def generate_daily_report(self):
        """生成每日报告"""
        logger.info("📄 生成每日报告...")
        
        try:
            if not self.data_manager or not self.analyzer:
                logger.error("❌ 组件未初始化")
                return
            
            # 生成报告
            report = self.data_manager.generate_daily_report()
            
            # 导出Excel
            filename = self.analyzer.export_analysis_to_excel(
                f"daily_report_{datetime.now().strftime('%Y%m%d')}.xlsx"
            )
            
            # 记录日志
            self.data_manager.log_system_event(
                'INFO', 'REPORT_GENERATOR', f'每日报告生成完成: {filename}', report
            )
            
            logger.info(f"✅ 每日报告生成完成: {filename}")
            
        except Exception as e:
            logger.error(f"❌ 生成每日报告失败: {e}")
            if self.data_manager:
                self.data_manager.log_system_event(
                    'ERROR', 'REPORT_GENERATOR', f'每日报告生成失败: {e}'
                )

    def setup_simple_scheduler(self):
        """设置简单定时器（不依赖schedule模块）"""
        logger.info("⏰ 设置简单定时任务...")

        def scheduler_loop():
            data_collection_interval = self.config['trading']['data_collection_interval']
            analysis_interval = self.config['trading']['analysis_interval']
            risk_check_interval = self.config['trading']['risk_check_interval']

            last_data_collection = 0
            last_analysis = 0
            last_risk_check = 0
            last_daily_report = 0

            while self.running:
                current_time = time.time()

                # 数据收集
                if current_time - last_data_collection >= data_collection_interval:
                    self.start_data_collection()
                    last_data_collection = current_time

                # 数据分析
                if current_time - last_analysis >= analysis_interval:
                    self.start_analysis()
                    last_analysis = current_time

                # 风险检查
                if current_time - last_risk_check >= risk_check_interval:
                    self.check_risks()
                    last_risk_check = current_time

                # 每日报告（每天18:00）
                now = datetime.now()
                if (now.hour == 18 and now.minute == 0 and
                    current_time - last_daily_report >= 3600):  # 至少间隔1小时
                    self.generate_daily_report()
                    last_daily_report = current_time

                time.sleep(60)  # 每分钟检查一次

        return scheduler_loop

    def setup_schedule_scheduler(self):
        """设置schedule定时任务"""
        logger.info("⏰ 设置schedule定时任务...")

        # 数据收集 - 每5分钟
        schedule.every(self.config['trading']['data_collection_interval']).seconds.do(
            self.start_data_collection
        )

        # 数据分析 - 每30分钟
        schedule.every(self.config['trading']['analysis_interval']).seconds.do(
            self.start_analysis
        )

        # 风险检查 - 每10分钟
        schedule.every(self.config['trading']['risk_check_interval']).seconds.do(
            self.check_risks
        )

        # 每日报告 - 每天18:00
        schedule.every().day.at("18:00").do(self.generate_daily_report)

        def scheduler_loop():
            while self.running:
                try:
                    schedule.run_pending()
                    time.sleep(1)
                except Exception as e:
                    logger.error(f"❌ 调度器运行错误: {e}")
                    time.sleep(10)

        return scheduler_loop

    def start(self):
        """启动系统"""
        logger.info("🚀 启动博弈论投资策略系统...")

        if not MODULES_AVAILABLE:
            logger.error("❌ 必要模块未安装，无法启动系统")
            return

        try:
            # 启动标志
            self.running = True

            # 立即执行一次数据收集和分析
            self.start_data_collection()
            self.start_analysis()
            self.check_risks()

            # 选择调度器
            if SCHEDULE_AVAILABLE:
                scheduler_loop = self.setup_schedule_scheduler()
                logger.info("✅ 使用schedule模块调度器")
            else:
                scheduler_loop = self.setup_simple_scheduler()
                logger.info("✅ 使用简单定时器调度器")

            # 启动调度器线程
            scheduler_thread = threading.Thread(target=scheduler_loop)
            scheduler_thread.daemon = True
            scheduler_thread.start()

            logger.info("✅ 系统启动成功")

            # 主循环
            try:
                while self.running:
                    time.sleep(60)  # 每分钟检查一次

            except KeyboardInterrupt:
                logger.info("🛑 接收到停止信号")
                self.stop()

        except Exception as e:
            logger.error(f"❌ 系统启动失败: {e}")
            self.stop()

    def stop(self):
        """停止系统"""
        logger.info("🛑 停止博弈论投资策略系统...")

        self.running = False

        # 关闭数据库连接
        if self.data_manager:
            self.data_manager.close()

        logger.info("✅ 系统已停止")

    def status(self):
        """获取系统状态"""
        status_info = {
            'running': self.running,
            'config_loaded': self.config is not None,
            'database_connected': (self.data_manager and
                                 self.data_manager.connection and
                                 self.data_manager.connection.is_connected()),
            'last_data_collection': self.last_data_collection.isoformat() if self.last_data_collection else None,
            'last_analysis': self.last_analysis.isoformat() if self.last_analysis else None,
            'last_risk_check': self.last_risk_check.isoformat() if self.last_risk_check else None,
            'modules_available': MODULES_AVAILABLE,
            'schedule_available': SCHEDULE_AVAILABLE
        }

        return status_info

    def print_status(self):
        """打印系统状态"""
        status = self.status()

        print("\n" + "="*60)
        print("📊 博弈论投资策略系统状态")
        print("="*60)
        print(f"🔄 运行状态: {'运行中' if status['running'] else '已停止'}")
        print(f"📁 配置加载: {'✅' if status['config_loaded'] else '❌'}")
        print(f"🗄️ 数据库连接: {'✅' if status['database_connected'] else '❌'}")
        print(f"📦 模块可用: {'✅' if status['modules_available'] else '❌'}")
        print(f"⏰ Schedule模块: {'✅' if status['schedule_available'] else '❌'}")
        print()
        print("📅 最近活动:")
        print(f"   数据收集: {status['last_data_collection'] or '未执行'}")
        print(f"   数据分析: {status['last_analysis'] or '未执行'}")
        print(f"   风险检查: {status['last_risk_check'] or '未执行'}")
        print("="*60)

def install_dependencies():
    """安装缺失的依赖"""
    print("📦 检查并安装依赖...")

    dependencies = [
        'schedule',
        'mysql-connector-python',
        'pandas',
        'numpy',
        'matplotlib',
        'plotly',
        'MetaTrader5'
    ]

    missing_deps = []

    for dep in dependencies:
        try:
            __import__(dep.replace('-', '_'))
            print(f"✅ {dep} 已安装")
        except ImportError:
            missing_deps.append(dep)
            print(f"❌ {dep} 未安装")

    if missing_deps:
        print(f"\n请安装缺失的依赖:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
    else:
        print("✅ 所有依赖都已安装")
        return True

def main():
    """主函数"""
    print("🚀 博弈论投资策略系统控制器 (修复版)")
    print("="*50)

    # 检查依赖
    if not install_dependencies():
        print("❌ 请先安装缺失的依赖")
        return

    # 创建系统控制器
    controller = SystemController()

    # 打印状态
    controller.print_status()

    try:
        # 启动系统
        controller.start()
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
    except Exception as e:
        print(f"❌ 系统错误: {e}")
    finally:
        controller.stop()

if __name__ == "__main__":
    main()
