"""
博弈论投资策略 - 数据管理和分析系统
VSCode + MariaDB + Python
"""

import mysql.connector
from mysql.connector import Error
import pandas as pd
import numpy as np
import MetaTrader5 as mt5
from datetime import datetime, timedelta
import json
import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import schedule
import time
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TradingDataManager:
    def __init__(self, db_config):
        """
        初始化数据管理器
        
        Args:
            db_config: 数据库配置字典
        """
        self.db_config = db_config
        self.connection = None
        self.connect_database()
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logger.info("✅ 数据库连接成功")
                return True
        except Error as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def execute_query(self, query, params=None, fetch=False):
        """执行SQL查询"""
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, params)
            
            if fetch:
                result = cursor.fetchall()
                cursor.close()
                return result
            else:
                self.connection.commit()
                cursor.close()
                return True
                
        except Error as e:
            logger.error(f"❌ SQL执行失败: {e}")
            self.connection.rollback()
            return None
    
    def get_config_value(self, parameter_name, default_value=None):
        """获取配置参数"""
        query = "SELECT parameter_value, parameter_type FROM strategy_config WHERE parameter_name = %s AND is_active = TRUE"
        result = self.execute_query(query, (parameter_name,), fetch=True)
        
        if result:
            value = result[0]['parameter_value']
            param_type = result[0]['parameter_type']
            
            # 类型转换
            if param_type == 'INTEGER':
                return int(value)
            elif param_type == 'DECIMAL':
                return float(value)
            elif param_type == 'BOOLEAN':
                return value.lower() == 'true'
            else:
                return value
        
        return default_value
    
    def save_market_data(self, symbol, timeframe, data):
        """保存市场数据"""
        query = """
        INSERT INTO market_data 
        (symbol, timeframe, datetime, open_price, high_price, low_price, close_price, tick_volume, spread)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        open_price = VALUES(open_price),
        high_price = VALUES(high_price),
        low_price = VALUES(low_price),
        close_price = VALUES(close_price),
        tick_volume = VALUES(tick_volume),
        spread = VALUES(spread)
        """
        
        success_count = 0
        for _, row in data.iterrows():
            params = (
                symbol, timeframe, row['time'], row['open'], row['high'],
                row['low'], row['close'], row['tick_volume'], row.get('spread', 0)
            )
            if self.execute_query(query, params):
                success_count += 1
        
        logger.info(f"✅ 保存市场数据: {success_count}/{len(data)}条")
        return success_count
    
    def save_game_theory_indicators(self, symbol, datetime_val, indicators):
        """保存博弈论指标"""
        query = """
        INSERT INTO game_theory_indicators
        (symbol, datetime, y_probability, x_inflow_ratio, price_trend_factor,
         volume_factor, volatility_factor, hl_factor, position_factor,
         price_volume_match, money_flow_ratio, trade_signal)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            symbol, datetime_val, indicators['y_probability'], indicators['x_inflow_ratio'],
            indicators.get('price_trend_factor'), indicators.get('volume_factor'),
            indicators.get('volatility_factor'), indicators.get('hl_factor'),
            indicators.get('position_factor'), indicators.get('price_volume_match'),
            indicators.get('money_flow_ratio'), indicators['trade_signal']
        )
        
        return self.execute_query(query, params)
    
    def save_trade_record(self, trade_data):
        """保存交易记录"""
        if trade_data['trade_action'] == 'OPEN':
            query = """
            INSERT INTO trades
            (ticket, symbol, trade_type, trade_action, volume, open_price, stop_loss, take_profit,
             open_time, y_value_open, x_value_open, magic_number, trade_comment)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                trade_data['ticket'], trade_data['symbol'], trade_data['trade_type'],
                trade_data['trade_action'], trade_data['volume'], trade_data['open_price'],
                trade_data.get('stop_loss'), trade_data.get('take_profit'),
                trade_data['open_time'], trade_data.get('y_value_open'),
                trade_data.get('x_value_open'), trade_data['magic_number'],
                trade_data.get('comment', '')
            )
        else:  # CLOSE
            query = """
            UPDATE trades SET 
            close_price = %s, close_time = %s, profit = %s, commission = %s, swap = %s,
            duration_minutes = %s, y_value_close = %s, x_value_close = %s, updated_at = CURRENT_TIMESTAMP
            WHERE ticket = %s AND trade_action = 'OPEN'
            """
            duration = None
            if trade_data.get('close_time') and trade_data.get('open_time'):
                duration = int((trade_data['close_time'] - trade_data['open_time']).total_seconds() / 60)
            
            params = (
                trade_data['close_price'], trade_data['close_time'], trade_data['profit'],
                trade_data.get('commission', 0), trade_data.get('swap', 0), duration,
                trade_data.get('y_value_close'), trade_data.get('x_value_close'),
                trade_data['ticket']
            )
        
        return self.execute_query(query, params)
    
    def save_account_status(self, account_info):
        """保存账户状态"""
        query = """
        INSERT INTO account_status
        (datetime, balance, equity, margin_used, free_margin, margin_level, profit, positions_count, orders_count)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            datetime.now(), account_info['balance'], account_info['equity'],
            account_info['margin'], account_info['free_margin'], account_info.get('margin_level'),
            account_info['profit'], account_info.get('positions_count', 0),
            account_info.get('orders_count', 0)
        )
        
        return self.execute_query(query, params)
    
    def log_system_event(self, level, component, message, details=None):
        """记录系统日志"""
        query = """
        INSERT INTO system_logs (log_datetime, log_level, component, log_message, log_details)
        VALUES (%s, %s, %s, %s, %s)
        """
        
        details_json = json.dumps(details) if details else None
        params = (datetime.now(), level, component, message, details_json)
        
        return self.execute_query(query, params)
    
    def check_risk_conditions(self):
        """检查风险条件"""
        risks = []
        
        # 检查当日亏损
        max_daily_loss = self.get_config_value('MAX_DAILY_LOSS', 1000)
        today_profit_query = """
        SELECT SUM(profit) as daily_profit 
        FROM trades 
        WHERE DATE(close_time) = CURDATE() AND action = 'CLOSE'
        """
        result = self.execute_query(today_profit_query, fetch=True)
        
        if result and result[0]['daily_profit']:
            daily_profit = float(result[0]['daily_profit'])
            if daily_profit < -max_daily_loss:
                risks.append({
                    'type': 'DAILY_LOSS_EXCEEDED',
                    'level': 'CRITICAL',
                    'description': f'当日亏损超过限制: {daily_profit:.2f}',
                    'current_value': daily_profit,
                    'threshold_value': -max_daily_loss
                })
        
        # 检查最大回撤
        max_drawdown = self.get_config_value('MAX_DRAWDOWN', 2000)
        account_query = """
        SELECT balance, equity 
        FROM account_status 
        ORDER BY datetime DESC LIMIT 1
        """
        result = self.execute_query(account_query, fetch=True)
        
        if result:
            balance = float(result[0]['balance'])
            equity = float(result[0]['equity'])
            current_drawdown = balance - equity
            
            if current_drawdown > max_drawdown:
                risks.append({
                    'type': 'MAX_DRAWDOWN_EXCEEDED',
                    'level': 'HIGH',
                    'description': f'当前回撤超过限制: {current_drawdown:.2f}',
                    'current_value': current_drawdown,
                    'threshold_value': max_drawdown
                })
        
        # 保存风险记录
        for risk in risks:
            self.save_risk_alert(risk)
        
        return risks
    
    def save_risk_alert(self, risk_data):
        """保存风险警报"""
        query = """
        INSERT INTO risk_monitoring
        (datetime, symbol, risk_level, risk_type, risk_description, current_value, threshold_value)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            datetime.now(), 'HSI50', risk_data['level'], risk_data['type'],
            risk_data['description'], risk_data['current_value'], risk_data['threshold_value']
        )
        
        return self.execute_query(query, params)
    
    def get_trading_summary(self, days=30):
        """获取交易汇总"""
        query = """
        SELECT 
            DATE(close_time) as trade_date,
            COUNT(*) as total_trades,
            SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) as winning_trades,
            SUM(CASE WHEN profit < 0 THEN 1 ELSE 0 END) as losing_trades,
            ROUND(SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as win_rate,
            ROUND(SUM(profit), 2) as total_profit,
            ROUND(MAX(profit), 2) as max_profit,
            ROUND(MIN(profit), 2) as max_loss
        FROM trades 
        WHERE trade_action = 'CLOSE'
        AND close_time >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
        GROUP BY DATE(close_time)
        ORDER BY trade_date DESC
        """
        
        result = self.execute_query(query, (days,), fetch=True)
        return pd.DataFrame(result) if result else pd.DataFrame()
    
    def get_current_positions(self):
        """获取当前持仓"""
        query = "SELECT * FROM v_current_positions"
        result = self.execute_query(query, fetch=True)
        return pd.DataFrame(result) if result else pd.DataFrame()
    
    def generate_daily_report(self):
        """生成每日报告"""
        # 获取今日交易汇总
        today_summary = self.get_trading_summary(days=1)
        
        # 获取当前持仓
        positions = self.get_current_positions()
        
        # 获取账户状态
        account_query = """
        SELECT * FROM account_status 
        ORDER BY datetime DESC LIMIT 1
        """
        account_result = self.execute_query(account_query, fetch=True)
        
        # 生成报告
        report = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'trading_summary': today_summary.to_dict('records') if not today_summary.empty else [],
            'current_positions': positions.to_dict('records') if not positions.empty else [],
            'account_status': account_result[0] if account_result else {},
            'risk_alerts': self.check_risk_conditions()
        }
        
        return report
    
    def close(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("✅ 数据库连接已关闭")

class MT5DataCollector:
    def __init__(self, data_manager):
        """
        MT5数据收集器
        
        Args:
            data_manager: TradingDataManager实例
        """
        self.data_manager = data_manager
        self.symbol = "HSI50"
        
    def initialize_mt5(self):
        """初始化MT5连接"""
        if not mt5.initialize():
            logger.error("❌ MT5初始化失败")
            return False
        
        if not mt5.symbol_select(self.symbol, True):
            logger.error(f"❌ 无法选择品种 {self.symbol}")
            return False
        
        logger.info("✅ MT5连接成功")
        return True
    
    def collect_market_data(self, timeframe=mt5.TIMEFRAME_M10, count=100):
        """收集市场数据"""
        if not self.initialize_mt5():
            return False
        
        try:
            # 获取市场数据
            rates = mt5.copy_rates_from_pos(self.symbol, timeframe, 0, count)
            if rates is None:
                logger.error("❌ 获取市场数据失败")
                return False
            
            # 转换为DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            
            # 保存到数据库
            timeframe_str = self.get_timeframe_string(timeframe)
            saved_count = self.data_manager.save_market_data(self.symbol, timeframe_str, df)
            
            logger.info(f"✅ 收集市场数据: {saved_count}条")
            return True
            
        except Exception as e:
            logger.error(f"❌ 收集市场数据失败: {e}")
            return False
        finally:
            mt5.shutdown()
    
    def collect_account_info(self):
        """收集账户信息"""
        if not self.initialize_mt5():
            return False
        
        try:
            account_info = mt5.account_info()
            if account_info is None:
                logger.error("❌ 获取账户信息失败")
                return False
            
            # 获取持仓和订单数量
            positions = mt5.positions_total()
            orders = mt5.orders_total()
            
            account_data = {
                'balance': account_info.balance,
                'equity': account_info.equity,
                'margin': account_info.margin,
                'free_margin': account_info.margin_free,
                'margin_level': account_info.margin_level,
                'profit': account_info.profit,
                'positions_count': positions,
                'orders_count': orders
            }
            
            # 保存到数据库
            self.data_manager.save_account_status(account_data)
            logger.info("✅ 收集账户信息成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 收集账户信息失败: {e}")
            return False
        finally:
            mt5.shutdown()
    
    def get_timeframe_string(self, timeframe):
        """获取时间周期字符串"""
        timeframe_map = {
            mt5.TIMEFRAME_M1: 'M1',
            mt5.TIMEFRAME_M5: 'M5',
            mt5.TIMEFRAME_M10: 'M10',
            mt5.TIMEFRAME_M15: 'M15',
            mt5.TIMEFRAME_M30: 'M30',
            mt5.TIMEFRAME_H1: 'H1',
            mt5.TIMEFRAME_H4: 'H4',
            mt5.TIMEFRAME_D1: 'D1'
        }
        return timeframe_map.get(timeframe, 'M10')
