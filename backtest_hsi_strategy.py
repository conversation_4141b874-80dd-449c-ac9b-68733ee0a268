#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数策略回测程序
==================

策略说明：
- 初始资金：30,000港元
- 每次交易：50个恒指点子
- 分析周期：20年
- 使用E值策略，基于X和Y的分布

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
from datetime import datetime
import sqlite3
import warnings
warnings.filterwarnings('ignore')

class HSIBacktest:
    def __init__(self):
        """初始化恒指回测系统"""
        self.symbol = "^HSI"
        self.initial_capital = 30000
        self.point_value = 50  # 每个恒指点子价值50港元
        self.data = None
        
        # 策略参数
        self.strategy_params = {
            'high_profit_y': 0.4,      # 高值盈利区Y门槛
            'high_profit_x': 0.4,      # 高值盈利区X门槛
            'control_zone_min': 0.333,  # 控股商控制区最小值
            'control_zone_max': 0.4,    # 控股商控制区最大值
            'strong_loss_y': 0.25,     # 强亏损区Y门槛
            'strong_loss_x': 0.25,     # 强亏损区X门槛
            'transaction_cost': 30,     # 每次交易成本（佣金+平台费）
            # 各区域止盈止损
            'high_profit_take_profit': 0.016,   # 高值盈利区止盈+1.6%
            'high_profit_stop_loss': 0.008,     # 高值盈利区止损-0.8%
            'strong_loss_take_profit': 0.008,   # 强亏损区止盈+0.8%
            'strong_loss_stop_loss': 0.016,     # 强亏损区止损-1.6%
            'other_take_profit': 0.01,          # 其他区域止盈+1%
            'other_stop_loss': 0.02,            # 其他区域止损-2%
        }
        
    def fetch_hsi_data(self):
        """获取恒生指数历史数据"""
        print("📈 获取恒生指数20年历史数据...")
        
        try:
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(period="20y", interval="1d")
            
            if self.data.empty:
                print("❌ 数据获取失败：返回空数据")
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取恒生指数数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data):,} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标"""
        print("📊 计算技术指标...")
        
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # Y值计算
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
        
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        # X值计算
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        rsi_adjustment = 0.3 * (self.data['rsi'] / 100 - 0.5)
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        # E值计算
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        # 调整后的策略区域分类
        conditions = [
            (self.data['y_probability'] > self.strategy_params['high_profit_y']) & 
            (self.data['inflow_ratio'] > self.strategy_params['high_profit_x']),  # 高值盈利区
            
            (self.data['y_probability'] > self.strategy_params['control_zone_min']) & 
            (self.data['y_probability'] < self.strategy_params['control_zone_max']),  # 控股商控制区
            
            (self.data['y_probability'] < self.strategy_params['strong_loss_y']) | 
            (self.data['inflow_ratio'] < self.strategy_params['strong_loss_x']),  # 强亏损区
        ]
        
        choices = ['高值盈利区', '控股商控制区', '强亏损区']
        self.data['strategy_zone'] = np.select(conditions, choices, default='其他区域')
        
        print("✅ 指标计算完成")
    
    def backtest(self):
        """执行回测"""
        print("🎯 开始回测...")
        
        trades = []
        current_cash = self.initial_capital
        position = None
        entry_price = None
        entry_date = None
        
        for i in range(60, len(self.data)):
            row = self.data.iloc[i]
            date = row['date']
            price = row['close']
            y_val = row['y_probability']
            x_val = row['inflow_ratio']
            e_val = row['e_value']
            zone = row['strategy_zone']
            
            # 如果有持仓，检查是否需要平仓
            if position:
                profit_points = 0
                if position == '做多':
                    profit_points = price - entry_price
                else:
                    profit_points = entry_price - price
                
                profit_pct = profit_points / entry_price
                
                # 检查止盈止损
                if position == '做多':
                    if profit_pct >= take_profit:
                        exit_reason = '止盈'
                        profit = profit_points * self.point_value
                    elif profit_pct <= -stop_loss:
                        exit_reason = '止损'
                        profit = -stop_loss * entry_price * self.point_value
                    else:
                        continue
                else:  # 做空
                    if profit_pct >= take_profit:
                        exit_reason = '止盈'
                        profit = profit_points * self.point_value
                    elif profit_pct <= -stop_loss:
                        exit_reason = '止损'
                        profit = -stop_loss * entry_price * self.point_value
                    else:
                        continue
                
                # 记录交易
                trade_record = {
                    '开仓日期': entry_date.strftime('%Y-%m-%d'),
                    '平仓日期': date.strftime('%Y-%m-%d'),
                    '交易方向': position,
                    '开仓价格': round(entry_price, 2),
                    '平仓价格': round(price, 2),
                    '点数': round(profit_points, 2),
                    '毛利润': round(profit, 2),
                    '交易成本': self.strategy_params['transaction_cost'],
                    '净利润': round(profit - self.strategy_params['transaction_cost'], 2),
                    '收益率%': round(profit_pct * 100, 2),
                    'Y值': round(y_val, 3),
                    'X值': round(x_val, 3),
                    'E值': round(e_val, 3),
                    '策略区域': zone,
                    '平仓原因': exit_reason
                }
                
                trades.append(trade_record)
                current_cash += trade_record['净利润']
                position = None
                
                continue
            
            # 控股商控制区不开新仓
            if zone == '控股商控制区':
                continue
            
            # 根据区域决定交易方向
            if zone == '高值盈利区':
                position = '做多'
                take_profit = self.strategy_params['high_profit_take_profit']
                stop_loss = self.strategy_params['high_profit_stop_loss']
            elif zone == '强亏损区':
                position = '做空'
                take_profit = self.strategy_params['strong_loss_take_profit']
                stop_loss = self.strategy_params['strong_loss_stop_loss']
            elif zone == '其他区域':
                position = '做空'
                take_profit = self.strategy_params['other_take_profit']
                stop_loss = self.strategy_params['other_stop_loss']
            
            if position:
                entry_price = price
                entry_date = date
        
        return pd.DataFrame(trades)
    
    def analyze_results(self, trades_df):
        """分析回测结果"""
        print("\n📊 回测结果分析")
        print("=" * 50)
        
        # 基本统计
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['净利润'] > 0])
        losing_trades = len(trades_df[trades_df['净利润'] < 0])
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        
        total_profit = trades_df['净利润'].sum()
        max_profit = trades_df['净利润'].max()
        max_loss = trades_df['净利润'].min()
        avg_profit = trades_df['净利润'].mean()
        
        # 按区域分析
        zone_stats = trades_df.groupby('策略区域').agg({
            '净利润': ['count', 'sum', 'mean'],
            '收益率%': 'mean'
        }).round(2)
        
        # 输出结果
        print(f"交易统计:")
        print(f"• 总交易次数: {total_trades}")
        print(f"• 盈利次数: {winning_trades}")
        print(f"• 亏损次数: {losing_trades}")
        print(f"• 胜率: {win_rate:.1f}%")
        print(f"\n盈亏统计:")
        print(f"• 总盈亏: {total_profit:,.0f} 港元")
        print(f"• 最大单笔盈利: {max_profit:,.0f} 港元")
        print(f"• 最大单笔亏损: {max_loss:,.0f} 港元")
        print(f"• 平均每笔盈亏: {avg_profit:,.0f} 港元")
        
        print(f"\n区域分析:")
        print(zone_stats)
        
        # 保存到Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"恒生指数回测结果_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 交易记录
            trades_df.to_excel(writer, sheet_name='交易记录', index=False)
            
            # 汇总统计
            summary_data = {
                '项目': ['总交易次数', '盈利次数', '亏损次数', '胜率(%)', 
                        '总盈亏(港元)', '最大单笔盈利(港元)', '最大单笔亏损(港元)', 
                        '平均每笔盈亏(港元)', '初始资金(港元)', '最终资金(港元)',
                        '总收益率(%)'],
                '数值': [total_trades, winning_trades, losing_trades, round(win_rate, 1),
                        round(total_profit, 0), round(max_profit, 0), round(max_loss, 0),
                        round(avg_profit, 0), self.initial_capital, 
                        round(self.initial_capital + total_profit, 0),
                        round((total_profit / self.initial_capital) * 100, 2)]
            }
            pd.DataFrame(summary_data).to_excel(writer, sheet_name='汇总统计', index=False)
            
            # 区域分析
            zone_stats.to_excel(writer, sheet_name='区域分析')
        
        print(f"\n✅ 详细结果已保存至: {filename}")
        return filename

def main():
    """主函数"""
    print("🏢 恒生指数策略回测系统")
    print("=" * 50)
    print("💰 初始资金: 30,000港元")
    print("📈 每次交易: 50个恒指点子")
    print("📊 分析周期: 20年")
    
    # 创建回测器
    backtester = HSIBacktest()
    
    # 获取数据
    if not backtester.fetch_hsi_data():
        return
    
    # 计算指标
    backtester.calculate_indicators()
    
    # 执行回测
    trades_df = backtester.backtest()
    
    # 分析结果
    if len(trades_df) > 0:
        backtester.analyze_results(trades_df)
    else:
        print("❌ 没有产生任何交易")

if __name__ == "__main__":
    main()
