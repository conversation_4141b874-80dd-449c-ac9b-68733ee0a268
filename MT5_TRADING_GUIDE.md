# 🎯 MT5交易监控系统使用指南

## 📖 系统简介

这是一个完整的MT5交易监控和博弈论策略分析系统，可以：

- 🔍 **实时监控MT5交易**：自动记录所有交易到数据库
- 📊 **博弈论策略分析**：基于Y和X指标的智能交易策略
- 🗄️ **数据库管理**：完整的交易数据存储和分析
- ⚠️ **风险监控**：实时风险评估和报警
- 📈 **性能分析**：详细的交易统计和收益分析

## 🚀 快速开始

### 1. 启动主系统
```bash
python main_launcher.py
```

### 2. 启动MT5监控
```bash
python mt5_trading_launcher.py
```

### 3. 启动完整系统控制器
```bash
python system_controller.py
```

## 📊 核心功能

### 🎯 MT5交易监控
- **实时监控**：每30秒检查一次交易状态
- **自动记录**：所有交易自动保存到数据库
- **账户监控**：实时监控账户余额、净值、保证金
- **持仓管理**：监控当前持仓和挂单状态

### 💡 博弈论策略
- **买入条件**：X>0.4 且 Y>0.4
- **卖出条件**：X<0.4
- **X指标**：资金流入比例
- **Y指标**：博弈论概率

### 🏆 推荐策略
1. **改进版网格+凯利策略** (年化30.60%)
2. **HK2800 ETF策略** (年化46.67%)
3. **优化版Y>X>0.4策略** (年化9.71%)

## ⚙️ 系统配置

### 数据库配置 (system_config.json)
```json
{
    "database": {
        "host": "************",
        "database": "game_theory_trading",
        "user": "root",
        "password": "",
        "port": 3306
    }
}
```

### 交易配置
```json
{
    "trading": {
        "symbol": "HSI50",
        "magic_number": 20250713,
        "monitor_interval": 30,
        "max_positions": 1
    }
}
```

## 🔧 安装要求

### 必需软件
- **MT5终端**：已安装并运行
- **MySQL数据库**：已配置并运行
- **Python 3.7+**：已安装

### Python依赖包
```bash
pip install MetaTrader5 pymysql pandas numpy matplotlib schedule
```

或运行自动安装：
```bash
python install_dependencies.py
```

## 📋 使用流程

### 🎯 日常交易监控
1. 启动MT5终端并登录账户
2. 运行 `python main_launcher.py`
3. 选择 "1. 启动MT5交易监控系统"
4. 系统将自动监控并记录所有交易

### 📊 策略分析
1. 运行 `python main_launcher.py`
2. 选择相应的策略分析功能
3. 查看分析结果和建议

### 🗄️ 数据库管理
1. 运行 `python main_launcher.py`
2. 选择 "7. 数据库状态检查"
3. 查看数据完整性和统计信息

## 📊 数据库表结构

### 主要数据表
- **trades**：交易记录表
- **account_status**：账户状态表
- **market_data**：市场数据表
- **hk2800**：HK2800 ETF数据表
- **risk_monitoring**：风险监控表

### 创建数据库
```bash
mysql -u root -p < database_setup.sql
```

## ⚠️ 重要提醒

### 🔑 成功关键
- ✅ 确保MT5终端正常运行
- ✅ 确保数据库连接正常
- ✅ 严格执行买卖条件
- ✅ 定期检查系统状态
- ✅ 及时响应风险警报

### ❗ 风险提示
- 投资有风险，入市需谨慎
- 建议先小资金测试
- 系统仅供参考，不构成投资建议
- 定期备份重要数据
- 监控系统运行状态

## 🛠️ 故障排除

### MT5连接问题
1. 检查MT5是否正常运行
2. 确认账户已登录
3. 检查交易品种是否可用
4. 重启MT5终端

### 数据库连接问题
1. 检查MySQL服务是否运行
2. 确认数据库配置正确
3. 检查网络连接
4. 验证用户权限

### Python依赖问题
```bash
python install_dependencies.py
```

## 📞 技术支持

如遇到问题，请检查：
1. **系统状态**：运行主启动器检查各组件状态
2. **日志文件**：查看 `mt5_trading.log` 和 `system_controller.log`
3. **配置文件**：确认 `system_config.json` 配置正确
4. **依赖包**：确认所有Python包已正确安装

## 📈 性能监控

### 实时监控指标
- 账户余额和净值变化
- 持仓数量和盈亏状态
- 交易频率和成功率
- 风险指标和警报

### 定期报告
- 每日交易统计报告
- 每周策略性能分析
- 每月风险评估报告

---

**免责声明**：本系统仅供学习和研究使用，投资有风险，入市需谨慎。过往业绩不代表未来表现。
