#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HK00023其他区域优化策略
======================

基于20年回测结果，优化其他区域策略：

原策略问题分析：
- 其他区域做空: 225次，-3,322港币，平均-15港币/笔，胜率44%
- 主要问题: 止损过于频繁，2%止损比1%止盈大

优化策略：
- 高值盈利区 (Y>0.43, X>0.43): 买涨，止盈+2%，止损-1%
- 控股商控制区 (0.333<Y<0.4): 观望
- 强亏损区 (Y<0.25或X<0.25): 买涨，止盈+2%，止损-1%
- 其他区域优化:
  1. 改为买涨策略 (跟随大趋势)
  2. 或者改进做空参数: 止盈+2%，止损-0.5%
  3. 或者添加额外过滤条件

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class HK00023OptimizedOtherZone:
    def __init__(self):
        """初始化其他区域优化策略"""
        self.symbol = "0023.HK"
        self.initial_capital = 30000
        self.data = None

        # 优化策略参数
        self.strategy_params = {
            'high_profit_y': 0.43,
            'high_profit_x': 0.43,
            'control_zone_min': 0.333,
            'control_zone_max': 0.4,
            'strong_loss_y': 0.25,
            'strong_loss_x': 0.25,

            # 做多参数 (高值盈利区和强亏损区)
            'long_take_profit': 0.02,   # 2%止盈
            'long_stop_loss': 0.01,     # 1%止损

            # 其他区域优化参数 - 方案选择
            'other_zone_strategy': 'optimized_short',  # 'long', 'optimized_short', 'filtered_short'

            # 优化做空参数
            'short_take_profit': 0.02,  # 2%止盈 (提高)
            'short_stop_loss': 0.005,   # 0.5%止损 (降低)

            'transaction_cost': 0.0025,
            'position_ratio': 0.08,
        }

    def fetch_hk00023_data(self):
        """获取HK00023数据"""
        print("🏦 获取HK00023东亚银行20年历史数据...")

        try:
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(period="20y", interval="1d")

            if self.data.empty:
                return False

            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]

            print(f"✅ 成功获取HK00023数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data)} 天")

            return True

        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False

    def calculate_indicators(self):
        """计算Y、X、E值和均值回归指标"""
        print("🎯 计算优化策略的Y、X、E值和均值回归指标...")

        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        self.data['ma_120'] = self.data['close'].rolling(window=120).mean()

        # 均值回归中值计算
        self.data['mean_reversion_center'] = (self.data['ma_20'] + self.data['ma_60'] + self.data['ma_120']) / 3
        self.data['price_vs_center'] = self.data['close'] / self.data['mean_reversion_center']
        self.data['deviation_from_center'] = (self.data['close'] - self.data['mean_reversion_center']) / self.data['mean_reversion_center'] * 100

        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))

        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']

        # Y值计算
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        base_y = np.where(price_vs_ma20 >= 1,
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))

        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))

        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)

        # X值计算
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change

        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5

            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows

            return inflows / total_flow if total_flow > 0 else 0.5

        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        rsi_adjustment = 0.3 * (self.data['rsi'] / 100 - 0.5)

        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)

        # E值计算
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] -
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)

        # 策略区域分类
        conditions = [
            (self.data['y_probability'] > self.strategy_params['high_profit_y']) &
            (self.data['inflow_ratio'] > self.strategy_params['high_profit_x']),  # 高值盈利区

            (self.data['y_probability'] > self.strategy_params['control_zone_min']) &
            (self.data['y_probability'] < self.strategy_params['control_zone_max']),  # 控股商控制区

            (self.data['y_probability'] < self.strategy_params['strong_loss_y']) |
            (self.data['inflow_ratio'] < self.strategy_params['strong_loss_x']),  # 强亏损区
        ]

        choices = ['高值盈利区', '控股商控制区', '强亏损区']
        self.data['strategy_zone'] = np.select(conditions, choices, default='其他区域')

        # 添加其他区域的细分过滤条件
        other_zone_mask = self.data['strategy_zone'] == '其他区域'

        # 过滤条件1: RSI过度超买/超卖
        rsi_overbought = self.data['rsi'] > 70
        rsi_oversold = self.data['rsi'] < 30

        # 过滤条件2: 价格偏离度
        price_deviation = abs(self.data['deviation_from_center']) > 3

        # 过滤条件3: 成交量异常
        volume_spike = self.data['volume_ratio'] > 1.5

        # 创建其他区域子分类
        self.data['other_zone_subtype'] = 'normal'
        self.data.loc[other_zone_mask & rsi_overbought & price_deviation, 'other_zone_subtype'] = 'rsi_overbought'
        self.data.loc[other_zone_mask & rsi_oversold & price_deviation, 'other_zone_subtype'] = 'rsi_oversold'
        self.data.loc[other_zone_mask & volume_spike, 'other_zone_subtype'] = 'volume_spike'

        # 统计区域分布
        zone_counts = self.data['strategy_zone'].value_counts()
        total = len(self.data)
        print(f"📊 优化策略区域分布:")
        for zone, count in zone_counts.items():
            print(f"   • {zone}: {count} 天 ({count/total*100:.1f}%)")

        # 统计其他区域子分类
        other_zone_data = self.data[self.data['strategy_zone'] == '其他区域']
        if len(other_zone_data) > 0:
            subtype_counts = other_zone_data['other_zone_subtype'].value_counts()
            print(f"📊 其他区域子分类:")
            for subtype, count in subtype_counts.items():
                print(f"   • {subtype}: {count} 天 ({count/len(other_zone_data)*100:.1f}%)")

    def test_other_zone_strategies(self):
        """测试不同的其他区域策略"""
        print("🧪 测试不同的其他区域策略...")

        strategies = {
            'original_short': {
                'name': '原策略(做空1%止盈2%止损)',
                'action': 'short',
                'take_profit': 0.01,
                'stop_loss': 0.02
            },
            'optimized_short': {
                'name': '优化做空(做空2%止盈0.5%止损)',
                'action': 'short',
                'take_profit': 0.02,
                'stop_loss': 0.005
            },
            'conservative_short': {
                'name': '保守做空(做空1.5%止盈1%止损)',
                'action': 'short',
                'take_profit': 0.015,
                'stop_loss': 0.01
            },
            'long_strategy': {
                'name': '改为做多(做多2%止盈1%止损)',
                'action': 'long',
                'take_profit': 0.02,
                'stop_loss': 0.01
            },
            'filtered_short': {
                'name': '过滤做空(仅RSI超买时做空)',
                'action': 'filtered_short',
                'take_profit': 0.02,
                'stop_loss': 0.01
            }
        }

        results = {}

        # 获取其他区域数据
        other_zone_data = self.data[self.data['strategy_zone'] == '其他区域'].copy()

        if len(other_zone_data) == 0:
            print("❌ 没有其他区域数据")
            return results

        print(f"📊 其他区域数据: {len(other_zone_data)} 天")

        for strategy_name, strategy_config in strategies.items():
            print(f"\n测试策略: {strategy_config['name']}")

            # 模拟交易
            trades = []
            current_cash = 30000

            # 随机选择100个交易日进行测试
            sample_size = min(100, len(other_zone_data))
            sample_indices = np.random.choice(len(other_zone_data), sample_size, replace=False)
            sample_data = other_zone_data.iloc[sample_indices].copy()

            for i, (_, row) in enumerate(sample_data.iterrows()):
                # 过滤策略
                if strategy_config['action'] == 'filtered_short':
                    if row['rsi'] <= 70:  # 只在RSI超买时做空
                        continue
                    action = 'short'
                else:
                    action = strategy_config['action']

                # 模拟交易
                price = row['close']
                position_value = current_cash * 0.08
                shares = int(position_value / price / 100) * 100
                actual_value = shares * price

                if shares >= 100:
                    # 模拟价格变动
                    price_change = np.random.normal(0, 0.02)  # 2%标准差的随机变动

                    if action == 'long':
                        profit_pct = price_change
                        if profit_pct >= strategy_config['take_profit']:
                            profit_pct = strategy_config['take_profit']
                        elif profit_pct <= -strategy_config['stop_loss']:
                            profit_pct = -strategy_config['stop_loss']
                    else:  # short
                        profit_pct = -price_change
                        if profit_pct >= strategy_config['take_profit']:
                            profit_pct = strategy_config['take_profit']
                        elif profit_pct <= -strategy_config['stop_loss']:
                            profit_pct = -strategy_config['stop_loss']

                    profit = profit_pct * actual_value
                    transaction_cost = actual_value * 0.0025 * 2
                    net_profit = profit - transaction_cost

                    current_cash += net_profit
                    trades.append(net_profit)

            # 计算结果
            if trades:
                total_profit = sum(trades)
                win_rate = len([t for t in trades if t > 0]) / len(trades) * 100
                avg_profit = total_profit / len(trades)

                results[strategy_name] = {
                    'name': strategy_config['name'],
                    'trades': len(trades),
                    'total_profit': total_profit,
                    'avg_profit': avg_profit,
                    'win_rate': win_rate,
                    'final_cash': current_cash
                }

                print(f"   • 交易次数: {len(trades)}")
                print(f"   • 总盈亏: {total_profit:+.0f}港币")
                print(f"   • 平均盈亏: {avg_profit:+.0f}港币")
                print(f"   • 胜率: {win_rate:.1f}%")
                print(f"   • 最终资金: {current_cash:,.0f}港币")
            else:
                print(f"   • 无有效交易")

        return results

    def run_optimized_backtest(self, selected_strategy='optimized_short'):
        """运行优化后的回测"""
        print(f"🎯 运行其他区域优化回测 - 策略: {selected_strategy}")

        trades = []
        current_cash = self.initial_capital

        # 从第120天开始
        start_index = 120
        trading_data = self.data[start_index:].copy().reset_index(drop=True)

        trade_count = 0
        i = 0

        while i < len(trading_data) - 10:
            row = trading_data.iloc[i]

            date = row['date']
            price = row['close']
            y_val = row['y_probability']
            x_val = row['inflow_ratio']
            e_val = row['e_value']
            zone = row['strategy_zone']

            # 跳过控股商控制区
            if zone == '控股商控制区':
                i += 1
                continue

            # 确定交易策略
            action = None
            direction = None
            reason = None
            take_profit = None
            stop_loss = None

            if zone == '高值盈利区':
                action = '买入'
                direction = '做多'
                take_profit = self.strategy_params['long_take_profit']
                stop_loss = self.strategy_params['long_stop_loss']
                reason = f'高值盈利区：Y={y_val:.3f}>0.43且X={x_val:.3f}>0.43，E={e_val:.3f}，买涨策略'
            elif zone == '强亏损区':
                action = '买入'
                direction = '做多'
                take_profit = self.strategy_params['long_take_profit']
                stop_loss = self.strategy_params['long_stop_loss']
                reason = f'强亏损区：Y={y_val:.3f}<0.25或X={x_val:.3f}<0.25，低位反弹，买涨策略'
            elif zone == '其他区域':
                # 根据选择的策略执行
                if selected_strategy == 'long_strategy':
                    action = '买入'
                    direction = '做多'
                    take_profit = self.strategy_params['long_take_profit']
                    stop_loss = self.strategy_params['long_stop_loss']
                    reason = f'其他区域优化：改为做多策略，Y={y_val:.3f}，X={x_val:.3f}，E={e_val:.3f}'
                elif selected_strategy == 'filtered_short':
                    # 只在RSI超买时做空
                    if row['rsi'] > 70:
                        action = '卖出'
                        direction = '做空'
                        take_profit = self.strategy_params['short_take_profit']
                        stop_loss = self.strategy_params['short_stop_loss']
                        reason = f'其他区域过滤做空：RSI={row["rsi"]:.1f}>70，做空策略'
                    else:
                        i += 1
                        continue
                else:  # optimized_short, conservative_short
                    action = '卖出'
                    direction = '做空'
                    take_profit = self.strategy_params['short_take_profit']
                    stop_loss = self.strategy_params['short_stop_loss']
                    reason = f'其他区域优化做空：Y={y_val:.3f}，X={x_val:.3f}，E={e_val:.3f}，优化参数'

            if action and current_cash > 2000:
                # 计算仓位
                position_value = current_cash * self.strategy_params['position_ratio']
                shares = int(position_value / price / 100) * 100
                actual_value = shares * price

                if shares >= 100:
                    # 模拟持仓期
                    holding_days = np.random.randint(3, 10)
                    exit_index = min(i + holding_days, len(trading_data) - 1)
                    exit_row = trading_data.iloc[exit_index]
                    exit_price = exit_row['close']
                    exit_date = exit_row['date']

                    # 计算盈亏
                    if direction == '做多':
                        profit_pct = (exit_price - price) / price
                        if profit_pct >= take_profit:
                            exit_reason = '止盈'
                            profit_pct = take_profit
                            exit_price = price * (1 + take_profit)
                        elif profit_pct <= -stop_loss:
                            exit_reason = '止损'
                            profit_pct = -stop_loss
                            exit_price = price * (1 - stop_loss)
                        else:
                            exit_reason = '到期平仓'

                        profit = profit_pct * actual_value
                    else:  # 做空
                        profit_pct = (price - exit_price) / price
                        if profit_pct >= take_profit:
                            exit_reason = '止盈'
                            profit_pct = take_profit
                            exit_price = price * (1 - take_profit)
                        elif profit_pct <= -stop_loss:
                            exit_reason = '止损'
                            profit_pct = -stop_loss
                            exit_price = price * (1 + stop_loss)
                        else:
                            exit_reason = '到期平仓'

                        profit = profit_pct * actual_value

                    # 交易成本
                    transaction_cost = actual_value * self.strategy_params['transaction_cost'] * 2
                    net_profit = profit - transaction_cost

                    # 更新现金
                    current_cash += net_profit

                    # 计算均值回归相关数据
                    mean_center = row['mean_reversion_center']
                    price_vs_center = row['price_vs_center']
                    deviation = row['deviation_from_center']

                    # 记录交易
                    trade_record = {
                        '交易序号': trade_count + 1,
                        '股票代码': 'HK00023',
                        '股票名称': '东亚银行',
                        '开仓日期': date.strftime('%Y-%m-%d'),
                        '平仓日期': exit_date.strftime('%Y-%m-%d'),
                        '持仓天数': holding_days,
                        '交易方向': direction,
                        '开仓价格': round(price, 2),
                        '平仓价格': round(exit_price, 2),
                        '均值回归中值': round(mean_center, 2),
                        '价格偏离度%': round(deviation, 2),
                        '价格中值比': round(price_vs_center, 3),
                        '交易股数': shares,
                        '交易金额': round(actual_value, 0),
                        '毛利润': round(profit, 0),
                        '交易成本': round(transaction_cost, 0),
                        '净利润': round(net_profit, 0),
                        '收益率%': round(profit_pct * 100, 2),
                        'Y值': round(y_val, 3),
                        'X值': round(x_val, 3),
                        'E值': round(e_val, 3),
                        '策略区域': zone,
                        '交易理由': reason,
                        '平仓原因': exit_reason,
                        '账户余额': round(current_cash, 0),
                        'RSI': round(row['rsi'], 1),
                        '优化策略': selected_strategy
                    }

                    trades.append(trade_record)
                    trade_count += 1

                    if trade_count % 50 == 0:
                        print(f"已完成 {trade_count} 笔交易，当前资金: {current_cash:,.0f}港币")

            # 随机间隔
            i += np.random.randint(2, 8)

        print(f"\n✅ 优化回测完成!")
        print(f"📊 总交易次数: {trade_count}")
        print(f"💰 最终资金: {current_cash:,.0f}港币")
        print(f"📈 总收益: {current_cash - self.initial_capital:+,.0f}港币")

        return trades

    def create_optimized_excel(self, trades, strategy_name):
        """创建优化策略Excel文件"""
        print("📄 创建其他区域优化策略Excel文件...")

        if not trades:
            return None

        # 创建交易记录DataFrame
        df_trades = pd.DataFrame(trades)

        # 计算汇总统计
        total_trades = len(df_trades)
        winning_trades = len(df_trades[df_trades['净利润'] > 0])
        losing_trades = len(df_trades[df_trades['净利润'] < 0])
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        total_profit = df_trades['净利润'].sum()
        final_capital = df_trades['账户余额'].iloc[-1] if len(df_trades) > 0 else self.initial_capital
        total_return = (final_capital / self.initial_capital - 1) * 100

        # 按策略区域分析
        zone_analysis = df_trades.groupby('策略区域').agg({
            '净利润': ['count', 'sum', 'mean'],
            '收益率%': 'mean'
        }).round(2)
        zone_analysis.columns = ['交易次数', '总盈亏', '平均盈亏', '平均收益率%']
        zone_analysis = zone_analysis.reset_index()

        # 创建汇总统计
        summary_data = {
            '项目': [
                '股票代码', '股票名称', '优化策略', '初始资金(港币)', '最终资金(港币)', '总盈亏(港币)',
                '总收益率(%)', '总交易次数', '盈利次数', '亏损次数', '胜率(%)',
                '平均每笔盈亏(港币)', '最大单笔盈利(港币)', '最大单笔亏损(港币)',
                '做多次数', '做空次数', '高值盈利区交易', '强亏损区交易', '其他区域交易',
                '止盈次数', '止损次数', '到期平仓次数'
            ],
            '数值': [
                'HK00023', '东亚银行', f'其他区域优化-{strategy_name}', self.initial_capital, final_capital, total_profit,
                round(total_return, 2), total_trades, winning_trades, losing_trades, round(win_rate, 1),
                round(total_profit/total_trades, 0) if total_trades > 0 else 0,
                df_trades['净利润'].max(), df_trades['净利润'].min(),
                len(df_trades[df_trades['交易方向'] == '做多']),
                len(df_trades[df_trades['交易方向'] == '做空']),
                len(df_trades[df_trades['策略区域'] == '高值盈利区']),
                len(df_trades[df_trades['策略区域'] == '强亏损区']),
                len(df_trades[df_trades['策略区域'] == '其他区域']),
                len(df_trades[df_trades['平仓原因'] == '止盈']),
                len(df_trades[df_trades['平仓原因'] == '止损']),
                len(df_trades[df_trades['平仓原因'] == '到期平仓'])
            ]
        }
        summary_df = pd.DataFrame(summary_data)

        # 创建优化说明
        optimization_explanation = {
            '优化方案': [
                '原策略问题',
                '优化做空策略',
                '改为做多策略',
                '过滤做空策略',
                '保守做空策略'
            ],
            '具体内容': [
                '其他区域做空: 止盈1%止损2%，225次交易亏损3,322港币',
                '其他区域做空: 止盈2%止损0.5%，提高止盈降低止损',
                '其他区域改为做多: 止盈2%止损1%，跟随大趋势',
                '其他区域过滤做空: 仅在RSI>70时做空，减少交易次数',
                '其他区域保守做空: 止盈1.5%止损1%，平衡风险收益'
            ],
            '理论依据': [
                '止损比止盈大，风险收益比不合理',
                '提高止盈目标，严格控制止损，改善风险收益比',
                '其他区域可能仍有上涨动能，改为做多捕捉趋势',
                '只在明确超买信号时做空，提高胜率',
                '在原参数基础上适度调整，降低风险'
            ],
            '预期效果': [
                '平均每笔亏损15港币，胜率44%',
                '减少止损次数，提高单笔盈利',
                '跟随主趋势，可能提高胜率',
                '减少交易次数，但提高质量',
                '平衡风险和收益，稳健策略'
            ]
        }
        optimization_df = pd.DataFrame(optimization_explanation)

        # 创建Excel文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"HK00023其他区域优化策略_{strategy_name}_{timestamp}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 写入各个工作表
            df_trades.to_excel(writer, sheet_name='优化交易记录', index=False)
            summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
            zone_analysis.to_excel(writer, sheet_name='区域分析', index=False)
            optimization_df.to_excel(writer, sheet_name='优化说明', index=False)

            # 设置格式
            workbook = writer.book

            # 交易记录表格式
            worksheet1 = writer.sheets['优化交易记录']
            column_widths = {
                'A': 8, 'B': 10, 'C': 10, 'D': 12, 'E': 12, 'F': 8, 'G': 8, 'H': 10, 'I': 10, 'J': 12, 'K': 12, 'L': 12,
                'M': 10, 'N': 12, 'O': 10, 'P': 10, 'Q': 10, 'R': 10, 'S': 8, 'T': 8, 'U': 8, 'V': 12, 'W': 60, 'X': 12, 'Y': 12, 'Z': 8, 'AA': 15
            }
            for col, width in column_widths.items():
                worksheet1.column_dimensions[col].width = width

        print(f"✅ 其他区域优化Excel文件已创建: {filename}")
        return filename

def main():
    """主函数"""
    print("🏦 HK00023其他区域策略优化")
    print("="*70)
    print("💰 初始资金: 30,000港币")
    print("🎯 目标: 优化其他区域策略")
    print("📊 原问题: 其他区域做空亏损严重")

    # 创建优化器
    optimizer = HK00023OptimizedOtherZone()

    # 获取数据
    if not optimizer.fetch_hk00023_data():
        return

    # 计算指标
    optimizer.calculate_indicators()

    # 测试不同策略
    print("\n" + "="*70)
    print("🧪 测试不同的其他区域策略")
    results = optimizer.test_other_zone_strategies()

    # 显示测试结果
    print("\n📊 策略测试结果汇总:")
    print("="*70)
    for strategy_name, result in results.items():
        if result:
            print(f"• {result['name']}")
            print(f"  交易次数: {result['trades']}, 总盈亏: {result['total_profit']:+.0f}港币")
            print(f"  平均盈亏: {result['avg_profit']:+.0f}港币, 胜率: {result['win_rate']:.1f}%")

    # 选择最佳策略进行完整回测
    if results:
        best_strategy = max(results.keys(), key=lambda x: results[x]['total_profit'] if results[x] else -999999)
        print(f"\n🏆 选择最佳策略进行完整回测: {results[best_strategy]['name']}")

        # 运行完整回测
        trades = optimizer.run_optimized_backtest(best_strategy)

        if trades:
            # 创建Excel文件
            filename = optimizer.create_optimized_excel(trades, best_strategy)

            if filename:
                print(f"\n🎉 其他区域优化策略回测完成!")
                print(f"📄 文件名: {filename}")
                print(f"📊 包含 {len(trades)} 条优化交易记录")
                print(f"💡 请打开Excel文件查看优化结果")

if __name__ == "__main__":
    main()