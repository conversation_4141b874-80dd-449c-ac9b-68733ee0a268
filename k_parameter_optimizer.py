#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K值参数优化器
=============

分析和优化控制系数K值的参数设定：
1. 历史K值分析
2. 最优K值阈值计算
3. 动态K值策略
4. 回测验证

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 数据库配置
db_config = {
    'host': '************',
    'user': 'root',
    'password': '',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def calculate_rolling_k_values():
    """计算滚动K值"""
    print("📊 计算历史滚动K值...")
    
    try:
        conn = pymysql.connect(**db_config)
        
        # 获取历史数据
        df = pd.read_sql("""
            SELECT date, close, `收市`, `中值`, 控股商
            FROM hkhsi50 
            WHERE 控股商 IS NOT NULL
            ORDER BY date
        """, conn)
        
        conn.close()
        
        if df.empty:
            print("❌ 没有找到控股商数据")
            return None
        
        # 计算不同周期的滚动K值
        periods = [20, 60, 120, 252]  # 20日、60日、120日、252日
        
        for period in periods:
            # 计算滚动K值
            df[f'k_{period}d'] = df['控股商'].rolling(window=period).apply(
                lambda x: (x == 1).sum() / len(x) if len(x) > 0 else 0
            )
        
        # 计算价格变化
        df['price_change'] = df['close'].pct_change()
        df['price_change_5d'] = df['close'].pct_change(5)
        df['price_change_20d'] = df['close'].pct_change(20)
        
        print(f"✅ 计算完成，数据期间: {df['date'].min()} 至 {df['date'].max()}")
        print(f"   • 总记录数: {len(df)}")
        
        return df
        
    except Exception as e:
        print(f"❌ 计算滚动K值失败: {e}")
        return None

def analyze_k_thresholds(df):
    """分析K值阈值"""
    print("\n🔍 分析K值阈值...")
    
    # 定义不同的K值阈值
    k_thresholds = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8]
    
    results = []
    
    for threshold in k_thresholds:
        # 基于20日K值的策略
        k_col = 'k_20d'
        
        # 策略信号
        df['signal'] = np.where(df[k_col] > threshold, 1,  # 控股商控制 -> 买涨
                               np.where(df[k_col] < (1-threshold), -1, 0))  # 散户控制 -> 买跌
        
        # 计算策略收益
        df['strategy_return'] = df['signal'].shift(1) * df['price_change']
        
        # 统计结果
        total_return = df['strategy_return'].sum()
        win_rate = (df['strategy_return'] > 0).sum() / (df['strategy_return'] != 0).sum() if (df['strategy_return'] != 0).sum() > 0 else 0
        trade_count = (df['signal'].diff() != 0).sum()
        
        # 最大回撤
        cumulative_return = (1 + df['strategy_return']).cumprod()
        rolling_max = cumulative_return.expanding().max()
        drawdown = (cumulative_return - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        results.append({
            'threshold': threshold,
            'total_return': total_return,
            'annualized_return': total_return * 252 / len(df),
            'win_rate': win_rate,
            'trade_count': trade_count,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': df['strategy_return'].mean() / df['strategy_return'].std() * np.sqrt(252) if df['strategy_return'].std() > 0 else 0
        })
    
    results_df = pd.DataFrame(results)
    
    print(f"📋 K值阈值分析结果:")
    print("-" * 100)
    print(f"{'阈值':<8} {'总收益':<10} {'年化收益':<10} {'胜率':<8} {'交易次数':<8} {'最大回撤':<10} {'夏普比率':<10}")
    print("-" * 100)
    
    for _, row in results_df.iterrows():
        print(f"{row['threshold']:<8.1f} {row['total_return']:<10.4f} {row['annualized_return']:<10.4f} "
              f"{row['win_rate']:<8.2f} {row['trade_count']:<8.0f} {row['max_drawdown']:<10.4f} {row['sharpe_ratio']:<10.4f}")
    
    # 找到最优阈值
    best_threshold = results_df.loc[results_df['sharpe_ratio'].idxmax(), 'threshold']
    print(f"\n🏆 最优K值阈值: {best_threshold}")
    
    return results_df, best_threshold

def analyze_dynamic_k_strategy(df):
    """分析动态K值策略"""
    print("\n🔄 分析动态K值策略...")
    
    # 动态K值策略：根据市场波动性调整阈值
    df['volatility_20d'] = df['price_change'].rolling(20).std()
    df['vol_percentile'] = df['volatility_20d'].rolling(252).rank(pct=True)
    
    # 动态阈值：高波动时提高阈值，低波动时降低阈值
    df['dynamic_threshold'] = 0.3 + 0.4 * df['vol_percentile']  # 0.3-0.7之间
    
    # 动态策略信号
    df['dynamic_signal'] = np.where(
        df['k_20d'] > df['dynamic_threshold'], 1,
        np.where(df['k_20d'] < (1 - df['dynamic_threshold']), -1, 0)
    )
    
    # 计算动态策略收益
    df['dynamic_return'] = df['dynamic_signal'].shift(1) * df['price_change']
    
    # 与固定阈值策略比较
    df['fixed_signal'] = np.where(df['k_20d'] > 0.5, 1,
                                 np.where(df['k_20d'] < 0.5, -1, 0))
    df['fixed_return'] = df['fixed_signal'].shift(1) * df['price_change']
    
    # 统计比较
    dynamic_total = df['dynamic_return'].sum()
    fixed_total = df['fixed_return'].sum()
    
    print(f"📊 动态vs固定策略比较:")
    print(f"   • 动态策略总收益: {dynamic_total:.4f}")
    print(f"   • 固定策略总收益: {fixed_total:.4f}")
    print(f"   • 改善幅度: {(dynamic_total - fixed_total):.4f}")
    
    return df

def recommend_k_parameters(df, best_threshold):
    """推荐K值参数设定"""
    print(f"\n💡 K值参数设定建议:")
    print("="*60)
    
    # 当前市场状态
    current_k = df['k_20d'].iloc[-1]
    current_vol = df['volatility_20d'].iloc[-1]
    
    print(f"📊 当前市场状态:")
    print(f"   • 当前20日K值: {current_k:.4f}")
    print(f"   • 当前波动率: {current_vol:.4f}")
    print(f"   • 最优固定阈值: {best_threshold:.2f}")
    
    # 基于历史分析的建议
    k_stats = {
        '20日K值': df['k_20d'].describe(),
        '60日K值': df['k_60d'].describe(),
        '120日K值': df['k_120d'].describe()
    }
    
    print(f"\n📈 历史K值统计:")
    for period, stats in k_stats.items():
        print(f"   • {period}: 均值={stats['mean']:.3f}, 中位数={stats['50%']:.3f}, "
              f"标准差={stats['std']:.3f}")
    
    # 推荐参数设定
    print(f"\n🎯 推荐K值参数设定:")
    print(f"   1. 固定阈值策略:")
    print(f"      • 买涨阈值: K > {best_threshold:.2f}")
    print(f"      • 买跌阈值: K < {1-best_threshold:.2f}")
    print(f"      • 观望区间: {1-best_threshold:.2f} ≤ K ≤ {best_threshold:.2f}")
    
    print(f"\n   2. 动态阈值策略:")
    print(f"      • 低波动期: K > 0.3 (更敏感)")
    print(f"      • 高波动期: K > 0.7 (更保守)")
    print(f"      • 当前建议阈值: {0.3 + 0.4 * df['vol_percentile'].iloc[-1]:.2f}")
    
    print(f"\n   3. 多周期确认:")
    print(f"      • 主要信号: 20日K值")
    print(f"      • 趋势确认: 60日K值")
    print(f"      • 长期背景: 120日K值")
    
    # 当前交易建议
    current_dynamic_threshold = 0.3 + 0.4 * df['vol_percentile'].iloc[-1]
    
    if current_k > current_dynamic_threshold:
        signal = "买涨"
        confidence = "高" if current_k > 0.6 else "中"
    elif current_k < (1 - current_dynamic_threshold):
        signal = "买跌"
        confidence = "高" if current_k < 0.4 else "中"
    else:
        signal = "观望"
        confidence = "中"
    
    print(f"\n🚦 当前交易建议:")
    print(f"   • 信号: {signal}")
    print(f"   • 置信度: {confidence}")
    print(f"   • 理由: K值={current_k:.3f}, 阈值={current_dynamic_threshold:.3f}")

def create_k_analysis_chart(df, results_df):
    """创建K值分析图表"""
    print(f"\n📊 生成K值分析图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('K值参数分析', fontsize=16, fontweight='bold')
    
    # 1. K值时间序列
    ax1 = axes[0, 0]
    recent_data = df.tail(500)
    ax1.plot(recent_data.index, recent_data['k_20d'], label='20日K值', linewidth=1)
    ax1.plot(recent_data.index, recent_data['k_60d'], label='60日K值', linewidth=1)
    ax1.axhline(y=0.5, color='red', linestyle='--', alpha=0.7, label='中性线')
    ax1.set_title('K值时间序列')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. K值分布
    ax2 = axes[0, 1]
    ax2.hist(df['k_20d'].dropna(), bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax2.axvline(x=df['k_20d'].mean(), color='red', linestyle='--', label=f'均值: {df["k_20d"].mean():.3f}')
    ax2.set_title('20日K值分布')
    ax2.set_xlabel('K值')
    ax2.set_ylabel('频次')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 阈值优化结果
    ax3 = axes[1, 0]
    ax3.plot(results_df['threshold'], results_df['sharpe_ratio'], 'o-', color='green', linewidth=2)
    best_idx = results_df['sharpe_ratio'].idxmax()
    ax3.plot(results_df.loc[best_idx, 'threshold'], results_df.loc[best_idx, 'sharpe_ratio'], 
             'ro', markersize=10, label=f'最优: {results_df.loc[best_idx, "threshold"]:.1f}')
    ax3.set_title('K值阈值优化')
    ax3.set_xlabel('K值阈值')
    ax3.set_ylabel('夏普比率')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 动态阈值
    ax4 = axes[1, 1]
    recent_data = df.tail(500)
    ax4.plot(recent_data.index, recent_data['k_20d'], label='20日K值', linewidth=1)
    ax4.plot(recent_data.index, recent_data['dynamic_threshold'], label='动态阈值', linewidth=1, color='orange')
    ax4.fill_between(recent_data.index, recent_data['dynamic_threshold'], 
                     1-recent_data['dynamic_threshold'], alpha=0.2, color='gray', label='观望区间')
    ax4.set_title('动态K值阈值')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"K值参数分析_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"✅ 图表已保存: {filename}")
    
    plt.show()

def main():
    """主函数"""
    print("🎯 K值参数优化器")
    print("="*50)
    print("📋 功能:")
    print("   • 计算历史滚动K值")
    print("   • 分析最优K值阈值")
    print("   • 设计动态K值策略")
    print("   • 提供参数设定建议")
    print("="*50)
    
    # 1. 计算滚动K值
    df = calculate_rolling_k_values()
    if df is None:
        return
    
    # 2. 分析K值阈值
    results_df, best_threshold = analyze_k_thresholds(df)
    
    # 3. 分析动态K值策略
    df = analyze_dynamic_k_strategy(df)
    
    # 4. 推荐K值参数
    recommend_k_parameters(df, best_threshold)
    
    # 5. 创建分析图表
    create_k_analysis_chart(df, results_df)
    
    # 6. 导出分析结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存K值数据
    k_filename = f"K值历史数据_{timestamp}.xlsx"
    df[['date', 'close', 'k_20d', 'k_60d', 'k_120d', 'dynamic_threshold', 'volatility_20d']].to_excel(k_filename, index=False)
    
    # 保存阈值分析
    threshold_filename = f"K值阈值分析_{timestamp}.xlsx"
    results_df.to_excel(threshold_filename, index=False)
    
    print(f"\n💾 分析结果已导出:")
    print(f"   • K值历史数据: {k_filename}")
    print(f"   • 阈值分析结果: {threshold_filename}")
    
    print(f"\n🎉 K值参数优化完成！")

if __name__ == "__main__":
    main()
