#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
8000港币20年复利回测 - 博弈论策略
================================

使用8000港币初始资金，回测20年历史数据
重点：复利计算，每次盈利后增加下次投资本金

核心策略：
- 买入条件: X>0.4 且 Y>0.4 (资金流入比例>0.4 且 博弈论概率>0.4)
- 卖出条件: X<0.4 (资金流入比例<0.4)
- 复利计算: 每次交易后更新可用资金

作者: 博弈论投资策略团队
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class HKD8000CompoundBacktest:
    def __init__(self):
        """初始化8000港币复利回测系统"""
        self.db_config = {
            'host': '************',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
        
        # 策略参数
        self.strategy_params = {
            'initial_capital': 8000,         # 初始资金8000港币
            'y_buy_threshold': 0.4,          # Y买入阈值
            'x_buy_threshold': 0.4,          # X买入阈值
            'x_sell_threshold': 0.4,         # X卖出阈值
            'position_ratio': 0.95,          # 仓位比例95% (留5%现金)
            'transaction_cost': 0.0025,      # 交易成本0.25% (港股实际成本)
            'min_holding_days': 1,           # 最少持有天数
            'compound_interest': True,       # 启用复利计算
        }
        
        self.data = None
        self.trades = []
        self.daily_portfolio = []
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def load_20year_data(self):
        """加载20年历史数据"""
        print("📊 加载20年历史数据...")
        
        try:
            # 查询20年的数据，优先使用hk00023
            query = """
                SELECT 
                    date,
                    close as price,
                    volume,
                    y_probability,
                    inflow_ratio as x_ratio,
                    high,
                    low,
                    open
                FROM hk00023 
                WHERE date >= DATE_SUB(CURDATE(), INTERVAL 20 YEAR)
                AND y_probability IS NOT NULL 
                AND inflow_ratio IS NOT NULL
                ORDER BY date ASC
            """
            
            self.data = pd.read_sql(query, self.connection)
            
            if self.data.empty:
                print("⚠️ hk00023数据为空，尝试hk2800...")
                query = query.replace('hk00023', 'hk2800')
                self.data = pd.read_sql(query, self.connection)
                
                if not self.data.empty:
                    print("✅ 使用HK2800(盈富基金)数据")
                else:
                    print("❌ 未找到可用的20年历史数据")
                    return False
            else:
                print("✅ 使用HK00023(恒生指数)数据")
            
            # 数据预处理
            self.data['date'] = pd.to_datetime(self.data['date'])
            self.data = self.data.sort_values('date').reset_index(drop=True)
            
            print(f"📈 数据加载完成:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总交易日: {len(self.data)} 天")
            print(f"   • Y值完整性: {self.data['y_probability'].notna().sum()}/{len(self.data)} ({self.data['y_probability'].notna().sum()/len(self.data)*100:.1f}%)")
            print(f"   • X值完整性: {self.data['x_ratio'].notna().sum()}/{len(self.data)} ({self.data['x_ratio'].notna().sum()/len(self.data)*100:.1f}%)")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def run_compound_backtest(self):
        """运行复利回测"""
        print(f"\n🚀 开始8000港币20年复利回测...")
        print("="*60)
        print(f"💰 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"📊 复利计算: {'启用' if self.strategy_params['compound_interest'] else '禁用'}")
        print(f"📈 仓位比例: {self.strategy_params['position_ratio']*100:.1f}%")
        print(f"💸 交易成本: {self.strategy_params['transaction_cost']*100:.2f}%")
        print("="*60)
        
        # 初始化变量
        current_cash = self.strategy_params['initial_capital']  # 当前现金
        current_position = 0  # 当前持仓数量
        position_cost = 0     # 持仓成本
        last_trade_date = None
        
        # 统计变量
        total_trades = 0
        buy_count = 0
        sell_count = 0
        winning_trades = 0
        losing_trades = 0
        
        print(f"开始回测，数据点数: {len(self.data)}")
        
        for i, row in self.data.iterrows():
            date = row['date']
            price = row['price']
            y_prob = row['y_probability']
            x_ratio = row['x_ratio']
            
            # 跳过缺失数据
            if pd.isna(y_prob) or pd.isna(x_ratio) or pd.isna(price):
                continue
            
            # 计算当前总资产
            position_value = current_position * price
            total_assets = current_cash + position_value
            
            # 交易信号判断
            buy_signal = (y_prob > self.strategy_params['y_buy_threshold'] and 
                         x_ratio > self.strategy_params['x_buy_threshold'] and 
                         current_position == 0)  # 无持仓时才能买入
            
            sell_signal = (x_ratio < self.strategy_params['x_sell_threshold'] and 
                          current_position > 0)  # 有持仓时才能卖出
            
            # 检查最少持有期
            can_trade = True
            if last_trade_date:
                days_since_last_trade = (date - last_trade_date).days
                if days_since_last_trade < self.strategy_params['min_holding_days']:
                    can_trade = False
            
            # 执行买入
            if buy_signal and can_trade:
                # 计算买入金额（使用当前全部现金的95%）
                buy_amount = current_cash * self.strategy_params['position_ratio']
                transaction_cost = buy_amount * self.strategy_params['transaction_cost']
                net_buy_amount = buy_amount - transaction_cost
                
                if net_buy_amount > 0:
                    # 计算可买入的股数
                    shares_to_buy = net_buy_amount / price
                    actual_cost = shares_to_buy * price + transaction_cost
                    
                    # 更新持仓和现金
                    current_position = shares_to_buy
                    position_cost = shares_to_buy * price  # 记录成本
                    current_cash -= actual_cost
                    
                    # 记录交易
                    trade_record = {
                        'date': date,
                        'action': 'BUY',
                        'price': price,
                        'shares': shares_to_buy,
                        'amount': actual_cost,
                        'cost': transaction_cost,
                        'y_value': y_prob,
                        'x_value': x_ratio,
                        'cash_before': current_cash + actual_cost,
                        'cash_after': current_cash,
                        'total_assets': total_assets
                    }
                    self.trades.append(trade_record)
                    
                    buy_count += 1
                    total_trades += 1
                    last_trade_date = date
                    
                    print(f"📈 {date.strftime('%Y-%m-%d')} 买入: {shares_to_buy:.0f}股 @ {price:.2f}, 成本: {actual_cost:.0f}")
            
            # 执行卖出
            elif sell_signal and can_trade:
                # 卖出全部持仓
                sell_amount = current_position * price
                transaction_cost = sell_amount * self.strategy_params['transaction_cost']
                net_sell_amount = sell_amount - transaction_cost
                
                # 计算盈亏
                profit = net_sell_amount - position_cost
                profit_pct = (profit / position_cost) * 100 if position_cost > 0 else 0
                
                # 更新现金（复利效应体现在这里）
                current_cash += net_sell_amount
                
                # 记录交易
                trade_record = {
                    'date': date,
                    'action': 'SELL',
                    'price': price,
                    'shares': current_position,
                    'amount': sell_amount,
                    'cost': transaction_cost,
                    'profit': profit,
                    'profit_pct': profit_pct,
                    'y_value': y_prob,
                    'x_value': x_ratio,
                    'cash_before': current_cash - net_sell_amount,
                    'cash_after': current_cash,
                    'total_assets': current_cash  # 卖出后全部为现金
                }
                self.trades.append(trade_record)
                
                # 统计盈亏
                if profit > 0:
                    winning_trades += 1
                else:
                    losing_trades += 1
                
                print(f"📉 {date.strftime('%Y-%m-%d')} 卖出: {current_position:.0f}股 @ {price:.2f}, 盈亏: {profit:+.0f} ({profit_pct:+.1f}%)")
                
                # 清空持仓
                current_position = 0
                position_cost = 0
                sell_count += 1
                total_trades += 1
                last_trade_date = date
            
            # 记录每日组合价值
            current_total = current_cash + (current_position * price)
            daily_record = {
                'date': date,
                'price': price,
                'cash': current_cash,
                'position': current_position,
                'position_value': current_position * price,
                'total_value': current_total,
                'y_value': y_prob,
                'x_value': x_ratio
            }
            self.daily_portfolio.append(daily_record)
        
        # 最终清仓（如果还有持仓）
        if current_position > 0:
            final_price = self.data['price'].iloc[-1]
            final_sell_amount = current_position * final_price
            final_cost = final_sell_amount * self.strategy_params['transaction_cost']
            current_cash += final_sell_amount - final_cost
            
            print(f"🔚 最终清仓: {current_position:.0f}股 @ {final_price:.2f}")
        
        # 计算最终结果
        final_value = current_cash
        total_return = (final_value / self.strategy_params['initial_capital'] - 1) * 100
        
        print(f"\n✅ 回测完成!")
        print(f"📊 交易统计:")
        print(f"   • 买入次数: {buy_count}")
        print(f"   • 卖出次数: {sell_count}")
        print(f"   • 总交易次数: {total_trades}")
        print(f"   • 盈利交易: {winning_trades}")
        print(f"   • 亏损交易: {losing_trades}")
        print(f"   • 胜率: {winning_trades/(winning_trades+losing_trades)*100:.1f}%" if (winning_trades+losing_trades) > 0 else "   • 胜率: N/A")
        
        print(f"\n💰 收益统计:")
        print(f"   • 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"   • 最终资金: {final_value:,.0f} 港币")
        print(f"   • 总收益: {final_value - self.strategy_params['initial_capital']:+,.0f} 港币")
        print(f"   • 总收益率: {total_return:+.2f}%")
        
        # 计算年化收益率
        years = len(self.data) / 252  # 假设每年252个交易日
        annual_return = (final_value / self.strategy_params['initial_capital']) ** (1/years) - 1
        print(f"   • 年化收益率: {annual_return*100:.2f}%")
        
        return True
    
    def analyze_and_save_results(self):
        """分析结果并保存"""
        if not self.daily_portfolio or not self.trades:
            print("❌ 没有数据可分析")
            return
        
        # 转换为DataFrame
        portfolio_df = pd.DataFrame(self.daily_portfolio)
        trades_df = pd.DataFrame(self.trades)
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存交易记录
        trades_filename = f"hkd8000_20year_trades_{timestamp}.csv"
        trades_df.to_csv(trades_filename, index=False, encoding='utf-8-sig')
        
        # 保存每日组合价值
        portfolio_filename = f"hkd8000_20year_portfolio_{timestamp}.csv"
        portfolio_df.to_csv(portfolio_filename, index=False, encoding='utf-8-sig')
        
        print(f"\n💾 结果已保存:")
        print(f"   • 交易记录: {trades_filename}")
        print(f"   • 组合价值: {portfolio_filename}")
        
        # 创建简单图表
        self.create_simple_chart(portfolio_df, timestamp)
    
    def create_simple_chart(self, portfolio_df, timestamp):
        """创建简单图表"""
        plt.figure(figsize=(12, 8))
        
        # 组合价值变化
        plt.subplot(2, 1, 1)
        plt.plot(portfolio_df['date'], portfolio_df['total_value'], 
                label=f'复利策略 (8000港币起)', linewidth=2, color='blue')
        
        # 买入持有对比
        initial_shares = self.strategy_params['initial_capital'] / portfolio_df['price'].iloc[0]
        buy_hold_value = initial_shares * portfolio_df['price']
        plt.plot(portfolio_df['date'], buy_hold_value, 
                label='买入持有策略', linewidth=2, color='red', alpha=0.7)
        
        plt.title('8000港币20年复利回测 - 组合价值对比')
        plt.ylabel('组合价值 (港币)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 收益率对比
        plt.subplot(2, 1, 2)
        strategy_return = (portfolio_df['total_value'] / self.strategy_params['initial_capital'] - 1) * 100
        buyhold_return = (buy_hold_value / self.strategy_params['initial_capital'] - 1) * 100
        
        plt.plot(portfolio_df['date'], strategy_return, 
                label='复利策略收益率', linewidth=2, color='blue')
        plt.plot(portfolio_df['date'], buyhold_return, 
                label='买入持有收益率', linewidth=2, color='red', alpha=0.7)
        
        plt.title('收益率对比')
        plt.ylabel('收益率 (%)')
        plt.xlabel('日期')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_filename = f"hkd8000_20year_chart_{timestamp}.png"
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"   • 分析图表: {chart_filename}")

def main():
    """主函数"""
    print("🎯 8000港币20年复利回测系统")
    print("="*50)
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 创建回测实例
    backtest = HKD8000CompoundBacktest()
    
    # 连接数据库
    if not backtest.connect_database():
        print("❌ 无法连接数据库，请检查配置")
        return
    
    # 加载20年数据
    if not backtest.load_20year_data():
        print("❌ 无法加载历史数据")
        return
    
    # 运行复利回测
    if not backtest.run_compound_backtest():
        print("❌ 回测执行失败")
        return
    
    # 分析和保存结果
    backtest.analyze_and_save_results()
    
    print("\n🎉 8000港币20年复利回测完成!")
    print("💡 复利是投资的第八大奇迹 - 爱因斯坦")

if __name__ == "__main__":
    main()
