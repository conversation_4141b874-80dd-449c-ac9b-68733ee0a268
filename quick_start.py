#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本
===========

一键启动MT5交易监控系统
"""

import os
import sys
import json
import subprocess
from datetime import datetime

def print_banner():
    """打印启动横幅"""
    print("=" * 70)
    print("🚀 博弈论投资策略系统 - 快速启动")
    print("=" * 70)
    print("📅", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("📂", os.getcwd())
    print("=" * 70)

def check_requirements():
    """检查系统要求"""
    print("\n🔍 检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要3.7+")
        return False
    else:
        print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查重要文件
    required_files = [
        "main_launcher.py",
        "mt5_trading_launcher.py", 
        "system_controller.py",
        "system_config.json"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ 缺少重要文件: {', '.join(missing_files)}")
        return False
    
    # 检查Python包
    required_packages = [
        "MetaTrader5",
        "pymysql", 
        "pandas",
        "numpy",
        "schedule"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少Python包: {', '.join(missing_packages)}")
        print("💡 运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        
        choice = input("\n是否现在自动安装? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            try:
                subprocess.run([sys.executable, "install_dependencies.py"], check=True)
                print("✅ 依赖包安装完成")
            except:
                print("❌ 自动安装失败，请手动安装")
                return False
    
    return True

def quick_start_menu():
    """快速启动菜单"""
    print("\n🚀 快速启动选项:")
    print("-" * 40)
    print("1. 🔍 启动MT5交易监控")
    print("2. 📈 启动完整系统控制器") 
    print("3. 🎯 启动主控制台")
    print("4. 📊 运行策略分析")
    print("5. 🔧 系统检查")
    print("6. 🚪 退出")
    print("-" * 40)

def run_system_check():
    """运行系统检查"""
    print("\n🔧 系统检查...")
    print("-" * 40)
    
    # 检查MT5
    try:
        import MetaTrader5 as mt5
        if mt5.initialize():
            account_info = mt5.account_info()
            if account_info:
                print("✅ MT5连接正常")
                print(f"📊 账户: {account_info.login}")
                print(f"💰 余额: {account_info.balance:.2f}")
            else:
                print("⚠️ MT5未登录账户")
            mt5.shutdown()
        else:
            print("❌ MT5初始化失败")
    except Exception as e:
        print(f"❌ MT5检查失败: {e}")
    
    # 检查数据库
    try:
        import pymysql
        with open("system_config.json", 'r') as f:
            config = json.load(f)
        
        db_config = config['database']
        connection = pymysql.connect(**db_config)
        print("✅ 数据库连接正常")
        print(f"📊 主机: {db_config['host']}")
        print(f"🗄️ 数据库: {db_config['database']}")
        connection.close()
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

def main():
    """主函数"""
    print_banner()
    
    # 检查系统要求
    if not check_requirements():
        print("\n❌ 系统要求检查失败，请解决问题后重试")
        input("按回车键退出...")
        return
    
    print("\n✅ 系统要求检查通过!")
    
    while True:
        quick_start_menu()
        
        try:
            choice = input("\n请选择 (1-6): ").strip()
            
            if choice == "1":
                print("\n🔍 启动MT5交易监控...")
                subprocess.run([sys.executable, "mt5_trading_launcher.py"])
                
            elif choice == "2":
                print("\n📈 启动完整系统控制器...")
                subprocess.run([sys.executable, "system_controller.py"])
                
            elif choice == "3":
                print("\n🎯 启动主控制台...")
                subprocess.run([sys.executable, "main_launcher.py"])
                
            elif choice == "4":
                print("\n📊 运行策略分析...")
                strategy_menu = """
选择策略:
1. 改进版网格+凯利策略
2. 优化版Y>0.4且X>0.4策略  
3. HK2800 ETF策略
请选择 (1-3): """
                
                strategy_choice = input(strategy_menu).strip()
                if strategy_choice == "1":
                    subprocess.run([sys.executable, "improved_grid_kelly.py"])
                elif strategy_choice == "2":
                    subprocess.run([sys.executable, "optimized_y_x_04_backtest.py"])
                elif strategy_choice == "3":
                    subprocess.run([sys.executable, "test_hk2800_strategy.py"])
                
            elif choice == "5":
                run_system_check()
                
            elif choice == "6":
                print("\n👋 感谢使用!")
                break
                
            else:
                print("\n❌ 无效选择")
            
            if choice != "6":
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断")
            break
        except Exception as e:
            print(f"\n❌ 错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
