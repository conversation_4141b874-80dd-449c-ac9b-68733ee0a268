#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cosmoon博弈论凯利公式策略
========================

完整的Cosmoon策略，包含：
1. 高值盈利区 (Y>0.5, X>0.5): 买涨，凯利1:2，止盈+4%，止损-2%
2. 控股商控制区 (0.333<Y<0.4): 观望
3. 强亏损区 (Y<0.25或X<0.25): 买涨，凯利1:2，止盈+4%，止损-2%
4. 其他区域: 买跌，凯利1:2，止盈-4%，止损+2%

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class CosmoonKellyStrategy:
    def __init__(self):
        """初始化Cosmoon凯利公式策略"""
        self.db_config = {
            'host': '************',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
        
        # Cosmoon凯利策略参数
        self.strategy_params = {
            'initial_capital': 8000,         # 初始资金8000港币
            
            # 区域定义
            'high_profit_y': 0.5,            # 高值盈利区Y阈值
            'high_profit_x': 0.5,            # 高值盈利区X阈值
            'control_zone_min': 0.333,       # 控股商控制区下限
            'control_zone_max': 0.4,         # 控股商控制区上限
            'strong_loss_y': 0.25,           # 强亏损区Y阈值
            'strong_loss_x': 0.25,           # 强亏损区X阈值
            
            # 凯利公式参数 (1:2赔率)
            'kelly_win_rate': 0.67,          # 假设胜率67% (基于E>0分析)
            'kelly_win_ratio': 2,            # 盈利倍数 (1:2)
            'kelly_loss_ratio': 1,           # 亏损倍数
            
            # 止盈止损参数
            'high_profit_take_profit': 0.04, # 高值区止盈+4%
            'high_profit_stop_loss': 0.02,   # 高值区止损-2%
            'strong_loss_take_profit': 0.04, # 强亏损区止盈+4%
            'strong_loss_stop_loss': 0.02,   # 强亏损区止损-2%
            'other_take_profit': 0.04,       # 其他区域止盈-4%
            'other_stop_loss': 0.02,         # 其他区域止损+2%
            
            'transaction_cost': 0.0025,      # 交易成本0.25%
            'max_position_ratio': 0.3,       # 最大仓位30% (风险控制)
        }
        
        self.data = None
        self.trades = []
        self.daily_portfolio = []
        self.current_positions = []  # 当前持仓列表
        
    def calculate_kelly_position(self, win_rate, win_ratio, loss_ratio):
        """计算凯利公式仓位"""
        # 凯利公式: f = (bp - q) / b
        # 其中: b = 盈利倍数, p = 胜率, q = 败率 = 1-p
        b = win_ratio / loss_ratio
        p = win_rate
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        
        # 限制最大仓位，避免过度杠杆
        kelly_fraction = max(0, min(kelly_fraction, self.strategy_params['max_position_ratio']))
        
        return kelly_fraction
    
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def load_data(self):
        """加载20年历史数据"""
        print("📊 加载Cosmoon凯利策略数据...")
        
        try:
            query = """
                SELECT 
                    date,
                    close as price,
                    y_probability,
                    inflow_ratio as x_ratio,
                    high,
                    low,
                    volume
                FROM hk00023 
                WHERE date >= DATE_SUB(CURDATE(), INTERVAL 20 YEAR)
                AND y_probability IS NOT NULL 
                AND inflow_ratio IS NOT NULL
                ORDER BY date ASC
            """
            
            self.data = pd.read_sql(query, self.connection)
            
            if self.data.empty:
                print("❌ 未找到可用的历史数据")
                return False
            
            # 数据预处理
            self.data['date'] = pd.to_datetime(self.data['date'])
            self.data = self.data.sort_values('date').reset_index(drop=True)
            
            # 计算E值和策略区域
            self.data['e_value'] = (8 * self.data['x_ratio'] * self.data['y_probability'] - 
                                   3 * self.data['x_ratio'] - 3 * self.data['y_probability'] + 1)
            
            # 标记策略区域
            self.data['strategy_zone'] = 'OTHER'
            
            # 高值盈利区
            high_profit_mask = ((self.data['y_probability'] > self.strategy_params['high_profit_y']) & 
                               (self.data['x_ratio'] > self.strategy_params['high_profit_x']))
            self.data.loc[high_profit_mask, 'strategy_zone'] = 'HIGH_PROFIT'
            
            # 控股商控制区
            control_mask = ((self.data['y_probability'] > self.strategy_params['control_zone_min']) & 
                           (self.data['y_probability'] < self.strategy_params['control_zone_max']))
            self.data.loc[control_mask, 'strategy_zone'] = 'CONTROL_ZONE'
            
            # 强亏损区
            strong_loss_mask = ((self.data['y_probability'] < self.strategy_params['strong_loss_y']) | 
                               (self.data['x_ratio'] < self.strategy_params['strong_loss_x']))
            self.data.loc[strong_loss_mask, 'strategy_zone'] = 'STRONG_LOSS'
            
            print(f"📈 数据加载完成:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总交易日: {len(self.data)} 天")
            
            # 统计各区域分布
            zone_counts = self.data['strategy_zone'].value_counts()
            for zone, count in zone_counts.items():
                percentage = count / len(self.data) * 100
                print(f"   • {zone}: {count} 天 ({percentage:.1f}%)")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def check_stop_conditions(self, position, current_price):
        """检查止盈止损条件"""
        if position['direction'] == 'LONG':
            # 做多止盈止损
            profit_pct = (current_price - position['entry_price']) / position['entry_price']
            if profit_pct >= position['take_profit']:
                return 'TAKE_PROFIT'
            elif profit_pct <= -position['stop_loss']:
                return 'STOP_LOSS'
        else:  # SHORT
            # 做空止盈止损
            profit_pct = (position['entry_price'] - current_price) / position['entry_price']
            if profit_pct >= position['take_profit']:
                return 'TAKE_PROFIT'
            elif profit_pct <= -position['stop_loss']:
                return 'STOP_LOSS'
        
        return None
    
    def run_kelly_backtest(self):
        """运行Cosmoon凯利公式策略回测"""
        print(f"\n🚀 开始Cosmoon凯利公式策略回测...")
        print("="*70)
        print(f"💰 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"📊 策略: 凯利公式 + 止盈止损 + 多空策略")
        print(f"🎯 凯利参数: 胜率{self.strategy_params['kelly_win_rate']*100:.0f}%, 赔率1:{self.strategy_params['kelly_win_ratio']}")
        print("="*70)
        
        current_cash = self.strategy_params['initial_capital']
        total_trades = 0
        winning_trades = 0
        losing_trades = 0
        
        for i, row in self.data.iterrows():
            date = row['date']
            price = row['price']
            high = row['high']
            low = row['low']
            y_val = row['y_probability']
            x_val = row['x_ratio']
            zone = row['strategy_zone']
            
            # 检查现有持仓的止盈止损
            positions_to_close = []
            for j, position in enumerate(self.current_positions):
                stop_reason = self.check_stop_conditions(position, price)
                if stop_reason:
                    positions_to_close.append((j, stop_reason))
            
            # 平仓处理
            for j, stop_reason in reversed(positions_to_close):
                position = self.current_positions[j]
                
                if position['direction'] == 'LONG':
                    profit = (price - position['entry_price']) * position['shares'] - position['cost']
                else:  # SHORT
                    profit = (position['entry_price'] - price) * position['shares'] - position['cost']
                
                current_cash += position['shares'] * price - position['cost']
                
                # 记录交易
                trade_record = {
                    'date': date,
                    'action': f"CLOSE_{position['direction']}",
                    'direction': position['direction'],
                    'price': price,
                    'shares': position['shares'],
                    'profit': profit,
                    'profit_pct': profit / (position['shares'] * position['entry_price']) * 100,
                    'reason': stop_reason,
                    'zone': position['zone'],
                    'cash_after': current_cash
                }
                self.trades.append(trade_record)
                
                if profit > 0:
                    winning_trades += 1
                else:
                    losing_trades += 1
                
                total_trades += 1
                
                print(f"📉 {date.strftime('%Y-%m-%d')} 平仓{position['direction']}: {position['shares']:.0f}股 @ {price:.2f}, "
                      f"盈亏: {profit:+.0f} ({profit/(position['shares']*position['entry_price'])*100:+.1f}%), 原因: {stop_reason}")
                
                # 移除已平仓的持仓
                del self.current_positions[j]
            
            # 根据策略区域决定开仓
            if zone == 'HIGH_PROFIT':
                # 高值盈利区：买涨
                kelly_fraction = self.calculate_kelly_position(
                    self.strategy_params['kelly_win_rate'],
                    self.strategy_params['kelly_win_ratio'],
                    self.strategy_params['kelly_loss_ratio']
                )
                
                if kelly_fraction > 0 and len(self.current_positions) < 3:  # 限制同时持仓数量
                    position_value = current_cash * kelly_fraction
                    transaction_cost = position_value * self.strategy_params['transaction_cost']
                    net_value = position_value - transaction_cost
                    shares = net_value / price
                    
                    if shares > 0:
                        current_cash -= position_value
                        
                        new_position = {
                            'entry_date': date,
                            'entry_price': price,
                            'shares': shares,
                            'direction': 'LONG',
                            'zone': zone,
                            'take_profit': self.strategy_params['high_profit_take_profit'],
                            'stop_loss': self.strategy_params['high_profit_stop_loss'],
                            'cost': transaction_cost
                        }
                        self.current_positions.append(new_position)
                        
                        print(f"📈 {date.strftime('%Y-%m-%d')} 开多仓: {shares:.0f}股 @ {price:.2f}, "
                              f"仓位: {kelly_fraction*100:.1f}%, 区域: {zone}")
            
            elif zone == 'STRONG_LOSS':
                # 强亏损区：买涨 (低位反弹机会)
                kelly_fraction = self.calculate_kelly_position(
                    self.strategy_params['kelly_win_rate'],
                    self.strategy_params['kelly_win_ratio'],
                    self.strategy_params['kelly_loss_ratio']
                )

                if kelly_fraction > 0 and len(self.current_positions) < 3:
                    position_value = current_cash * kelly_fraction
                    transaction_cost = position_value * self.strategy_params['transaction_cost']
                    net_value = position_value - transaction_cost
                    shares = net_value / price

                    if shares > 0:
                        current_cash -= position_value

                        new_position = {
                            'entry_date': date,
                            'entry_price': price,
                            'shares': shares,
                            'direction': 'LONG',  # 改为做多
                            'zone': zone,
                            'take_profit': self.strategy_params['strong_loss_take_profit'],
                            'stop_loss': self.strategy_params['strong_loss_stop_loss'],
                            'cost': transaction_cost
                        }
                        self.current_positions.append(new_position)

                        print(f"📈 {date.strftime('%Y-%m-%d')} 开多仓: {shares:.0f}股 @ {price:.2f}, "
                              f"仓位: {kelly_fraction*100:.1f}%, 区域: {zone} (低位反弹)")
            
            elif zone == 'OTHER':
                # 其他区域：买跌
                kelly_fraction = self.calculate_kelly_position(
                    self.strategy_params['kelly_win_rate'] * 0.8,  # 降低胜率
                    self.strategy_params['kelly_win_ratio'],
                    self.strategy_params['kelly_loss_ratio']
                ) * 0.5  # 降低仓位
                
                if kelly_fraction > 0 and len(self.current_positions) < 2:
                    position_value = current_cash * kelly_fraction
                    transaction_cost = position_value * self.strategy_params['transaction_cost']
                    net_value = position_value - transaction_cost
                    shares = net_value / price
                    
                    if shares > 0:
                        current_cash -= position_value
                        
                        new_position = {
                            'entry_date': date,
                            'entry_price': price,
                            'shares': shares,
                            'direction': 'SHORT',
                            'zone': zone,
                            'take_profit': self.strategy_params['other_take_profit'],
                            'stop_loss': self.strategy_params['other_stop_loss'],
                            'cost': transaction_cost
                        }
                        self.current_positions.append(new_position)
            
            # 计算当前总资产
            position_value = 0
            for position in self.current_positions:
                if position['direction'] == 'LONG':
                    position_value += position['shares'] * price
                else:  # SHORT
                    position_value += position['shares'] * (2 * position['entry_price'] - price)
            
            total_assets = current_cash + position_value
            
            # 记录每日组合价值
            daily_record = {
                'date': date,
                'price': price,
                'cash': current_cash,
                'position_value': position_value,
                'total_value': total_assets,
                'positions_count': len(self.current_positions),
                'zone': zone,
                'y_value': y_val,
                'x_value': x_val
            }
            self.daily_portfolio.append(daily_record)
        
        # 最终清仓
        final_price = self.data['price'].iloc[-1]
        for position in self.current_positions:
            if position['direction'] == 'LONG':
                profit = (final_price - position['entry_price']) * position['shares']
            else:
                profit = (position['entry_price'] - final_price) * position['shares']
            
            current_cash += position['shares'] * final_price
            
            if profit > 0:
                winning_trades += 1
            else:
                losing_trades += 1
            total_trades += 1
        
        # 计算最终结果
        final_value = current_cash
        total_return = (final_value / self.strategy_params['initial_capital'] - 1) * 100
        years = len(self.data) / 252
        annual_return = (final_value / self.strategy_params['initial_capital']) ** (1/years) - 1
        
        print(f"\n✅ Cosmoon凯利公式策略回测完成!")
        print(f"📊 交易统计:")
        print(f"   • 总交易次数: {total_trades}")
        print(f"   • 盈利交易: {winning_trades}")
        print(f"   • 亏损交易: {losing_trades}")
        print(f"   • 实际胜率: {winning_trades/total_trades*100:.1f}%" if total_trades > 0 else "   • 胜率: N/A")
        
        print(f"\n💰 收益统计:")
        print(f"   • 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"   • 最终资金: {final_value:,.0f} 港币")
        print(f"   • 总收益: {final_value - self.strategy_params['initial_capital']:+,.0f} 港币")
        print(f"   • 总收益率: {total_return:+.2f}%")
        print(f"   • 年化收益率: {annual_return*100:.2f}%")
        
        return True

def main():
    """主函数"""
    print("🎯 Cosmoon博弈论凯利公式策略")
    print("="*50)
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("👨‍💼 策略作者: Cosmoon NG")
    print("📊 核心: 凯利公式 + 博弈论 + 止盈止损")
    
    # 创建回测实例
    backtest = CosmoonKellyStrategy()
    
    # 连接数据库
    if not backtest.connect_database():
        return
    
    # 加载数据
    if not backtest.load_data():
        return
    
    # 运行回测
    if not backtest.run_kelly_backtest():
        return
    
    print("\n🎉 Cosmoon凯利公式策略回测完成!")
    print("💡 凯利公式 + 博弈论 = 数学制胜！")

if __name__ == "__main__":
    main()
