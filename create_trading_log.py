#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易记录追踪表格生成器
===================

生成一个详细的交易记录Excel表格，用于：
1. 记录每笔交易的X、Y、E值
2. 追踪交易结果
3. 分析交易表现
4. 优化交易策略

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import numpy as np
from datetime import datetime
import openpyxl
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter

def create_trading_log():
    """创建交易记录表格"""
    print("📝 创建交易记录表格...")
    
    # 创建Excel写入器
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"交易记录追踪表_{timestamp}.xlsx"
    writer = pd.ExcelWriter(filename, engine='openpyxl')
    
    # === 1. 交易记录模板 ===
    trade_log_template = {
        '日期': [],
        '时间': [],
        '交易品种': [],
        '操作': [],  # 开仓/加仓/减仓/平仓
        '方向': [],  # 做多/做空
        '价格': [],
        '数量': [],
        '成交金额': [],
        'Y值': [],
        'X值': [],
        'E值': [],
        '策略区域': [],
        '交易理由': [],
        '风险评估': [],
        '止盈价': [],
        '止损价': [],
        '预期收益': [],
        '实际盈亏': [],
        '持仓时间': [],
        '备注': []
    }
    
    # === 2. 统计分析模板 ===
    analysis_template = {
        '分析项目': [
            '总交易次数',
            '盈利次数',
            '亏损次数',
            '胜率',
            '总盈亏',
            '最大单笔盈利',
            '最大单笔亏损',
            '平均盈亏',
            '盈亏比',
            '最大连续盈利',
            '最大连续亏损',
            '平均持仓时间'
        ],
        '统计结果': [''] * 12,
        '分析说明': [
            '记录所有交易次数',
            '盈利交易次数',
            '亏损交易次数',
            '盈利次数/总次数',
            '所有交易盈亏总和',
            '单笔最大盈利金额',
            '单笔最大亏损金额',
            '平均每笔盈亏',
            '平均盈利/平均亏损',
            '最多连续盈利次数',
            '最多连续亏损次数',
            '平均每笔持仓时长'
        ]
    }
    
    # === 3. 区域表现分析模板 ===
    zone_analysis_template = {
        '策略区域': [
            '高值盈利区',
            '强亏损区',
            '其他区域',
            '控股商控制区'
        ],
        '交易次数': [''] * 4,
        '盈利次数': [''] * 4,
        '亏损次数': [''] * 4,
        '胜率': [''] * 4,
        '总盈亏': [''] * 4,
        '平均盈亏': [''] * 4,
        '最大盈利': [''] * 4,
        '最大亏损': [''] * 4,
        '平均持仓时间': [''] * 4
    }
    
    # === 4. 策略评估模板 ===
    strategy_evaluation_template = {
        '评估项目': [
            'Y值分布',
            'X值分布',
            'E值分布',
            '最佳交易区域',
            '最差交易区域',
            '最佳持仓时间',
            '最优止盈位置',
            '最优止损位置',
            '建议改进方向'
        ],
        '分析结果': [''] * 9,
        '改进建议': [''] * 9
    }
    
    # === 5. 月度汇总模板 ===
    monthly_summary_template = {
        '月份': [],
        '交易次数': [],
        '胜率': [],
        '盈亏金额': [],
        '最大盈利': [],
        '最大亏损': [],
        '累计收益率': [],
        '夏普比率': [],
        '交易成本': [],
        '净收益': []
    }
    
    # 写入各个工作表
    pd.DataFrame(trade_log_template).to_excel(writer, sheet_name='交易记录', index=False)
    pd.DataFrame(analysis_template).to_excel(writer, sheet_name='统计分析', index=False)
    pd.DataFrame(zone_analysis_template).to_excel(writer, sheet_name='区域分析', index=False)
    pd.DataFrame(strategy_evaluation_template).to_excel(writer, sheet_name='策略评估', index=False)
    pd.DataFrame(monthly_summary_template).to_excel(writer, sheet_name='月度汇总', index=False)
    
    # 添加公式
    workbook = writer.book
    
    # 在交易记录表中添加计算公式
    worksheet = writer.sheets['交易记录']
    
    # 成交金额计算公式
    worksheet['H2'] = '=F2*G2'
    
    # 实际盈亏计算公式（平仓时）
    worksheet['R2'] = '=IF(D2="平仓",H2-VLOOKUP(C2,交易记录!C:H,6,FALSE),"")'
    
    # 在统计分析表中添加计算公式
    worksheet = writer.sheets['统计分析']
    worksheet['B1'] = '=COUNTA(交易记录!A:A)-1'  # 总交易次数
    worksheet['B2'] = '=COUNTIF(交易记录!R:R,">0")'  # 盈利次数
    worksheet['B3'] = '=COUNTIF(交易记录!R:R,"<0")'  # 亏损次数
    worksheet['B4'] = '=B2/B1'  # 胜率
    worksheet['B5'] = '=SUM(交易记录!R:R)'  # 总盈亏
    worksheet['B6'] = '=MAX(交易记录!R:R)'  # 最大单笔盈利
    worksheet['B7'] = '=MIN(交易记录!R:R)'  # 最大单笔亏损
    worksheet['B8'] = '=AVERAGE(交易记录!R:R)'  # 平均盈亏
    
    # 设置格式
    for sheet_name in writer.sheets:
        worksheet = writer.sheets[sheet_name]
        
        # 设置列宽
        for i, col in enumerate(worksheet.columns):
            max_length = 0
            column = get_column_letter(i + 1)
            
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column].width = adjusted_width
        
        # 设置标题行格式
        for cell in worksheet[1]:
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.font = Font(color="FFFFFF", bold=True)
            cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
        
        # 设置单元格格式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in worksheet.iter_rows(min_row=2):
            for cell in row:
                cell.border = thin_border
                cell.alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)
    
    # 保存文件
    writer.close()
    
    print(f"✅ 交易记录表格已创建: {filename}")
    return filename

def main():
    """主函数"""
    print("📊 交易记录追踪表格生成器")
    print("="*50)
    
    filename = create_trading_log()
    
    if filename:
        print("\n📋 表格包含以下工作表：")
        print("1. 交易记录 - 详细记录每笔交易")
        print("2. 统计分析 - 整体交易表现")
        print("3. 区域分析 - 各策略区域表现")
        print("4. 策略评估 - 策略优化建议")
        print("5. 月度汇总 - 按月统计表现")
        
        print("\n💡 使用说明：")
        print('1. 在"交易记录"表中记录每笔交易')
        print("2. 其他表格会自动计算统计结果")
        print('3. 定期查看"策略评估"获取优化建议')
        print('4. 使用"月度汇总"跟踪长期表现')

if __name__ == "__main__":
    main()
