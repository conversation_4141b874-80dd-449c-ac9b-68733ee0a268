#!/usr/bin/env pythondef create_database():
    """创建数据库和表结构"""
    print("创建数据库表...")
    conn = sqlite3.connect('finance.db')
    cursor = conn.cursor()
    
    # 检查表是否存在
    cursor.execute("DROP TABLE IF EXISTS hkhsi50")
    
    # 创建数据表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS hkhsi50 ( coding: utf-8 -*-
"""
获取恒生50指数数据
=================

从Yahoo Finance获取恒生50指数数据并存入SQLite数据库
- 数据库：finance.db
- 表名：hkhsi50

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import sqlite3
from datetime import datetime, timedelta
import time
import numpy as np

def create_database():
    """创建数据库和表结构"""
    conn = sqlite3.connect('finance.db')
    cursor = conn.cursor()
    
    # 创建数据表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS hkhsi50 (
        date TEXT PRIMARY KEY,
        open REAL,
        high REAL,
        low REAL,
        close REAL,
        volume REAL,
        ma20 REAL,
        ma60 REAL,
        rsi REAL,
        money_flow REAL,
        base_x REAL,
        x_value REAL,
        base_y REAL,
        y_value REAL,
        e_value REAL
    )
    ''')
    
    conn.commit()
    conn.close()

def calculate_indicators(df):
    """计算技术指标"""
    # 计算移动平均线
    df['ma20'] = df['Close'].rolling(window=20).mean()
    df['ma60'] = df['Close'].rolling(window=60).mean()
    
    # 计算RSI
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # 计算资金流
    df['money_flow'] = df['Volume'] * (df['Close'] - df['Open']) / df['Open']
    
    # 计算基础X值（20日资金流向）
    pos_flow = df['money_flow'].where(df['money_flow'] > 0, 0).rolling(window=20).sum()
    neg_flow = (-df['money_flow'].where(df['money_flow'] < 0, 0)).rolling(window=20).sum()
    df['base_x'] = pos_flow / (pos_flow + neg_flow)
    
    # 计算最终X值
    rsi_adj = 0.3 * (df['rsi']/100 - 0.5)
    df['x_value'] = df['base_x'] + rsi_adj
    df['x_value'] = df['x_value'].clip(0.1, 0.9)
    
    # 计算基础Y值和最终Y值
    price_ma20_ratio = df['Close'] / df['ma20']
    df['base_y'] = pd.Series(index=df.index)
    mask = price_ma20_ratio >= 1
    df.loc[mask, 'base_y'] = 0.5 + 0.4 * np.tanh((price_ma20_ratio[mask] - 1) * 3)
    df.loc[~mask, 'base_y'] = 0.5 - 0.4 * np.tanh((1 - price_ma20_ratio[~mask]) * 3)
    
    # 趋势调整
    trend_adj = 0.1 * np.tanh((df['ma20'] / df['ma60'] - 1) * 2)
    
    # 成交量调整
    volume_ma20 = df['Volume'].rolling(window=20).mean()
    vol_adj = 0.05 * np.tanh((df['Volume'] / volume_ma20 - 1))
    
    df['y_value'] = (df['base_y'] + trend_adj + vol_adj).clip(0.1, 0.9)
    
    # 计算E值
    df['e_value'] = 8 * df['x_value'] * df['y_value'] - 3 * df['x_value'] - 3 * df['y_value'] + 1
    
    return df

def fetch_hsi50_data():
    """获取恒生50指数数据"""
    print("📊 开始获取恒生50指数数据...")
    
    # 创建数据库和表
    create_database()
    
    try:
        # 获取恒生50指数数据
        symbol = "^HSI"  # 恒生指数的Yahoo Finance代码
        ticker = yf.Ticker(symbol)
        
        # 获取过去5年的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=5*365)
        
        df = ticker.history(start=start_date, end=end_date)
        
        if df.empty:
            print("❌ 未能获取到数据，请检查网络连接和股票代码")
            return
        
        # 计算技术指标
        df = calculate_indicators(df)
        
        # 准备数据库连接
        conn = sqlite3.connect('finance.db')
        
        # 将数据写入数据库
        df.reset_index(inplace=True)
        df['Date'] = df['Date'].dt.strftime('%Y-%m-%d')
        
        # 选择需要的列并重命名
        columns = {
            'Date': 'date',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume',
            'ma20': 'ma20',
            'ma60': 'ma60',
            'rsi': 'rsi',
            'money_flow': 'money_flow',
            'base_x': 'base_x',
            'x_value': 'x_value',
            'base_y': 'base_y',
            'y_value': 'y_value',
            'e_value': 'e_value'
        }
        
        df = df.rename(columns=columns)
        df[columns.values()].to_sql('hkhsi50', conn, if_exists='replace', index=False)
        
        conn.close()
        
        print(f"✅ 成功获取并存储了 {len(df)} 条数据")
        print("📊 数据摘要：")
        print(f"时间范围：{df['date'].min()} 至 {df['date'].max()}")
        print("\n最新数据：")
        latest = df.iloc[-1]
        print(f"日期：{latest['date']}")
        print(f"收盘价：{latest['close']:.2f}")
        print(f"X值：{latest['x_value']:.3f}")
        print(f"Y值：{latest['y_value']:.3f}")
        print(f"E值：{latest['e_value']:.3f}")
        
    except Exception as e:
        print(f"❌ 出现错误：{str(e)}")

if __name__ == "__main__":
    print("📈 恒生50指数数据获取工具")
    print("="*50)
    
    fetch_hsi50_data()
