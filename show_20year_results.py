#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示20年回测结果
===============

显示HK00023东亚银行20年完整回测结果

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import glob
import os

def show_20year_results():
    """显示20年回测结果"""
    # 查找最新的20年回测文件
    excel_files = glob.glob("HK00023东亚银行20年完整回测_*.xlsx")
    if not excel_files:
        print("❌ 未找到20年回测文件")
        return
    
    # 获取最新文件
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"🏦 HK00023东亚银行20年完整回测: {latest_file}")
    
    try:
        # 读取所有工作表
        df_trades = pd.read_excel(latest_file, sheet_name='20年交易记录')
        df_summary = pd.read_excel(latest_file, sheet_name='汇总统计')
        df_yearly = pd.read_excel(latest_file, sheet_name='年度统计')
        df_strategy = pd.read_excel(latest_file, sheet_name='策略说明')
        df_mean_reversion = pd.read_excel(latest_file, sheet_name='均值回归分析')
        
        print(f"\n📊 HK00023东亚银行20年完整回测汇总:")
        print("="*90)
        
        # 显示关键统计
        for _, row in df_summary.iterrows():
            if pd.notna(row['数值']):
                print(f"{row['项目']}: {row['数值']}")
        
        print(f"\n📅 年度交易统计:")
        print("="*60)
        print(df_yearly.to_string(index=False))
        
        print(f"\n📈 前20条交易记录预览:")
        print("="*150)
        
        # 显示关键列
        preview_columns = ['交易序号', '开仓日期', '交易方向', '开仓价格', '均值回归中值', 
                          '价格偏离度%', '净利润', 'Y值', 'X值', 'E值', '策略区域', '平仓原因']
        
        print(df_trades[preview_columns].head(20).to_string(index=False))
        
        print(f"\n📊 20年回测详细分析:")
        print("="*80)
        
        # 按策略区域分析
        print(f"按策略区域分析:")
        for zone in df_trades['策略区域'].unique():
            zone_data = df_trades[df_trades['策略区域'] == zone]
            count = len(zone_data)
            total_profit = zone_data['净利润'].sum()
            avg_profit = zone_data['净利润'].mean()
            win_rate = len(zone_data[zone_data['净利润'] > 0]) / count * 100
            
            # 显示该区域的交易方向
            directions = zone_data['交易方向'].value_counts()
            direction_str = ", ".join([f"{dir}:{cnt}次" for dir, cnt in directions.items()])
            
            print(f"• {zone}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%, 方向({direction_str})")
        
        # 按交易方向分析
        print(f"\n按交易方向分析:")
        for direction in df_trades['交易方向'].unique():
            dir_data = df_trades[df_trades['交易方向'] == direction]
            count = len(dir_data)
            total_profit = dir_data['净利润'].sum()
            avg_profit = dir_data['净利润'].mean()
            win_rate = len(dir_data[dir_data['净利润'] > 0]) / count * 100
            
            print(f"• {direction}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # E值分析
        print(f"\n按E值分析:")
        e_positive = df_trades[df_trades['E值'] > 0]
        e_negative = df_trades[df_trades['E值'] < 0]
        
        if len(e_positive) > 0:
            e_pos_profit = e_positive['净利润'].sum()
            e_pos_count = len(e_positive)
            e_pos_win_rate = len(e_positive[e_positive['净利润'] > 0]) / e_pos_count * 100
            print(f"• E>0: {e_pos_count}次, 总盈亏{e_pos_profit:+,.0f}港币, 胜率{e_pos_win_rate:.1f}%")
        
        if len(e_negative) > 0:
            e_neg_profit = e_negative['净利润'].sum()
            e_neg_count = len(e_negative)
            e_neg_win_rate = len(e_negative[e_negative['净利润'] > 0]) / e_neg_count * 100
            print(f"• E<0: {e_neg_count}次, 总盈亏{e_neg_profit:+,.0f}港币, 胜率{e_neg_win_rate:.1f}%")
        
        # 平仓原因分析
        print(f"\n按平仓原因分析:")
        exit_reasons = df_trades['平仓原因'].value_counts()
        for reason, count in exit_reasons.items():
            reason_data = df_trades[df_trades['平仓原因'] == reason]
            total_profit = reason_data['净利润'].sum()
            avg_profit = reason_data['净利润'].mean()
            win_rate = len(reason_data[reason_data['净利润'] > 0]) / count * 100
            print(f"• {reason}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # 年度表现分析
        print(f"\n📅 年度表现分析:")
        print("="*70)
        
        # 计算年度收益率
        df_yearly['年收益率%'] = (df_yearly['总盈亏'] / 30000 * 100).round(2)
        
        # 找出最佳和最差年份
        best_year = df_yearly.loc[df_yearly['总盈亏'].idxmax()]
        worst_year = df_yearly.loc[df_yearly['总盈亏'].idxmin()]
        
        print(f"最佳年份: {best_year['开仓年份']}年")
        print(f"• 交易次数: {best_year['交易次数']}次")
        print(f"• 总盈亏: {best_year['总盈亏']:+,.0f}港币")
        print(f"• 年收益率: {best_year['年收益率%']:+.2f}%")
        
        print(f"\n最差年份: {worst_year['开仓年份']}年")
        print(f"• 交易次数: {worst_year['交易次数']}次")
        print(f"• 总盈亏: {worst_year['总盈亏']:+,.0f}港币")
        print(f"• 年收益率: {worst_year['年收益率%']:+.2f}%")
        
        # 盈利年份统计
        profitable_years = len(df_yearly[df_yearly['总盈亏'] > 0])
        total_years = len(df_yearly)
        print(f"\n盈利年份: {profitable_years}/{total_years} ({profitable_years/total_years*100:.1f}%)")
        
        # 均值回归与博弈论结合分析
        print(f"\n📈 均值回归与博弈论结合分析:")
        print("="*70)
        
        # 分析不同偏离度下的策略效果
        deviation_ranges = [
            ('严重高估 (>10%)', df_trades[df_trades['价格偏离度%'] > 10]),
            ('轻微高估 (5%-10%)', df_trades[(df_trades['价格偏离度%'] > 5) & (df_trades['价格偏离度%'] <= 10)]),
            ('合理区间 (-5%-5%)', df_trades[(df_trades['价格偏离度%'] >= -5) & (df_trades['价格偏离度%'] <= 5)]),
            ('轻微低估 (-10%到-5%)', df_trades[(df_trades['价格偏离度%'] >= -10) & (df_trades['价格偏离度%'] < -5)]),
            ('严重低估 (<-10%)', df_trades[df_trades['价格偏离度%'] < -10])
        ]
        
        for range_name, range_data in deviation_ranges:
            if len(range_data) > 0:
                count = len(range_data)
                total_profit = range_data['净利润'].sum()
                win_rate = len(range_data[range_data['净利润'] > 0]) / count * 100
                avg_deviation = range_data['价格偏离度%'].mean()
                
                print(f"• {range_name}: {count}次, 盈亏{total_profit:+,.0f}港币, "
                      f"胜率{win_rate:.1f}%, 平均偏离{avg_deviation:+.1f}%")
        
        # 最佳和最差交易分析
        print(f"\n🎯 最佳和最差交易分析:")
        print("="*60)
        
        best_trade = df_trades.loc[df_trades['净利润'].idxmax()]
        worst_trade = df_trades.loc[df_trades['净利润'].idxmin()]
        
        print(f"最佳交易:")
        print(f"• 交易{best_trade['交易序号']}: {best_trade['开仓日期']}, {best_trade['交易方向']}")
        print(f"  盈利{best_trade['净利润']:+.0f}港币, {best_trade['策略区域']}")
        print(f"  Y={best_trade['Y值']:.3f}, X={best_trade['X值']:.3f}, E={best_trade['E值']:.3f}")
        print(f"  偏离度{best_trade['价格偏离度%']:+.1f}%, 平仓原因: {best_trade['平仓原因']}")
        
        print(f"\n最差交易:")
        print(f"• 交易{worst_trade['交易序号']}: {worst_trade['开仓日期']}, {worst_trade['交易方向']}")
        print(f"  亏损{worst_trade['净利润']:+.0f}港币, {worst_trade['策略区域']}")
        print(f"  Y={worst_trade['Y值']:.3f}, X={worst_trade['X值']:.3f}, E={worst_trade['E值']:.3f}")
        print(f"  偏离度{worst_trade['价格偏离度%']:+.1f}%, 平仓原因: {worst_trade['平仓原因']}")
        
        # 策略效果总结
        print(f"\n📊 20年回测策略效果总结:")
        print("="*70)
        
        final_capital = df_trades['账户余额'].iloc[-1]
        total_return = (final_capital / 30000 - 1) * 100
        annual_return = ((final_capital / 30000) ** (1/20) - 1) * 100
        total_profit = df_trades['净利润'].sum()
        win_rate = len(df_trades[df_trades['净利润'] > 0]) / len(df_trades) * 100
        
        print(f"• 初始资金: 30,000港币")
        print(f"• 最终资金: {final_capital:,.0f}港币")
        print(f"• 总收益率: {total_return:+.2f}%")
        print(f"• 年化收益率: {annual_return:+.2f}%")
        print(f"• 总盈亏: {total_profit:+,.0f}港币")
        print(f"• 总交易次数: {len(df_trades)}次")
        print(f"• 胜率: {win_rate:.1f}%")
        print(f"• 最大单笔盈利: {df_trades['净利润'].max():+.0f}港币")
        print(f"• 最大单笔亏损: {df_trades['净利润'].min():+.0f}港币")
        print(f"• 平均每笔盈亏: {total_profit/len(df_trades):+.0f}港币")
        
        # 风险指标
        print(f"\n📊 风险指标分析:")
        print("="*60)
        
        # 计算最大回撤
        df_trades['累计资金'] = df_trades['账户余额']
        df_trades['最高资金'] = df_trades['累计资金'].expanding().max()
        df_trades['回撤'] = (df_trades['累计资金'] - df_trades['最高资金']) / df_trades['最高资金'] * 100
        max_drawdown = df_trades['回撤'].min()
        
        # 计算夏普比率 (简化版)
        annual_returns = df_yearly['总盈亏'] / 30000 * 100
        sharpe_ratio = annual_returns.mean() / annual_returns.std() if annual_returns.std() > 0 else 0
        
        print(f"• 最大回撤: {max_drawdown:.2f}%")
        print(f"• 夏普比率: {sharpe_ratio:.2f}")
        print(f"• 年度收益标准差: {annual_returns.std():.2f}%")
        
        print(f"\n💡 20年回测Excel文件结构:")
        print("="*70)
        print(f"📊 工作表1: 20年交易记录")
        print(f"   • {len(df_trades)}条详细交易记录")
        print(f"   • 包含均值回归和博弈论指标")
        
        print(f"\n📈 工作表2: 汇总统计")
        print(f"   • 完整的20年财务统计")
        print(f"   • 年化收益率计算")
        
        print(f"\n📅 工作表3: 年度统计")
        print(f"   • 逐年交易次数和盈亏")
        print(f"   • 年度表现分析")
        
        print(f"\n🎯 工作表4: 策略说明")
        print(f"   • 详细的策略操作指引")
        print(f"   • 各区域止盈止损设置")
        
        print(f"\n📋 工作表5: 均值回归分析")
        print(f"   • 均值回归理论应用")
        print(f"   • 价格偏离度分析")
        
        print(f"\n🎉 20年回测关键发现:")
        print("="*60)
        print(f"✅ 完成了{len(df_trades)}笔交易，覆盖20年完整周期")
        print(f"✅ 验证了Cosmoon博弈论策略的长期有效性")
        print(f"✅ 均值回归理论在实际交易中的应用")
        print(f"✅ 包含完整的年度统计和风险分析")
        print(f"❌ 年化收益率为负，策略需要进一步优化")
        print(f"💡 建议重点优化止损策略和仓位管理")
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")

if __name__ == "__main__":
    show_20year_results()
