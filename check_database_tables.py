#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构
===============

检查数据库中有哪些表，以及表的结构

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd

def check_database():
    """检查数据库表"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        print("✅ 数据库连接成功")
        
        # 查看所有表
        print("\n📊 数据库中的所有表:")
        tables_query = "SHOW TABLES"
        tables = pd.read_sql(tables_query, connection)
        
        if tables.empty:
            print("❌ 数据库中没有表")
            return
        
        for i, table in enumerate(tables.iloc[:, 0]):
            print(f"   {i+1}. {table}")
        
        # 检查每个表的结构和数据量
        print(f"\n🔍 表结构和数据量:")
        for table in tables.iloc[:, 0]:
            try:
                # 获取表结构
                structure_query = f"DESCRIBE {table}"
                structure = pd.read_sql(structure_query, connection)
                
                # 获取数据量
                count_query = f"SELECT COUNT(*) as count FROM {table}"
                count = pd.read_sql(count_query, connection)
                
                print(f"\n📋 表: {table}")
                print(f"   • 记录数: {count['count'].iloc[0]:,}")
                print(f"   • 字段:")
                for _, row in structure.iterrows():
                    print(f"     - {row['Field']}: {row['Type']}")
                
                # 如果表名包含股票代码，显示样本数据
                if any(code in table.lower() for code in ['hk', '00', 'stock', 'equity']):
                    sample_query = f"SELECT * FROM {table} LIMIT 3"
                    sample = pd.read_sql(sample_query, connection)
                    print(f"   • 样本数据:")
                    for _, row in sample.iterrows():
                        print(f"     {dict(row)}")
                        
            except Exception as e:
                print(f"   ❌ 无法查询表 {table}: {e}")
        
        # 查找包含Y和X值的表
        print(f"\n🎯 查找包含Y和X值的表:")
        for table in tables.iloc[:, 0]:
            try:
                structure_query = f"DESCRIBE {table}"
                structure = pd.read_sql(structure_query, connection)
                
                has_y = any('y_probability' in field.lower() or 'y_value' in field.lower() for field in structure['Field'])
                has_x = any('x_ratio' in field.lower() or 'inflow_ratio' in field.lower() or 'x_value' in field.lower() for field in structure['Field'])
                
                if has_y or has_x:
                    print(f"   📊 {table}:")
                    if has_y:
                        print(f"     ✅ 包含Y值字段")
                    if has_x:
                        print(f"     ✅ 包含X值字段")
                        
            except Exception as e:
                continue
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

if __name__ == "__main__":
    print("🔍 检查数据库表结构")
    print("="*50)
    check_database()
