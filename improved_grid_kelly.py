#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版网格+凯利策略
优化参数，降低交易频率，提高收益
"""

import numpy as np

def improved_grid_kelly():
    """改进版网格+凯利策略分析"""
    
    print("🔧 改进版网格+凯利策略分析")
    print("="*60)
    print("💰 初始资金: 30,000 港币")
    print("📅 投资期限: 3年")
    print("🎯 目标: 优化参数，提高收益")
    
    # 1. 问题分析
    print(f"\n🚨 原策略问题分析:")
    print("="*40)
    print("❌ 胜率从51.7%降至42.5%")
    print("❌ 交易过于频繁 (1075次/3年)")
    print("❌ 年化收益仅1.46%")
    print("❌ 网格间距过小 (2%)")
    print("❌ 凯利比例过高 (19.5%)")
    
    # 2. 优化方案设计
    optimization_strategies = [
        {
            'name': '保守网格策略',
            'grid_spacing': 0.05,      # 5%网格间距
            'kelly_fraction': 0.10,    # 10%凯利比例
            'max_trades_per_month': 4, # 每月最多4次
            'stop_loss': 0.06,         # 6%止损
            'take_profit': 0.10,       # 10%止盈
            'description': '大间距，低频交易，保守仓位'
        },
        {
            'name': '平衡网格策略',
            'grid_spacing': 0.04,      # 4%网格间距
            'kelly_fraction': 0.12,    # 12%凯利比例
            'max_trades_per_month': 6, # 每月最多6次
            'stop_loss': 0.07,         # 7%止损
            'take_profit': 0.12,       # 12%止盈
            'description': '中等间距，适中频率，平衡风险'
        },
        {
            'name': '趋势跟踪策略',
            'grid_spacing': 0.03,      # 3%网格间距
            'kelly_fraction': 0.08,    # 8%凯利比例
            'max_trades_per_month': 8, # 每月最多8次
            'stop_loss': 0.05,         # 5%止损
            'take_profit': 0.15,       # 15%止盈
            'description': '小间距，快速止损，大止盈'
        }
    ]
    
    # 3. 模拟各种策略
    def simulate_strategy(strategy, years=3):
        """模拟策略表现"""
        
        initial_capital = 30000
        
        # 基于改进的参数估算
        # 假设通过优化能提升胜率到48-50%
        win_rate = 0.48 + (0.10 - strategy['kelly_fraction']) * 2  # 保守仓位提升胜率
        win_rate = min(win_rate, 0.52)
        
        # 根据网格间距调整平均收益
        avg_win = strategy['take_profit'] * 0.7  # 70%概率达到止盈
        avg_loss = strategy['stop_loss'] * 0.8   # 80%概率达到止损
        
        # 年均交易次数
        annual_trades = strategy['max_trades_per_month'] * 12
        total_trades = annual_trades * years
        
        # 蒙特卡洛模拟
        np.random.seed(42)
        simulations = 1000
        final_amounts = []
        
        for sim in range(simulations):
            capital = initial_capital
            
            for trade in range(total_trades):
                position_size = capital * strategy['kelly_fraction']
                
                if np.random.random() < win_rate:
                    # 盈利交易
                    profit = position_size * avg_win
                    capital += profit
                else:
                    # 亏损交易
                    loss = position_size * avg_loss
                    capital -= loss
                
                # 防止资金为负
                capital = max(capital, 0)
                if capital < initial_capital * 0.5:  # 50%止损
                    break
            
            final_amounts.append(capital)
        
        # 统计结果
        median_final = np.median(final_amounts)
        mean_final = np.mean(final_amounts)
        success_rate = sum(1 for x in final_amounts if x > initial_capital) / len(final_amounts)
        
        total_return = (median_final - initial_capital) / initial_capital
        annual_return = ((median_final / initial_capital) ** (1/years)) - 1
        
        return {
            'median_final': median_final,
            'mean_final': mean_final,
            'total_return': total_return,
            'annual_return': annual_return,
            'success_rate': success_rate,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'avg_win': avg_win,
            'avg_loss': avg_loss
        }
    
    # 4. 执行模拟
    print(f"\n📊 优化策略对比:")
    print("="*80)
    print("策略           最终金额   总收益   年化收益  胜率   交易数  成功率")
    print("-" * 80)
    
    best_strategy = None
    best_annual = 0
    
    for strategy in optimization_strategies:
        result = simulate_strategy(strategy)
        
        print(f"{strategy['name']:<12} {result['median_final']:>8,.0f}  {result['median_final']-30000:>+7,.0f}  {result['annual_return']*100:>+6.2f}%  {result['win_rate']*100:>4.1f}%  {result['total_trades']:>5}  {result['success_rate']*100:>5.1f}%")
        
        if result['annual_return'] > best_annual:
            best_annual = result['annual_return']
            best_strategy = {**strategy, **result}
    
    # 5. 最佳策略详细分析
    if best_strategy:
        print(f"\n🏆 最佳策略: {best_strategy['name']}")
        print("="*50)
        print(f"📊 {best_strategy['description']}")
        
        print(f"\n📊 策略参数:")
        print(f"   网格间距: {best_strategy['grid_spacing']*100:.0f}%")
        print(f"   凯利比例: {best_strategy['kelly_fraction']*100:.0f}%")
        print(f"   月交易限制: {best_strategy['max_trades_per_month']}次")
        print(f"   止损/止盈: {best_strategy['stop_loss']*100:.0f}%/{best_strategy['take_profit']*100:.0f}%")
        
        print(f"\n📊 预期表现:")
        print(f"   最终金额: {best_strategy['median_final']:,.0f} 港币")
        print(f"   总收益: {best_strategy['median_final']-30000:+,.0f} 港币")
        print(f"   年化收益: {best_strategy['annual_return']*100:+.2f}%")
        print(f"   胜率: {best_strategy['win_rate']*100:.1f}%")
        print(f"   成功概率: {best_strategy['success_rate']*100:.1f}%")
        
        # 6. 月度收益分析
        monthly_profit = (best_strategy['median_final'] - 30000) / (3 * 12)
        single_position = 30000 * best_strategy['kelly_fraction']
        
        print(f"\n💰 收益分析:")
        print("="*30)
        print(f"   月均收益: {monthly_profit:+,.0f} 港币")
        print(f"   单次仓位: {single_position:,.0f} 港币")
        print(f"   月均交易: {best_strategy['max_trades_per_month']}次")
        print(f"   年均交易: {best_strategy['max_trades_per_month']*12}次")
    
    # 7. 与基准对比
    print(f"\n📊 与基准策略对比:")
    print("="*60)
    
    initial = 30000
    bank_deposit = initial * (1.02 ** 3)
    index_fund = initial * (1.05 ** 3)
    original_grid = 31335  # 原网格策略结果
    
    comparisons = [
        ('银行定期 (2%)', bank_deposit, 2.00),
        ('指数基金 (5%)', index_fund, 5.00),
        ('原网格策略', original_grid, 1.46),
        ('改进网格策略', best_strategy['median_final'], best_strategy['annual_return']*100)
    ]
    
    print("策略              最终金额   总收益   年化收益")
    print("-" * 50)
    
    for name, amount, annual in comparisons:
        profit = amount - initial
        print(f"{name:<15} {amount:>9,.0f}  {profit:>+8,.0f}  {annual:>+7.2f}%")
    
    # 8. 实战执行建议
    print(f"\n💡 实战执行建议:")
    print("="*40)
    print(f"✅ 推荐策略: {best_strategy['name']}")
    print(f"✅ 网格设置: {best_strategy['grid_spacing']*100:.0f}%间距，5层网格")
    print(f"✅ 仓位管理: {best_strategy['kelly_fraction']*100:.0f}%凯利比例")
    print(f"✅ 交易频率: 每月最多{best_strategy['max_trades_per_month']}次")
    print(f"✅ 风险控制: {best_strategy['stop_loss']*100:.0f}%止损，{best_strategy['take_profit']*100:.0f}%止盈")
    print(f"✅ 预期收益: 年化{best_strategy['annual_return']*100:.2f}%")
    
    # 9. 关键改进点
    print(f"\n🔧 关键改进点:")
    print("="*30)
    print("✅ 增大网格间距，减少无效交易")
    print("✅ 降低凯利比例，控制单笔风险")
    print("✅ 限制交易频率，避免过度交易")
    print("✅ 优化止损止盈比例")
    print("✅ 提高胜率预期")
    
    # 10. 风险管理
    print(f"\n🛡️ 风险管理:")
    print("="*20)
    print("⚠️ 设置总资金止损线 (50%)")
    print("⚠️ 定期评估策略表现")
    print("⚠️ 根据市场调整参数")
    print("⚠️ 避免情绪化交易")
    print("⚠️ 保持充足的现金储备")
    
    # 11. 适用性分析
    print(f"\n🎯 策略适用性:")
    print("="*25)
    
    if best_strategy['annual_return'] > 0.03:
        print("✅ 策略有明显改进，值得尝试")
        print("✅ 适合3-10万港币资金规模")
        print("✅ 需要一定的技术分析能力")
    elif best_strategy['annual_return'] > 0.02:
        print("⚠️ 策略有所改进，谨慎使用")
        print("⚠️ 建议与其他投资组合")
    else:
        print("❌ 改进效果有限，不建议使用")
    
    return best_strategy

if __name__ == "__main__":
    result = improved_grid_kelly()
