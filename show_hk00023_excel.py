#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示HK00023 Excel文件预览
========================

显示刚生成的HK00023东亚银行Excel文件内容

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import glob
import os

def show_hk00023_excel():
    """显示HK00023 Excel文件内容"""
    # 查找HK00023的Excel文件
    excel_files = glob.glob("HK00023东亚银行交易记录_*.xlsx")
    if not excel_files:
        print("❌ 未找到HK00023 Excel文件")
        return
    
    # 获取最新文件
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"🏦 HK00023东亚银行交易记录文件: {latest_file}")
    
    try:
        # 读取所有工作表
        df_trades = pd.read_excel(latest_file, sheet_name='交易记录')
        df_summary = pd.read_excel(latest_file, sheet_name='汇总统计')
        df_strategy = pd.read_excel(latest_file, sheet_name='策略说明')
        
        print(f"\n📊 HK00023交易记录 (共{len(df_trades)}条):")
        print("="*150)
        
        # 显示关键列
        key_columns = ['交易序号', '开仓日期', '平仓日期', '交易方向', '开仓价格', '平仓价格', 
                      '交易股数', '净利润', '收益率%', 'Y值', 'X值', 'E值', '策略区域', '平仓原因']
        
        print(df_trades[key_columns].to_string(index=False))
        
        print(f"\n📈 HK00023汇总统计:")
        print("="*60)
        for _, row in df_summary.iterrows():
            if pd.notna(row['数值']):
                print(f"{row['项目']}: {row['数值']}")
        
        print(f"\n🎯 Cosmoon博弈论策略说明:")
        print("="*80)
        for _, row in df_strategy.iterrows():
            print(f"• {row['策略区域']} ({row['条件']}): {row['操作']}")
            print(f"  止盈{row['止盈']}, 止损{row['止损']} - {row['说明']}")
            print()
        
        # 分析交易效果
        print(f"📊 HK00023交易分析:")
        print("="*60)
        
        # 按策略区域分析
        zone_analysis = df_trades.groupby('策略区域').agg({
            '净利润': ['count', 'sum', 'mean'],
            '收益率%': 'mean'
        }).round(2)
        
        print(f"按策略区域分析:")
        for zone in df_trades['策略区域'].unique():
            zone_data = df_trades[df_trades['策略区域'] == zone]
            count = len(zone_data)
            total_profit = zone_data['净利润'].sum()
            avg_profit = zone_data['净利润'].mean()
            win_rate = len(zone_data[zone_data['净利润'] > 0]) / count * 100
            
            print(f"• {zone}: {count}次交易, 总盈亏{total_profit:+.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # 按交易方向分析
        print(f"\n按交易方向分析:")
        for direction in df_trades['交易方向'].unique():
            dir_data = df_trades[df_trades['交易方向'] == direction]
            count = len(dir_data)
            total_profit = dir_data['净利润'].sum()
            avg_profit = dir_data['净利润'].mean()
            win_rate = len(dir_data[dir_data['净利润'] > 0]) / count * 100
            
            print(f"• {direction}: {count}次交易, 总盈亏{total_profit:+.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # E值分析
        print(f"\n按E值分析:")
        e_positive = df_trades[df_trades['E值'] > 0]
        e_negative = df_trades[df_trades['E值'] < 0]
        
        if len(e_positive) > 0:
            e_pos_profit = e_positive['净利润'].sum()
            e_pos_count = len(e_positive)
            e_pos_win_rate = len(e_positive[e_positive['净利润'] > 0]) / e_pos_count * 100
            print(f"• E>0: {e_pos_count}次交易, 总盈亏{e_pos_profit:+.0f}港币, 胜率{e_pos_win_rate:.1f}%")
        
        if len(e_negative) > 0:
            e_neg_profit = e_negative['净利润'].sum()
            e_neg_count = len(e_negative)
            e_neg_win_rate = len(e_negative[e_negative['净利润'] > 0]) / e_neg_count * 100
            print(f"• E<0: {e_neg_count}次交易, 总盈亏{e_neg_profit:+.0f}港币, 胜率{e_neg_win_rate:.1f}%")
        
        print(f"\n💡 HK00023 Excel文件包含:")
        print(f"   ✅ 20条完整的东亚银行交易记录")
        print(f"   ✅ 每笔交易的Y值、X值、E值")
        print(f"   ✅ 详细的策略区域和交易理由")
        print(f"   ✅ 完整的汇总统计和策略说明")
        print(f"   ✅ 30,000港币初始资金的回测结果")
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")

if __name__ == "__main__":
    show_hk00023_excel()
