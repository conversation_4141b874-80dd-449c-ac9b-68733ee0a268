#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的HK00023东亚银行20年历史数据回测
=====================================

优化策略：
1. 加入趋势判断机制
2. 优化高值盈利区参数
3. 动态调整止盈止损
4. 改进X、Y值计算

优化后规则：
- 牛市环境：偏向买涨策略
- 熊市环境：偏向买跌策略
- 震荡市：按原策略执行
- 动态止盈止损：根据波动率调整

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class OptimizedHK00023Backtester:
    def __init__(self):
        """初始化优化的HK00023回测器"""
        self.symbol = "0023.HK"
        self.initial_capital = 30000
        self.transaction_cost_rate = 0.0025
        
        # 策略参数
        self.high_profit_y = 0.43
        self.high_profit_x = 0.43
        self.control_zone_min = 0.333
        self.control_zone_max = 0.4
        self.strong_loss_y = 0.25
        self.strong_loss_x = 0.25
        
        # 趋势判断参数
        self.trend_window = 50  # 50日趋势
        self.volatility_window = 20  # 20日波动率
        
    def fetch_20year_data(self):
        """获取20年历史数据"""
        try:
            print("📊 获取HK00023东亚银行20年历史数据...")
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)
            
            print(f"   • 开始日期: {start_date.strftime('%Y-%m-%d')}")
            print(f"   • 结束日期: {end_date.strftime('%Y-%m-%d')}")
            
            ticker = yf.Ticker(self.symbol)
            data = ticker.history(start=start_date, end=end_date)
            
            if data.empty:
                print("❌ 无法获取数据")
                return None
            
            print(f"✅ 成功获取 {len(data)} 条历史数据")
            print(f"   • 价格范围: {data['Close'].min():.2f} - {data['Close'].max():.2f}港币")
            
            return data
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None
    
    def calculate_enhanced_indicators(self, data):
        """计算增强的技术指标"""
        try:
            print("\n🧮 计算增强的技术指标...")
            
            # 基础指标
            data['Price_Change'] = data['Close'].pct_change()
            data['Volume_Change'] = data['Volume'].pct_change()
            
            # 趋势指标
            data['MA20'] = data['Close'].rolling(window=20).mean()
            data['MA50'] = data['Close'].rolling(window=self.trend_window).mean()
            data['MA200'] = data['Close'].rolling(window=200).mean()
            
            # 趋势判断
            data['Trend'] = 'Neutral'
            data.loc[data['Close'] > data['MA50'] * 1.05, 'Trend'] = 'Bull'  # 牛市：价格超过50日均线5%
            data.loc[data['Close'] < data['MA50'] * 0.95, 'Trend'] = 'Bear'  # 熊市：价格低于50日均线5%
            
            # 波动率指标
            data['Volatility'] = data['Price_Change'].rolling(window=self.volatility_window).std()
            data['Volatility_Percentile'] = data['Volatility'].rolling(window=252).rank(pct=True)  # 年度波动率百分位
            
            # 改进的资金流指标
            data['Money_Flow'] = data['Close'] * data['Volume']
            data['MF_Ratio'] = data['Money_Flow'] / data['Money_Flow'].rolling(window=20).mean()
            
            # 改进的Y值计算（控制系数）
            data['Price_Stability'] = 1 / (1 + data['Volatility'] * 20)  # 价格稳定性
            data['Trend_Strength'] = abs(data['Close'] / data['MA20'] - 1)  # 趋势强度
            data['Y'] = (data['Price_Stability'] + (1 - data['Trend_Strength'])) / 2  # 综合控制系数
            
            # 改进的X值计算（资金流比例）
            data['Volume_Strength'] = np.tanh((data['Volume'] / data['Volume'].rolling(window=20).mean() - 1) * 2) * 0.5 + 0.5
            data['Price_Volume_Sync'] = np.sign(data['Price_Change']) == np.sign(data['Volume_Change'])
            data['X'] = data['Volume_Strength'] * (0.8 + 0.2 * data['Price_Volume_Sync'].astype(float))
            
            # E值计算
            data['E'] = 8 * data['X'] * data['Y'] - 3 * data['X'] - 3 * data['Y'] + 1
            
            # 删除NaN值
            data = data.dropna()
            
            print(f"✅ 计算完成，有效数据: {len(data)} 条")
            print(f"   • Y值范围: {data['Y'].min():.3f} - {data['Y'].max():.3f}")
            print(f"   • X值范围: {data['X'].min():.3f} - {data['X'].max():.3f}")
            print(f"   • 趋势分布: 牛市{(data['Trend']=='Bull').sum()}天, 熊市{(data['Trend']=='Bear').sum()}天, 震荡{(data['Trend']=='Neutral').sum()}天")
            
            return data
            
        except Exception as e:
            print(f"❌ 计算指标失败: {e}")
            return None
    
    def determine_optimized_strategy(self, row):
        """确定优化后的策略"""
        y_val = row['Y']
        x_val = row['X']
        trend = row['Trend']
        volatility_pct = row['Volatility_Percentile']
        
        # 动态调整止盈止损（基于波动率）
        if volatility_pct > 0.8:  # 高波动期
            base_profit = 0.05  # 5%
            base_loss = 0.025   # 2.5%
        elif volatility_pct < 0.2:  # 低波动期
            base_profit = 0.02  # 2%
            base_loss = 0.015   # 1.5%
        else:  # 正常波动期
            base_profit = 0.03  # 3%
            base_loss = 0.02    # 2%
        
        # 策略区域判断
        if y_val > self.high_profit_y and x_val > self.high_profit_x:
            zone = '高值盈利区'
            if trend == 'Bull':
                direction = '买涨'
                take_profit = base_profit * 1.5  # 牛市中增加止盈
                stop_loss = base_loss * 0.8      # 减少止损
            elif trend == 'Bear':
                direction = '买跌'
                take_profit = base_profit
                stop_loss = base_loss * 1.2
            else:  # Neutral
                direction = '买涨'  # 高值盈利区默认买涨
                take_profit = base_profit
                stop_loss = base_loss
                
        elif self.control_zone_min < y_val < self.control_zone_max:
            zone = '控股商控制区'
            direction = '观望'
            take_profit = 0
            stop_loss = 0
            
        elif y_val < self.strong_loss_y or x_val < self.strong_loss_x:
            zone = '强亏损区'
            if trend == 'Bull':
                direction = '买涨'  # 牛市中即使强亏损区也考虑买涨
                take_profit = base_profit * 0.8
                stop_loss = base_loss * 1.5
            else:
                direction = '买跌'
                take_profit = base_profit
                stop_loss = base_loss * 2
        else:
            zone = '其他区域'
            if trend == 'Bull':
                direction = '买涨'  # 牛市偏向买涨
                take_profit = base_profit
                stop_loss = base_loss * 1.2
            elif trend == 'Bear':
                direction = '买跌'
                take_profit = base_profit * 0.8
                stop_loss = base_loss * 1.8
            else:
                direction = '买跌'  # 震荡市默认买跌
                take_profit = base_profit * 0.6
                stop_loss = base_loss * 2
        
        return zone, direction, take_profit, stop_loss
    
    def run_optimized_backtest(self, data):
        """运行优化后的回测"""
        try:
            print("\n🎯 运行优化策略回测...")
            print("📊 优化要点:")
            print("   • 加入趋势判断：牛市偏向买涨，熊市偏向买跌")
            print("   • 动态止盈止损：根据波动率调整参数")
            print("   • 改进X、Y值计算：更准确的市场状态判断")
            print("   • 强亏损区优化：牛市中也考虑买涨")
            
            current_capital = self.initial_capital
            position = 0
            entry_price = 0
            entry_date = None
            strategy_zone = None
            direction = None
            profit_target = 0
            loss_limit = 0
            
            trades = []
            daily_capital = []
            
            for i, (date, row) in enumerate(data.iterrows()):
                current_price = row['Close']
                
                # 如果没有持仓，考虑开仓
                if position == 0:
                    zone, trade_direction, take_profit_pct, stop_loss_pct = self.determine_optimized_strategy(row)
                    
                    if trade_direction != '观望':
                        shares = 100
                        position_value = shares * current_price
                        
                        if current_capital >= position_value:
                            position = shares if trade_direction == '买涨' else -shares
                            entry_price = current_price
                            entry_date = date
                            strategy_zone = zone
                            direction = trade_direction
                            
                            if trade_direction == '买涨':
                                profit_target = entry_price * (1 + take_profit_pct)
                                loss_limit = entry_price * (1 - stop_loss_pct)
                            else:  # 买跌
                                profit_target = entry_price * (1 - take_profit_pct)
                                loss_limit = entry_price * (1 + stop_loss_pct)
                            
                            current_capital -= position_value
                
                # 检查平仓条件
                elif position != 0:
                    should_close = False
                    close_reason = ""
                    
                    if direction == '买涨':
                        if current_price >= profit_target:
                            should_close = True
                            close_reason = "止盈"
                        elif current_price <= loss_limit:
                            should_close = True
                            close_reason = "止损"
                    else:  # 买跌
                        if current_price <= profit_target:
                            should_close = True
                            close_reason = "止盈"
                        elif current_price >= loss_limit:
                            should_close = True
                            close_reason = "止损"
                    
                    if should_close:
                        shares = abs(position)
                        close_value = shares * current_price
                        
                        if direction == '买涨':
                            gross_profit = (current_price - entry_price) * shares
                        else:
                            gross_profit = (entry_price - current_price) * shares
                        
                        transaction_cost = (shares * entry_price + close_value) * self.transaction_cost_rate
                        net_profit = gross_profit - transaction_cost
                        
                        current_capital += close_value + net_profit
                        
                        trades.append({
                            'entry_date': entry_date,
                            'exit_date': date,
                            'strategy_zone': strategy_zone,
                            'direction': direction,
                            'entry_price': entry_price,
                            'exit_price': current_price,
                            'profit_target': profit_target,
                            'loss_limit': loss_limit,
                            'shares': shares,
                            'gross_profit': gross_profit,
                            'net_profit': net_profit,
                            'close_reason': close_reason,
                            'capital_after': current_capital,
                            'trend': row['Trend'],
                            'volatility_pct': row['Volatility_Percentile'],
                            'y_val': row['Y'],
                            'x_val': row['X'],
                            'e_val': row['E']
                        })
                        
                        position = 0
                        entry_price = 0
                        entry_date = None
                
                total_value = current_capital
                if position != 0:
                    total_value += abs(position) * current_price
                daily_capital.append(total_value)
            
            print(f"✅ 优化策略回测完成")
            print(f"   • 总交易次数: {len(trades)}")
            print(f"   • 最终资金: {current_capital:,.0f}港币")
            print(f"   • 总收益: {current_capital - self.initial_capital:+,.0f}港币")
            print(f"   • 总收益率: {(current_capital / self.initial_capital - 1) * 100:+.2f}%")
            
            return trades, daily_capital
            
        except Exception as e:
            print(f"❌ 优化回测失败: {e}")
            return None, None
    
    def analyze_optimized_results(self, trades):
        """分析优化后的结果"""
        if not trades:
            print("❌ 没有交易记录")
            return
        
        print(f"\n📊 优化策略详细分析:")
        print("="*140)
        
        df = pd.DataFrame(trades)
        
        # 显示前20笔交易
        print(f"前20笔交易记录:")
        print("-" * 140)
        print(f"{'序号':<4} {'策略区域':<12} {'方向':<6} {'趋势':<6} {'开仓价':<8} {'平仓价':<8} "
              f"{'止盈价':<8} {'止损价':<8} {'结果':<8} {'净利润':<8}")
        print("-" * 140)
        
        for i, trade in enumerate(trades[:20]):
            print(f"{i+1:<4} {trade['strategy_zone']:<12} {trade['direction']:<6} {trade['trend']:<6} "
                  f"{trade['entry_price']:<8.2f} {trade['exit_price']:<8.2f} "
                  f"{trade['profit_target']:<8.2f} {trade['loss_limit']:<8.2f} "
                  f"{trade['close_reason']:<8} {trade['net_profit']:<8.0f}")
        
        # 统计分析
        print(f"\n📈 优化策略统计分析:")
        print("-" * 80)
        
        total_trades = len(trades)
        winning_trades = len(df[df['net_profit'] > 0])
        
        print(f"• 总交易次数: {total_trades}")
        print(f"• 盈利交易: {winning_trades} ({winning_trades/total_trades*100:.1f}%)")
        print(f"• 胜率: {winning_trades/total_trades*100:.1f}%")
        print(f"• 总盈利: {df['net_profit'].sum():+,.0f}港币")
        print(f"• 平均盈利: {df['net_profit'].mean():+.0f}港币/笔")
        print(f"• 最大盈利: {df['net_profit'].max():+.0f}港币")
        print(f"• 最大亏损: {df['net_profit'].min():+.0f}港币")
        
        # 按趋势环境分析
        print(f"\n📊 按趋势环境分析:")
        print("-" * 80)
        
        for trend in ['Bull', 'Bear', 'Neutral']:
            trend_data = df[df['trend'] == trend]
            if len(trend_data) > 0:
                count = len(trend_data)
                total_profit = trend_data['net_profit'].sum()
                wins = len(trend_data[trend_data['net_profit'] > 0])
                
                print(f"• {trend}市环境:")
                print(f"  - 交易次数: {count}")
                print(f"  - 总盈亏: {total_profit:+,.0f}港币")
                print(f"  - 胜率: {wins/count*100:.1f}%")
                print(f"  - 平均盈亏: {total_profit/count:+.0f}港币")
        
        # 按策略区域分析
        print(f"\n📊 按策略区域分析:")
        print("-" * 80)
        
        for zone in df['strategy_zone'].unique():
            zone_data = df[df['strategy_zone'] == zone]
            count = len(zone_data)
            total_profit = zone_data['net_profit'].sum()
            wins = len(zone_data[zone_data['net_profit'] > 0])
            
            print(f"• {zone}:")
            print(f"  - 交易次数: {count}")
            print(f"  - 总盈亏: {total_profit:+,.0f}港币")
            print(f"  - 胜率: {wins/count*100:.1f}%")
            print(f"  - 平均盈亏: {total_profit/count:+.0f}港币")
        
        # 优化效果对比
        print(f"\n🔍 优化效果对比:")
        print("-" * 60)
        print(f"• 原始策略: -31.31% (-9,392港币)")
        print(f"• 调整参数: -29.83% (-8,949港币)")
        print(f"• 优化策略: {(df['net_profit'].sum()/30000)*100:+.2f}% ({df['net_profit'].sum():+,.0f}港币)")
        
        improvement_vs_original = df['net_profit'].sum() - (-9392)
        improvement_vs_adjusted = df['net_profit'].sum() - (-8949)
        print(f"• vs原始策略改进: {improvement_vs_original:+,.0f}港币")
        print(f"• vs调整参数改进: {improvement_vs_adjusted:+,.0f}港币")
        
        return df

def main():
    """主函数"""
    print("🏦 HK00023东亚银行20年历史回测 (优化策略)")
    print("="*60)
    print("📊 优化策略特点:")
    print("   • 趋势判断：牛市偏向买涨，熊市偏向买跌")
    print("   • 动态止盈止损：根据波动率调整")
    print("   • 改进指标计算：更准确的X、Y值")
    print("   • 智能区域策略：根据市场环境调整")
    print("   • 初始资金: 30,000港币")
    
    backtester = OptimizedHK00023Backtester()
    
    data = backtester.fetch_20year_data()
    if data is None:
        return
    
    data = backtester.calculate_enhanced_indicators(data)
    if data is None:
        return
    
    trades, daily_capital = backtester.run_optimized_backtest(data)
    if trades is None:
        return
    
    backtester.analyze_optimized_results(trades)
    
    print(f"\n🎉 HK00023东亚银行20年优化策略回测完成!")

if __name__ == "__main__":
    main()
