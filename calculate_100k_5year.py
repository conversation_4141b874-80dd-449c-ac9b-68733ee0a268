#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
10万港币投资5年收益计算 - 博弈论策略
"""

import numpy as np
import pandas as pd

def calculate_100k_5year():
    """计算10万港币5年投资收益"""
    
    print("💰 博弈论投资策略 - 10万港币5年投资")
    print("="*70)
    print("📊 初始投资: 100,000 港币")
    print("📅 投资期限: 5年")
    print("🎯 策略: Y≥X≥0.4 + 优化仓位管理")
    
    # 基于历史验证的策略参数
    strategy_params = {
        'initial_capital': 100000,
        'win_rate': 0.513,         # 胜率 51.3%
        'avg_win': 0.10,           # 平均盈利 10%
        'avg_loss': 0.10,          # 平均亏损 10%
        'trades_per_year': 34,     # 年均交易次数
        'years': 5,
    }
    
    print(f"\n📊 策略参数:")
    print(f"   胜率: {strategy_params['win_rate']*100:.1f}%")
    print(f"   平均盈利: {strategy_params['avg_win']*100:.0f}%")
    print(f"   平均亏损: {strategy_params['avg_loss']*100:.0f}%")
    print(f"   年均交易: {strategy_params['trades_per_year']}次")
    print(f"   总交易数: {strategy_params['trades_per_year'] * strategy_params['years']}次")
    
    # 不同仓位策略对比
    position_strategies = [
        {'name': '保守策略', 'position': 0.10, 'description': '10%仓位，低风险'},
        {'name': '平衡策略', 'position': 0.15, 'description': '15%仓位，中等风险'},
        {'name': '积极策略', 'position': 0.20, 'description': '20%仓位，较高风险'},
        {'name': '激进策略', 'position': 0.25, 'description': '25%仓位，高风险'},
        {'name': '凯利优化', 'position': 0.035, 'description': '3.5%仓位，数学最优'},
    ]
    
    def monte_carlo_simulation(position_size, num_simulations=10000):
        """蒙特卡洛模拟"""
        
        np.random.seed(42)
        total_trades = strategy_params['trades_per_year'] * strategy_params['years']
        
        final_amounts = []
        yearly_returns = []
        max_drawdowns = []
        
        for sim in range(num_simulations):
            capital = strategy_params['initial_capital']
            capital_history = [capital]
            peak_capital = capital
            max_drawdown = 0
            
            for trade in range(total_trades):
                position = capital * position_size
                
                # 模拟交易结果
                if np.random.random() < strategy_params['win_rate']:
                    trade_return = strategy_params['avg_win']
                else:
                    trade_return = -strategy_params['avg_loss']
                
                pnl = position * trade_return
                capital += pnl
                capital = max(capital, 0)  # 防止负值
                
                capital_history.append(capital)
                
                # 计算回撤
                if capital > peak_capital:
                    peak_capital = capital
                current_drawdown = (peak_capital - capital) / peak_capital
                max_drawdown = max(max_drawdown, current_drawdown)
            
            final_amounts.append(capital)
            max_drawdowns.append(max_drawdown)
            
            # 计算年化收益
            annual_return = ((capital / strategy_params['initial_capital']) ** (1/strategy_params['years'])) - 1
            yearly_returns.append(annual_return)
        
        return {
            'final_amounts': final_amounts,
            'yearly_returns': yearly_returns,
            'max_drawdowns': max_drawdowns
        }
    
    # 执行所有策略模拟
    print(f"\n📊 不同仓位策略对比 (5年):")
    print("="*90)
    print("策略       仓位   最终金额   总收益    年化收益   最大回撤   夏普比率   推荐度")
    print("-" * 90)
    
    all_results = []
    
    for strategy in position_strategies:
        results = monte_carlo_simulation(strategy['position'])
        
        # 统计分析
        median_final = np.median(results['final_amounts'])
        mean_final = np.mean(results['final_amounts'])
        std_final = np.std(results['final_amounts'])
        
        median_annual = np.median(results['yearly_returns'])
        std_annual = np.std(results['yearly_returns'])
        
        median_drawdown = np.median(results['max_drawdowns'])
        
        # 计算夏普比率 (假设无风险利率2%)
        risk_free_rate = 0.02
        sharpe_ratio = (median_annual - risk_free_rate) / std_annual if std_annual > 0 else 0
        
        # 推荐度评分
        score = 0
        if median_annual > 0.03: score += 1  # 年化>3%
        if median_annual > 0.05: score += 1  # 年化>5%
        if median_annual > 0.08: score += 1  # 年化>8%
        if median_drawdown < 0.15: score += 1  # 回撤<15%
        if median_drawdown < 0.10: score += 1  # 回撤<10%
        if sharpe_ratio > 0.3: score += 1   # 夏普>0.3
        if sharpe_ratio > 0.5: score += 1   # 夏普>0.5
        
        recommendation = "⭐" * min(score, 5)
        
        total_profit = median_final - strategy_params['initial_capital']
        
        print(f"{strategy['name']:<10} {strategy['position']*100:>4.0f}%  {median_final:>9,.0f}  {total_profit:>+8,.0f}  {median_annual*100:>+7.2f}%   {median_drawdown*100:>6.1f}%    {sharpe_ratio:>6.2f}    {recommendation}")
        
        all_results.append({
            **strategy,
            'median_final': median_final,
            'total_profit': total_profit,
            'annual_return': median_annual,
            'max_drawdown': median_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'score': score,
            'results': results
        })
    
    # 找出最佳策略
    best_strategy = max(all_results, key=lambda x: x['score'])
    
    print(f"\n🏆 推荐策略: {best_strategy['name']}")
    print("="*60)
    print(f"📊 {best_strategy['description']}")
    print(f"   仓位大小: {best_strategy['position']*100:.0f}%")
    print(f"   预期最终金额: {best_strategy['median_final']:,.0f} 港币")
    print(f"   预期总收益: {best_strategy['total_profit']:+,.0f} 港币")
    print(f"   年化收益率: {best_strategy['annual_return']*100:+.2f}%")
    print(f"   最大回撤: {best_strategy['max_drawdown']*100:.1f}%")
    print(f"   夏普比率: {best_strategy['sharpe_ratio']:.2f}")
    
    # 详细收益分析
    print(f"\n💰 详细收益分析:")
    print("="*50)
    
    single_position = strategy_params['initial_capital'] * best_strategy['position']
    annual_trades = strategy_params['trades_per_year']
    
    # 年度收益预期
    annual_wins = annual_trades * strategy_params['win_rate']
    annual_losses = annual_trades * (1 - strategy_params['win_rate'])
    
    annual_profit = annual_wins * single_position * strategy_params['avg_win']
    annual_loss = annual_losses * single_position * strategy_params['avg_loss']
    net_annual = annual_profit - annual_loss
    
    print(f"   单次仓位: {single_position:,.0f} 港币")
    print(f"   年均盈利交易: {annual_wins:.1f} 次")
    print(f"   年均亏损交易: {annual_losses:.1f} 次")
    print(f"   年预期盈利: {annual_profit:,.0f} 港币")
    print(f"   年预期亏损: {annual_loss:,.0f} 港币")
    print(f"   年净收益: {net_annual:+,.0f} 港币")
    print(f"   月均收益: {net_annual/12:+,.0f} 港币")
    
    # 概率分析
    results = best_strategy['results']
    
    print(f"\n📊 概率分析:")
    print("="*40)
    
    percentiles = [5, 25, 50, 75, 95]
    print("概率     最终金额     总收益")
    print("-" * 30)
    
    for p in percentiles:
        final_amount = np.percentile(results['final_amounts'], p)
        profit = final_amount - strategy_params['initial_capital']
        print(f"{p:>2}%     {final_amount:>9,.0f}    {profit:>+8,.0f}")
    
    # 风险分析
    print(f"\n🛡️ 风险分析:")
    print("="*30)
    
    loss_probability = sum(1 for x in results['final_amounts'] if x < strategy_params['initial_capital']) / len(results['final_amounts'])
    severe_loss_prob = sum(1 for x in results['final_amounts'] if x < strategy_params['initial_capital'] * 0.85) / len(results['final_amounts'])
    
    print(f"   亏损概率: {loss_probability*100:.1f}%")
    print(f"   严重亏损概率 (>15%): {severe_loss_prob*100:.1f}%")
    print(f"   单笔最大风险: {single_position * strategy_params['avg_loss']:,.0f} 港币")
    print(f"   建议止损线: 总资金的15% ({strategy_params['initial_capital']*0.15:,.0f}港币)")
    
    # 与其他投资对比
    print(f"\n📊 投资对比 (5年):")
    print("="*50)
    
    # 其他投资选项
    bank_deposit = strategy_params['initial_capital'] * (1.02 ** 5)
    index_fund = strategy_params['initial_capital'] * (1.05 ** 5)
    bond_fund = strategy_params['initial_capital'] * (1.035 ** 5)
    
    print(f"   银行定期 (2%): {bank_deposit:,.0f} 港币 (收益{bank_deposit-100000:+,.0f})")
    print(f"   债券基金 (3.5%): {bond_fund:,.0f} 港币 (收益{bond_fund-100000:+,.0f})")
    print(f"   指数基金 (5%): {index_fund:,.0f} 港币 (收益{index_fund-100000:+,.0f})")
    print(f"   博弈论策略: {best_strategy['median_final']:,.0f} 港币 (收益{best_strategy['total_profit']:+,.0f})")
    
    # 超额收益分析
    excess_vs_bank = best_strategy['total_profit'] - (bank_deposit - 100000)
    excess_vs_index = best_strategy['total_profit'] - (index_fund - 100000)
    
    print(f"\n📈 超额收益:")
    print(f"   vs 银行定期: {excess_vs_bank:+,.0f} 港币")
    print(f"   vs 指数基金: {excess_vs_index:+,.0f} 港币")
    
    # 实战执行计划
    print(f"\n💡 实战执行计划:")
    print("="*40)
    print(f"✅ 推荐仓位: {best_strategy['position']*100:.0f}%")
    print(f"✅ 单笔投资: {single_position:,.0f} 港币")
    print(f"✅ 止损设定: 10% ({single_position*0.1:,.0f}港币)")
    print(f"✅ 止盈设定: 10% ({single_position*0.1:,.0f}港币)")
    print(f"✅ 最大持有: 60天")
    print(f"✅ 年度目标: {net_annual:+,.0f} 港币")
    print(f"✅ 月度目标: {net_annual/12:+,.0f} 港币")
    
    # 分年度规划
    print(f"\n📅 5年投资规划:")
    print("="*40)
    
    compound_growth = best_strategy['annual_return']
    
    for year in range(1, 6):
        year_end_amount = strategy_params['initial_capital'] * ((1 + compound_growth) ** year)
        year_profit = year_end_amount - strategy_params['initial_capital']
        
        print(f"   第{year}年末: {year_end_amount:,.0f} 港币 (累计收益{year_profit:+,.0f})")
    
    # 成功要素
    print(f"\n🎯 成功要素:")
    print("="*30)
    print("✅ 严格执行Y≥X≥0.4信号")
    print("✅ 坚持10%止损止盈纪律")
    print("✅ 保持理性，避免情绪交易")
    print("✅ 定期回顾和优化策略")
    print("✅ 分散投资时间，避免集中风险")
    
    return {
        'recommended_strategy': best_strategy['name'],
        'position_size': best_strategy['position'],
        'expected_final': best_strategy['median_final'],
        'expected_profit': best_strategy['total_profit'],
        'annual_return': best_strategy['annual_return']
    }

if __name__ == "__main__":
    results = calculate_100k_5year()
