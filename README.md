# 🎯 博弈论投资策略系统

## 📖 系统简介

这是一个基于博弈论的完整投资策略系统，整合了多种分析工具和计算功能。

### 💡 核心投资理论
- **买入条件**: X>0.4 且 Y>0.4 (资金流入比例>0.4 且 博弈论概率>0.4)
- **卖出条件**: X<0.4 (资金流入比例<0.4)
- **理论基础**: 基于资金流向和博弈论概率的完整交易系统

## 🚀 快速开始

### 启动主菜单系统
```bash
python game_investment_menu.py
```

### 推荐使用流程
1. **新手入门**: 选择功能1 - 完整博弈论策略分析
2. **实时交易**: 选择功能15-16 进行实时决策
3. **投资规划**: 选择功能6-10 进行收益计算
4. **策略验证**: 选择功能11-14 进行回测验证
5. **系统维护**: 定期运行功能17 检查数据库状态

## 📊 功能模块详解

### 🎯 策略分析功能 (1-5)

#### 1. 完整博弈论策略分析 ⭐⭐⭐⭐⭐
- **文件**: `complete_game_theory_strategy.py`
- **功能**: 最完整的策略分析，包含买卖条件
- **特点**: 年化收益率26.67%，平均持有45天
- **推荐**: 所有用户必用

#### 2. 纯Y>0.4且X>0.4策略分析
- **文件**: `pure_y_x_04_strategy_10k_2year.py`
- **功能**: 简化版策略，固定持有期
- **特点**: 年化收益率9.71%，持有120天

#### 3. 改进版网格+凯利策略 ⭐⭐⭐⭐⭐
- **文件**: `improved_grid_kelly.py`
- **功能**: 网格交易结合凯利公式
- **特点**: 年化收益率30.60%，适合高频交易

#### 4. HK2800 ETF策略测试 ⭐⭐⭐⭐
- **文件**: `test_hk2800_strategy.py`
- **功能**: 基于盈富基金的ETF策略
- **特点**: 年化收益率46.67%

#### 5. Y=X策略分析
- **文件**: `y_equals_x_strategy.py`
- **功能**: 当Y概率等于X资金流入比例时的策略

### 💰 投资收益计算 (6-10)

#### 6. 1万元2年投资计算
- **文件**: `calculate_10k_rmb_2year.py`
- **功能**: 计算1万元人民币2年投资收益
- **结果**: 预期收益+2,036元

#### 7. 3万元3年投资计算
- **文件**: `calculate_30k_3year_returns.py`
- **功能**: 计算3万元3年投资收益

#### 8. 10万元5年投资计算
- **文件**: `calculate_100k_5year.py`
- **功能**: 计算10万元5年投资收益

#### 9. 自定义金额和时间计算
- **功能**: 用户输入任意金额和时间进行计算
- **特点**: 灵活性强，适合个性化需求

#### 10. 目标收益反推计算
- **文件**: `calculate_5k_profit_target.py`
- **功能**: 根据目标收益反推所需资金和时间
- **示例**: 要获得5000元收益需要25,000元投资2年

### 📈 回测验证功能 (11-14)

#### 11. 优化版Y>X>0.4回测 ⭐⭐⭐⭐
- **文件**: `optimized_y_x_04_backtest.py`
- **功能**: 基于历史数据的优化回测
- **特点**: 包含多种仓位和持有期对比

#### 12. 简化版Y>X>0.4回测
- **文件**: `simple_y_x_04_backtest.py`
- **功能**: 简化版回测，快速验证

#### 13. 凯利公式优化回测
- **文件**: `kelly_optimized_backtest.py`
- **功能**: 基于凯利公式的仓位优化

#### 14. 扩展策略回测
- **文件**: `extended_strategy_backtest.py`
- **功能**: 多策略组合回测

### 📱 实时工具 (15-16)

#### 15. 实时投资计算参考 ⭐⭐⭐⭐⭐ (新增)
- **文件**: `realtime_investment_calculator.py`
- **功能**: 基于当前市场条件的实时投资决策辅助
- **特点**:
  - 实时信号判断 (买入/卖出/观望)
  - 仓位计算和风险评估
  - 目标价位和预期收益分析
  - 市场状态评估
- **适用**: 日常交易决策

#### 16. 快速信号检查
- **文件**: `realtime_investment_calculator.py`
- **功能**: 快速检查当前Y和X指标的交易信号
- **特点**: 简化版实时判断，快速响应

### 🔧 系统工具 (17-20)

#### 17. 数据库状态检查 ⭐⭐⭐⭐
- **文件**: `check_database_status.py`
- **功能**: 检查数据库连接和数据完整性
- **重要性**: 确保策略正常运行的基础

#### 18. 策略优化分析
- **文件**: `strategy_optimization.py`
- **功能**: 策略参数优化和对比分析

#### 19. 重要文件清单
- **功能**: 显示所有文件状态和重要性评级
- **用途**: 系统完整性检查

#### 20. 数据转换工具
- **文件**: `convert_to_hk2800.py`
- **功能**: 数据格式转换和处理

## 📋 投资策略对比

| 策略类型 | 年化收益率 | 平均持有期 | 交易频率 | 风险等级 | 推荐指数 |
|----------|------------|------------|----------|----------|----------|
| 完整博弈论策略 | 26.67% | 45天 | 35次/年 | 中 | ⭐⭐⭐⭐⭐ |
| 网格+凯利策略 | 30.60% | 灵活 | 高频 | 中高 | ⭐⭐⭐⭐⭐ |
| HK2800策略 | 46.67% | 灵活 | 中频 | 中高 | ⭐⭐⭐⭐ |
| 纯Y>X>0.4策略 | 9.71% | 120天 | 24次/年 | 中低 | ⭐⭐⭐ |

## 💡 使用建议

### 🎯 新手用户
1. 先运行功能1了解完整策略
2. 使用功能15-16进行实时信号判断
3. 使用功能6-10进行投资规划
4. 小资金测试3-6个月

### 🎯 有经验用户
1. 运行功能11-14进行回测验证
2. 使用功能3尝试高频策略
3. 使用功能15进行日常交易决策
4. 定期运行功能17检查系统状态

### 🎯 日常交易流程
1. 每日运行功能16快速检查信号
2. 买入时运行功能15详细计算
3. 持仓期间监控X指标变化
4. 及时响应卖出信号

### 🎯 资金配置建议

#### 保守型 (推荐新手)
- 40% 博弈论策略
- 60% 指数基金
- 风险等级: 低

#### 平衡型 (推荐)
- 60% 博弈论策略
- 40% 指数基金
- 风险等级: 中

#### 激进型 (有经验用户)
- 80% 博弈论策略
- 20% 指数基金
- 风险等级: 中高

## ⚠️ 重要提醒

### 🔑 成功关键因素
- ✅ 严格执行买卖条件
- ✅ 及时响应X指标变化
- ✅ 保持仓位纪律
- ✅ 避免情绪化交易
- ✅ 持续监控市场指标

### ❗ 风险提示
- 需要实时获取Y和X数据
- 交易频率较高，需要时间精力
- 市场极端情况下策略可能失效
- 收益预测基于历史数据，实际可能有差异
- 建议先小资金测试验证

## 🛠️ 技术要求

### 环境依赖
- Python 3.7+
- numpy
- pandas
- pymysql (如需数据库功能)

### 数据要求
- Y概率数据 (博弈论概率)
- X资金流入比例数据
- 历史价格数据

## 📞 技术支持

如遇到问题，请检查：
1. 文件完整性 (运行功能17)
2. 数据库连接 (运行功能15)
3. Python环境和依赖包

---

**免责声明**: 本系统仅供学习和研究使用，投资有风险，入市需谨慎。过往业绩不代表未来表现。
