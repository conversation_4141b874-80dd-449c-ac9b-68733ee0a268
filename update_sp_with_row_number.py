#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新存储过程 - 让i从1开始对应第一条记录
=====================================

修改存储过程，使用ROW_NUMBER()让i从1开始，
而不是依赖AUTO_INCREMENT的i字段

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

# 数据库配置
db_config = {
    'host': '************',
    'user': 'root',
    'password': '',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def create_updated_stored_procedure():
    """创建更新的存储过程，i从1开始"""
    print("🔧 创建更新的存储过程...")
    
    # 分别执行SQL语句
    sqls = [
        "DROP PROCEDURE IF EXISTS sp_stock_analysis_with_row_coefficients",
        """
        CREATE PROCEDURE sp_stock_analysis_with_row_coefficients(IN tablename VARCHAR(64))
        BEGIN
            DECLARE stmt_sql TEXT;
            
            -- 0. 确保临时表不存在
            DROP TEMPORARY TABLE IF EXISTS temp_results;
            
            -- 1. 创建临时表存储计算结果，使用ROW_NUMBER()从1开始
            SET stmt_sql = CONCAT('
                CREATE TEMPORARY TABLE temp_results AS
                SELECT 
                    Date,
                    ROW_NUMBER() OVER (ORDER BY Date) AS i,
                    SUM(CASE WHEN Controller = 1 THEN 1 ELSE 0 END) OVER (ORDER BY Date) / 
                    ROW_NUMBER() OVER (ORDER BY Date) AS row_control_coefficient
                FROM 
                    `', tablename, '`
                ORDER BY Date
            ');
            PREPARE stmt FROM stmt_sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            -- 2. 更新原表的Full_Y字段
            SET stmt_sql = CONCAT('
                UPDATE `', tablename, '` t
                JOIN temp_results tr ON t.Date = tr.Date
                SET t.Full_Y = tr.row_control_coefficient
            ');
            PREPARE stmt FROM stmt_sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            -- 3. 重新计算E值
            SET stmt_sql = CONCAT('
                UPDATE `', tablename, '`
                SET E = 8 * MoneyFlowRatio * Full_Y - 3 * MoneyFlowRatio - 3 * Full_Y + 1
                WHERE MoneyFlowRatio IS NOT NULL AND Full_Y IS NOT NULL
            ');
            PREPARE stmt FROM stmt_sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            -- 4. 输出结果，使用ROW_NUMBER()作为i
            SET stmt_sql = CONCAT('
                SELECT 
                    ROW_NUMBER() OVER (ORDER BY Date) AS i,
                    Close AS close,
                    Midprice AS midprice,
                    Controller AS controller,
                    Full_Y AS full_Y,
                    MoneyFlowRatio,
                    E,
                    Date
                FROM 
                    `', tablename, '`
                ORDER BY 
                    Date
                LIMIT 20
            ');
            PREPARE stmt FROM stmt_sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            -- 5. 清理临时表
            DROP TEMPORARY TABLE IF EXISTS temp_results;
            
            SELECT CONCAT('存储过程执行完成，已更新表: ', tablename, '，i从1开始编号') AS 结果;
            
        END
        """
    ]
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 执行创建存储过程的SQL
        for sql in sqls:
            sql = sql.strip()
            if sql:
                cursor.execute(sql)
        
        conn.commit()
        print("✅ 更新的存储过程创建成功")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建存储过程失败: {e}")
        return False

def test_updated_procedure():
    """测试更新的存储过程"""
    print("\n🧪 测试更新的存储过程...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 调用存储过程
        cursor.execute("CALL sp_stock_analysis_with_row_coefficients('hkhsi50')")
        
        # 获取结果
        rows = cursor.fetchall()
        
        if rows:
            print(f"📊 存储过程返回 {len(rows)} 条结果:")
            
            # 显示前10行结果
            print(f"{'i':<4} {'日期':<12} {'收盘价':<10} {'中位价':<10} {'控股商':<6} {'Full_Y':<8} {'资金流':<8} {'E值':<8}")
            print("-" * 80)
            
            for row in rows[:10]:  # 只显示前10行
                i = row[0]
                close = row[1]
                midprice = row[2]
                controller = row[3]
                full_y = row[4]
                money_flow = row[5]
                e_value = row[6]
                date = row[7]
                
                print(f"{i:<4} {str(date):<12} {close:<10.2f} {midprice:<10.2f} {controller:<6} {full_y:<8.4f} {money_flow:<8.3f} {e_value:<8.3f}")
        else:
            print("⚠️  存储过程没有返回结果")
        
        # 验证i的范围
        cursor.execute("""
            SELECT
                COUNT(*) as 总记录数,
                1 as 最小i,
                COUNT(*) as 最大i
            FROM hkhsi50
        """)
        
        stats = cursor.fetchone()
        print(f"\n📈 i值验证:")
        print(f"   • 总记录数: {stats[0]}")
        print(f"   • i值范围: {stats[1]} - {stats[2]}")
        print(f"   • 确认: i从1开始到{stats[0]}结束 ✅")
        
        # 显示Full_Y的计算验证
        cursor.execute("""
            SELECT 
                ROW_NUMBER() OVER (ORDER BY Date) AS i,
                Date,
                Controller,
                Full_Y,
                SUM(CASE WHEN Controller = 1 THEN 1 ELSE 0 END) OVER (ORDER BY Date) AS 累计控股商天数,
                ROW_NUMBER() OVER (ORDER BY Date) AS 累计天数
            FROM hkhsi50 
            ORDER BY Date 
            LIMIT 10
        """)
        
        calc_rows = cursor.fetchall()
        print(f"\n🧮 Full_Y计算验证 (前10条):")
        print(f"{'i':<4} {'日期':<12} {'控股商':<6} {'Full_Y':<8} {'累计控股商':<10} {'累计天数':<8} {'计算验证':<10}")
        print("-" * 80)
        
        for row in calc_rows:
            i = row[0]
            date = row[1]
            controller = row[2]
            full_y = row[3]
            cum_controller = row[4]
            cum_days = row[5]
            calculated = cum_controller / cum_days if cum_days > 0 else 0
            
            print(f"{i:<4} {str(date):<12} {controller:<6} {full_y:<8.4f} {cum_controller:<10} {cum_days:<8} {calculated:<10.4f}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_comparison():
    """显示修改前后的对比"""
    print("\n📊 修改前后对比:")
    print("="*60)
    print("🔧 修改前:")
    print("   • 使用AUTO_INCREMENT的i字段")
    print("   • i值可能不连续或不从1开始")
    print("   • 依赖数据库的自增机制")
    
    print("\n✅ 修改后:")
    print("   • 使用ROW_NUMBER() OVER (ORDER BY Date)")
    print("   • i值从1开始，连续递增")
    print("   • 按日期排序，确保逻辑顺序")
    print("   • 计算更准确的行级控制系数")
    
    print("\n🎯 优势:")
    print("   • i=1 对应最早的记录")
    print("   • i=n 对应第n条记录")
    print("   • Full_Y计算基于正确的行号")
    print("   • 结果更符合预期")

def main():
    """主函数"""
    print("🎯 更新存储过程 - i从1开始对应第一条记录")
    print("="*60)
    
    # 显示修改说明
    show_comparison()
    
    # 1. 创建更新的存储过程
    if not create_updated_stored_procedure():
        return
    
    # 2. 测试存储过程
    if test_updated_procedure():
        print("\n🎉 存储过程更新和测试完成！")
        print("💡 现在可以使用:")
        print("   CALL sp_stock_analysis_with_row_coefficients('hkhsi50');")
        print("📊 特点:")
        print("   • i从1开始对应第一条记录")
        print("   • 按日期排序计算行级控制系数")
        print("   • Full_Y基于正确的累积比例计算")
    else:
        print("\n❌ 存储过程测试失败")

if __name__ == "__main__":
    main()
