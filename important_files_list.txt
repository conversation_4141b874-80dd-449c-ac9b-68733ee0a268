博弈论投资策略 - 重要文件清单
=====================================

🏆 最重要文件（必须转移）:
--------------------------
1. improved_grid_kelly.py - 改进版网格+凯利策略 ⭐⭐⭐⭐⭐
   - 年化30.60%收益的最佳策略
   - 3万港币3年收益36,824港币

2. optimized_y_x_04_backtest.py - 优化版Y>0.4且X>0.4策略 ⭐⭐⭐⭐
   - 5万港币10年回测，年化9.71%
   - 简单易执行的策略

3. test_hk2800_strategy.py - HK2800策略测试 ⭐⭐⭐⭐
   - 盈富基金策略，年化46.67%
   - ETF投资的最佳选择

4. check_database_status.py - 数据库状态检查 ⭐⭐⭐⭐
   - 检查数据完整性
   - 确保策略可正常运行

📊 数据库文件（重要）:
--------------------
5. convert_to_hk2800.py - HK2800数据库转换
6. fix_hk2800_indicators.py - 修复技术指标

🎯 策略回测文件:
--------------
7. y_equals_x_strategy.py - Y=X策略扩展
8. simple_y_x_04_backtest.py - 简化策略回测
9. correct_10year_backtest.py - 10年回测验证

💰 收益计算文件:
--------------
10. calculate_30k_3year_returns.py - 3万港币3年收益
11. calculate_100k_5year.py - 10万港币5年收益
12. backtest_30k_5year.py - 3万港币5年回测

📈 分析优化文件:
--------------
13. strategy_optimization.py - 策略优化分析
14. kelly_optimized_backtest.py - 凯利优化回测
15. extended_strategy_backtest.py - 扩展策略回测

🔧 其他工具文件:
--------------
16. grid_kelly_strategy.py - 原版网格策略
17. realistic_3year_calculation.py - 现实收益计算

📋 使用优先级:
============
⭐⭐⭐⭐⭐ 必须转移 - 核心策略文件
⭐⭐⭐⭐ 重要 - 主要回测和分析文件
⭐⭐⭐ 一般 - 辅助分析文件
⭐⭐ 可选 - 实验性文件

🎯 推荐转移顺序:
==============
1. improved_grid_kelly.py (最佳策略)
2. check_database_status.py (数据检查)
3. optimized_y_x_04_backtest.py (简化策略)
4. test_hk2800_strategy.py (ETF策略)
5. convert_to_hk2800.py (数据转换)
6. 其他文件按需转移

💡 使用建议:
==========
- 新文件夹建议命名: "博弈论投资策略"
- 先转移前5个核心文件
- 确保数据库连接配置正确
- 运行check_database_status.py验证环境
