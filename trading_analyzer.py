"""
博弈论投资策略 - 数据分析和监控系统
实时分析、性能评估、风险监控
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
from trading_data_manager import TradingDataManager
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class TradingAnalyzer:
    def __init__(self, data_manager):
        """
        初始化交易分析器
        
        Args:
            data_manager: TradingDataManager实例
        """
        self.data_manager = data_manager
        
    def analyze_strategy_performance(self, days=30):
        """分析策略性能"""
        print("📊 分析策略性能...")
        
        # 获取交易数据
        query = """
        SELECT * FROM trades 
        WHERE action = 'CLOSE' 
        AND close_time >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
        ORDER BY close_time
        """
        trades_data = self.data_manager.execute_query(query, (days,), fetch=True)
        
        if not trades_data:
            print("❌ 没有找到交易数据")
            return None
        
        df = pd.DataFrame(trades_data)
        
        # 基本统计
        total_trades = len(df)
        winning_trades = len(df[df['profit'] > 0])
        losing_trades = len(df[df['profit'] < 0])
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        
        total_profit = df['profit'].sum()
        max_profit = df['profit'].max()
        max_loss = df['profit'].min()
        avg_profit = df[df['profit'] > 0]['profit'].mean() if winning_trades > 0 else 0
        avg_loss = df[df['profit'] < 0]['profit'].mean() if losing_trades > 0 else 0
        
        # 盈亏比
        profit_factor = abs(avg_profit / avg_loss) if avg_loss < 0 else 0
        
        # 最大回撤
        df['cumulative_profit'] = df['profit'].cumsum()
        df['running_max'] = df['cumulative_profit'].expanding().max()
        df['drawdown'] = df['cumulative_profit'] - df['running_max']
        max_drawdown = df['drawdown'].min()
        
        # 夏普比率 (简化版)
        returns = df['profit'] / 10000  # 假设基准资金10000
        sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
        
        performance = {
            'period_days': days,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': round(win_rate, 2),
            'total_profit': round(total_profit, 2),
            'max_profit': round(max_profit, 2),
            'max_loss': round(max_loss, 2),
            'avg_profit': round(avg_profit, 2),
            'avg_loss': round(avg_loss, 2),
            'profit_factor': round(profit_factor, 2),
            'max_drawdown': round(max_drawdown, 2),
            'sharpe_ratio': round(sharpe_ratio, 2)
        }
        
        return performance, df
    
    def analyze_game_theory_indicators(self, days=7):
        """分析博弈论指标"""
        print("🎯 分析博弈论指标...")
        
        query = """
        SELECT * FROM game_theory_indicators 
        WHERE datetime >= DATE_SUB(NOW(), INTERVAL %s DAY)
        ORDER BY datetime
        """
        indicators_data = self.data_manager.execute_query(query, (days,), fetch=True)
        
        if not indicators_data:
            print("❌ 没有找到指标数据")
            return None
        
        df = pd.DataFrame(indicators_data)
        df['datetime'] = pd.to_datetime(df['datetime'])
        
        # 统计分析
        analysis = {
            'y_value_stats': {
                'mean': df['y_probability'].mean(),
                'std': df['y_probability'].std(),
                'min': df['y_probability'].min(),
                'max': df['y_probability'].max(),
                'above_buy_threshold': len(df[df['y_probability'] > 0.55]) / len(df) * 100,
                'below_sell_threshold': len(df[df['y_probability'] < 0.45]) / len(df) * 100
            },
            'x_value_stats': {
                'mean': df['x_inflow_ratio'].mean(),
                'std': df['x_inflow_ratio'].std(),
                'min': df['x_inflow_ratio'].min(),
                'max': df['x_inflow_ratio'].max(),
                'above_buy_threshold': len(df[df['x_inflow_ratio'] > 0.5]) / len(df) * 100,
                'below_sell_threshold': len(df[df['x_inflow_ratio'] < 0.4]) / len(df) * 100
            },
            'signal_distribution': df['trade_signal'].value_counts().to_dict()
        }
        
        return analysis, df
    
    def create_performance_dashboard(self, days=30):
        """创建性能仪表板"""
        print("📈 创建性能仪表板...")
        
        # 获取数据
        performance, trades_df = self.analyze_strategy_performance(days)
        if performance is None:
            return None
        
        indicators_analysis, indicators_df = self.analyze_game_theory_indicators(days)
        
        # 创建子图
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=[
                '累积收益曲线', 'Y值和X值变化',
                '每日盈亏分布', '交易信号分布',
                '回撤曲线', '胜率统计'
            ],
            specs=[[{"secondary_y": False}, {"secondary_y": True}],
                   [{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # 1. 累积收益曲线
        fig.add_trace(
            go.Scatter(
                x=trades_df['close_time'],
                y=trades_df['cumulative_profit'],
                mode='lines',
                name='累积收益',
                line=dict(color='blue', width=2)
            ),
            row=1, col=1
        )
        
        # 2. Y值和X值变化
        if indicators_df is not None:
            fig.add_trace(
                go.Scatter(
                    x=indicators_df['datetime'],
                    y=indicators_df['y_probability'],
                    mode='lines',
                    name='Y值',
                    line=dict(color='red', width=1)
                ),
                row=1, col=2
            )
            
            fig.add_trace(
                go.Scatter(
                    x=indicators_df['datetime'],
                    y=indicators_df['x_inflow_ratio'],
                    mode='lines',
                    name='X值',
                    line=dict(color='green', width=1),
                    yaxis='y2'
                ),
                row=1, col=2
            )
        
        # 3. 每日盈亏分布
        daily_profit = trades_df.groupby(trades_df['close_time'].dt.date)['profit'].sum()
        fig.add_trace(
            go.Bar(
                x=daily_profit.index,
                y=daily_profit.values,
                name='每日盈亏',
                marker_color=['green' if x > 0 else 'red' for x in daily_profit.values]
            ),
            row=2, col=1
        )
        
        # 4. 交易信号分布
        if indicators_df is not None:
            signal_counts = indicators_df['trade_signal'].value_counts()
            fig.add_trace(
                go.Pie(
                    labels=signal_counts.index,
                    values=signal_counts.values,
                    name='信号分布'
                ),
                row=2, col=2
            )
        
        # 5. 回撤曲线
        fig.add_trace(
            go.Scatter(
                x=trades_df['close_time'],
                y=trades_df['drawdown'],
                mode='lines',
                name='回撤',
                line=dict(color='orange', width=2),
                fill='tonexty'
            ),
            row=3, col=1
        )
        
        # 6. 胜率统计
        win_loss_data = ['胜', '负']
        win_loss_counts = [performance['winning_trades'], performance['losing_trades']]
        fig.add_trace(
            go.Bar(
                x=win_loss_data,
                y=win_loss_counts,
                name='胜负统计',
                marker_color=['green', 'red']
            ),
            row=3, col=2
        )
        
        # 更新布局
        fig.update_layout(
            title=f'博弈论策略性能仪表板 - 最近{days}天',
            height=1200,
            showlegend=True
        )
        
        # 保存图表
        fig.write_html('performance_dashboard.html')
        print("✅ 性能仪表板已保存: performance_dashboard.html")
        
        return fig
    
    def generate_risk_report(self):
        """生成风险报告"""
        print("⚠️ 生成风险报告...")
        
        # 获取风险监控数据
        query = """
        SELECT * FROM risk_monitoring 
        WHERE datetime >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY datetime DESC
        """
        risk_data = self.data_manager.execute_query(query, fetch=True)
        
        # 获取当前账户状态
        account_query = """
        SELECT * FROM account_status 
        ORDER BY datetime DESC LIMIT 1
        """
        account_data = self.data_manager.execute_query(account_query, fetch=True)
        
        # 获取当前持仓
        positions = self.data_manager.get_current_positions()
        
        risk_report = {
            'report_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'account_status': account_data[0] if account_data else {},
            'current_positions': positions.to_dict('records') if not positions.empty else [],
            'risk_alerts': risk_data if risk_data else [],
            'risk_summary': {
                'total_alerts': len(risk_data) if risk_data else 0,
                'critical_alerts': len([r for r in risk_data if r['risk_level'] == 'CRITICAL']) if risk_data else 0,
                'high_alerts': len([r for r in risk_data if r['risk_level'] == 'HIGH']) if risk_data else 0,
                'unresolved_alerts': len([r for r in risk_data if not r['resolved']]) if risk_data else 0
            }
        }
        
        return risk_report
    
    def print_performance_summary(self, days=30):
        """打印性能摘要"""
        performance, _ = self.analyze_strategy_performance(days)
        if performance is None:
            return
        
        print("\n" + "="*60)
        print(f"📊 博弈论策略性能报告 - 最近{days}天")
        print("="*60)
        print(f"📈 交易统计:")
        print(f"   总交易次数: {performance['total_trades']}")
        print(f"   盈利交易: {performance['winning_trades']}")
        print(f"   亏损交易: {performance['losing_trades']}")
        print(f"   胜率: {performance['win_rate']:.2f}%")
        print()
        print(f"💰 盈亏分析:")
        print(f"   总盈亏: {performance['total_profit']:+.2f}港元")
        print(f"   最大盈利: {performance['max_profit']:+.2f}港元")
        print(f"   最大亏损: {performance['max_loss']:+.2f}港元")
        print(f"   平均盈利: {performance['avg_profit']:+.2f}港元")
        print(f"   平均亏损: {performance['avg_loss']:+.2f}港元")
        print()
        print(f"📊 风险指标:")
        print(f"   盈亏比: {performance['profit_factor']:.2f}")
        print(f"   最大回撤: {performance['max_drawdown']:+.2f}港元")
        print(f"   夏普比率: {performance['sharpe_ratio']:.2f}")
        print("="*60)
    
    def export_analysis_to_excel(self, filename=None):
        """导出分析结果到Excel"""
        if filename is None:
            filename = f"trading_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        print(f"📄 导出分析结果到: {filename}")
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 性能分析
            performance, trades_df = self.analyze_strategy_performance(30)
            if performance:
                performance_df = pd.DataFrame([performance])
                performance_df.to_excel(writer, sheet_name='策略性能', index=False)
                
                if not trades_df.empty:
                    trades_df.to_excel(writer, sheet_name='交易记录', index=False)
            
            # 博弈论指标分析
            indicators_analysis, indicators_df = self.analyze_game_theory_indicators(7)
            if indicators_analysis and not indicators_df.empty:
                indicators_df.to_excel(writer, sheet_name='博弈论指标', index=False)
            
            # 风险报告
            risk_report = self.generate_risk_report()
            if risk_report['risk_alerts']:
                risk_df = pd.DataFrame(risk_report['risk_alerts'])
                risk_df.to_excel(writer, sheet_name='风险监控', index=False)
            
            # 交易汇总
            summary_df = self.data_manager.get_trading_summary(30)
            if not summary_df.empty:
                summary_df.to_excel(writer, sheet_name='交易汇总', index=False)
        
        print(f"✅ 分析结果已导出: {filename}")
        return filename

def main():
    """主函数 - 演示分析功能"""
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'database': 'game_theory_trading',
        'user': 'your_username',
        'password': 'your_password',
        'charset': 'utf8mb4'
    }
    
    # 创建数据管理器和分析器
    data_manager = TradingDataManager(db_config)
    analyzer = TradingAnalyzer(data_manager)
    
    try:
        # 打印性能摘要
        analyzer.print_performance_summary(30)
        
        # 创建性能仪表板
        analyzer.create_performance_dashboard(30)
        
        # 导出分析结果
        analyzer.export_analysis_to_excel()
        
        # 生成风险报告
        risk_report = analyzer.generate_risk_report()
        print(f"\n⚠️ 风险警报: {risk_report['risk_summary']['total_alerts']}个")
        
    finally:
        data_manager.close()

if __name__ == "__main__":
    main()
