#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HK00023四象限策略回测
====================

全新四象限策略：
- Y>0.5且X>0.5: 买升 (做多)
- Y<0.5且X>0.5: 买跌 (做空)  
- Y>0.5且X<0.5: 买跌 (做空)
- Y<0.4且X<0.4: 买跌 (做空)
- 其他区域: 观望

赔率设置: 1:2 (止损1%，止盈2%)
初始资金: 30,000港币

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class HK00023QuadrantStrategy:
    def __init__(self):
        """初始化HK00023四象限策略"""
        self.symbol = "0023.HK"
        self.initial_capital = 30000
        self.data = None
        
        # 四象限策略参数
        self.strategy_params = {
            # 四象限阈值
            'high_threshold': 0.5,
            'low_threshold': 0.4,
            
            # 赔率1:2设置
            'take_profit': 0.02,   # 2%止盈
            'stop_loss': 0.01,     # 1%止损
            
            'transaction_cost': 0.0025,
            'position_ratio': 0.08,
        }
        
    def fetch_hk00023_data(self):
        """获取HK00023数据"""
        print("🏦 获取HK00023东亚银行20年历史数据...")
        
        try:
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(period="20y", interval="1d")
            
            if self.data.empty:
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取HK00023数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data)} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算Y、X、E值和均值回归指标"""
        print("🎯 计算四象限策略的Y、X、E值和均值回归指标...")
        
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        self.data['ma_120'] = self.data['close'].rolling(window=120).mean()
        
        # 均值回归中值计算
        self.data['mean_reversion_center'] = (self.data['ma_20'] + self.data['ma_60'] + self.data['ma_120']) / 3
        self.data['price_vs_center'] = self.data['close'] / self.data['mean_reversion_center']
        self.data['deviation_from_center'] = (self.data['close'] - self.data['mean_reversion_center']) / self.data['mean_reversion_center'] * 100
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # Y值计算
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
        
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        # X值计算
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        rsi_adjustment = 0.3 * (self.data['rsi'] / 100 - 0.5)
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        # E值计算
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        # 四象限策略分类
        y_val = self.data['y_probability']
        x_val = self.data['inflow_ratio']
        
        conditions = [
            (y_val > self.strategy_params['high_threshold']) & 
            (x_val > self.strategy_params['high_threshold']),  # 第一象限：Y>0.5且X>0.5
            
            (y_val < self.strategy_params['high_threshold']) & 
            (x_val > self.strategy_params['high_threshold']),  # 第二象限：Y<0.5且X>0.5
            
            (y_val > self.strategy_params['high_threshold']) & 
            (x_val < self.strategy_params['high_threshold']),  # 第三象限：Y>0.5且X<0.5
            
            (y_val < self.strategy_params['low_threshold']) & 
            (x_val < self.strategy_params['low_threshold']),   # 第四象限：Y<0.4且X<0.4
        ]
        
        choices = ['第一象限(Y>0.5,X>0.5)', '第二象限(Y<0.5,X>0.5)', 
                  '第三象限(Y>0.5,X<0.5)', '第四象限(Y<0.4,X<0.4)']
        self.data['quadrant'] = np.select(conditions, choices, default='观望区域')
        
        # 确定交易方向
        direction_conditions = [
            (y_val > self.strategy_params['high_threshold']) & 
            (x_val > self.strategy_params['high_threshold']),  # 买升
            
            (y_val < self.strategy_params['high_threshold']) & 
            (x_val > self.strategy_params['high_threshold']),  # 买跌
            
            (y_val > self.strategy_params['high_threshold']) & 
            (x_val < self.strategy_params['high_threshold']),  # 买跌
            
            (y_val < self.strategy_params['low_threshold']) & 
            (x_val < self.strategy_params['low_threshold']),   # 买跌
        ]
        
        direction_choices = ['做多', '做空', '做空', '做空']
        self.data['trade_direction'] = np.select(direction_conditions, direction_choices, default='观望')
        
        # 统计四象限分布
        quadrant_counts = self.data['quadrant'].value_counts()
        total = len(self.data)
        print(f"📊 四象限策略分布:")
        for quadrant, count in quadrant_counts.items():
            direction = self.data[self.data['quadrant'] == quadrant]['trade_direction'].iloc[0] if count > 0 else '观望'
            print(f"   • {quadrant}: {count} 天 ({count/total*100:.1f}%) - {direction}")
    
    def run_quadrant_backtest(self):
        """运行四象限策略回测"""
        print("🎯 开始四象限策略回测...")
        print("📋 策略: Y>0.5且X>0.5买升，其他组合买跌，赔率1:2")
        
        trades = []
        current_cash = self.initial_capital
        
        # 从第120天开始
        start_index = 120
        trading_data = self.data[start_index:].copy().reset_index(drop=True)
        
        trade_count = 0
        i = 0
        
        while i < len(trading_data) - 10:
            row = trading_data.iloc[i]
            
            date = row['date']
            price = row['close']
            y_val = row['y_probability']
            x_val = row['inflow_ratio']
            e_val = row['e_value']
            quadrant = row['quadrant']
            direction = row['trade_direction']
            
            # 跳过观望区域
            if direction == '观望':
                i += 1
                continue
            
            # 确定交易理由
            if quadrant == '第一象限(Y>0.5,X>0.5)':
                reason = f'第一象限：Y={y_val:.3f}>0.5且X={x_val:.3f}>0.5，控股商托价且资金流入，买升'
            elif quadrant == '第二象限(Y<0.5,X>0.5)':
                reason = f'第二象限：Y={y_val:.3f}<0.5且X={x_val:.3f}>0.5，控股商不托价但资金流入，买跌'
            elif quadrant == '第三象限(Y>0.5,X<0.5)':
                reason = f'第三象限：Y={y_val:.3f}>0.5且X={x_val:.3f}<0.5，控股商托价但资金流出，买跌'
            elif quadrant == '第四象限(Y<0.4,X<0.4)':
                reason = f'第四象限：Y={y_val:.3f}<0.4且X={x_val:.3f}<0.4，控股商不托价且资金流出，买跌'
            
            if current_cash > 2000:
                # 计算仓位
                position_value = current_cash * self.strategy_params['position_ratio']
                shares = int(position_value / price / 100) * 100
                actual_value = shares * price
                
                if shares >= 100:
                    # 模拟持仓期
                    holding_days = np.random.randint(3, 10)
                    exit_index = min(i + holding_days, len(trading_data) - 1)
                    exit_row = trading_data.iloc[exit_index]
                    exit_price = exit_row['close']
                    exit_date = exit_row['date']
                    
                    # 计算盈亏 - 统一使用1:2赔率
                    if direction == '做多':
                        profit_pct = (exit_price - price) / price
                        if profit_pct >= self.strategy_params['take_profit']:  # 止盈2%
                            exit_reason = '止盈'
                            profit_pct = self.strategy_params['take_profit']
                            exit_price = price * (1 + self.strategy_params['take_profit'])
                        elif profit_pct <= -self.strategy_params['stop_loss']:  # 止损1%
                            exit_reason = '止损'
                            profit_pct = -self.strategy_params['stop_loss']
                            exit_price = price * (1 - self.strategy_params['stop_loss'])
                        else:
                            exit_reason = '到期平仓'
                        
                        profit = profit_pct * actual_value
                    else:  # 做空
                        profit_pct = (price - exit_price) / price
                        if profit_pct >= self.strategy_params['take_profit']:  # 止盈2%
                            exit_reason = '止盈'
                            profit_pct = self.strategy_params['take_profit']
                            exit_price = price * (1 - self.strategy_params['take_profit'])
                        elif profit_pct <= -self.strategy_params['stop_loss']:  # 止损1%
                            exit_reason = '止损'
                            profit_pct = -self.strategy_params['stop_loss']
                            exit_price = price * (1 + self.strategy_params['stop_loss'])
                        else:
                            exit_reason = '到期平仓'
                        
                        profit = profit_pct * actual_value
                    
                    # 交易成本
                    transaction_cost = actual_value * self.strategy_params['transaction_cost'] * 2
                    net_profit = profit - transaction_cost
                    
                    # 更新现金
                    current_cash += net_profit
                    
                    # 计算均值回归相关数据
                    mean_center = row['mean_reversion_center']
                    price_vs_center = row['price_vs_center']
                    deviation = row['deviation_from_center']
                    
                    # 记录交易
                    trade_record = {
                        '交易序号': trade_count + 1,
                        '股票代码': 'HK00023',
                        '股票名称': '东亚银行',
                        '开仓日期': date.strftime('%Y-%m-%d'),
                        '平仓日期': exit_date.strftime('%Y-%m-%d'),
                        '持仓天数': holding_days,
                        '交易方向': direction,
                        '开仓价格': round(price, 2),
                        '平仓价格': round(exit_price, 2),
                        '均值回归中值': round(mean_center, 2),
                        '价格偏离度%': round(deviation, 2),
                        '价格中值比': round(price_vs_center, 3),
                        '交易股数': shares,
                        '交易金额': round(actual_value, 0),
                        '毛利润': round(profit, 0),
                        '交易成本': round(transaction_cost, 0),
                        '净利润': round(net_profit, 0),
                        '收益率%': round(profit_pct * 100, 2),
                        'Y值': round(y_val, 3),
                        'X值': round(x_val, 3),
                        'E值': round(e_val, 3),
                        '四象限': quadrant,
                        '交易理由': reason,
                        '平仓原因': exit_reason,
                        '账户余额': round(current_cash, 0),
                        'RSI': round(row['rsi'], 1)
                    }
                    
                    trades.append(trade_record)
                    trade_count += 1
                    
                    if trade_count % 50 == 0:
                        print(f"已完成 {trade_count} 笔交易，当前资金: {current_cash:,.0f}港币")
            
            # 随机间隔
            i += np.random.randint(2, 8)
        
        print(f"\n✅ 四象限策略回测完成!")
        print(f"📊 总交易次数: {trade_count}")
        print(f"💰 最终资金: {current_cash:,.0f}港币")
        print(f"📈 总收益: {current_cash - self.initial_capital:+,.0f}港币")
        
        return trades

    def create_quadrant_excel(self, trades):
        """创建四象限策略Excel文件"""
        print("📄 创建四象限策略Excel文件...")

        if not trades:
            return None

        # 创建交易记录DataFrame
        df_trades = pd.DataFrame(trades)

        # 计算汇总统计
        total_trades = len(df_trades)
        winning_trades = len(df_trades[df_trades['净利润'] > 0])
        losing_trades = len(df_trades[df_trades['净利润'] < 0])
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        total_profit = df_trades['净利润'].sum()
        final_capital = df_trades['账户余额'].iloc[-1] if len(df_trades) > 0 else self.initial_capital
        total_return = (final_capital / self.initial_capital - 1) * 100

        # 按四象限分析
        quadrant_analysis = df_trades.groupby('四象限').agg({
            '净利润': ['count', 'sum', 'mean'],
            '收益率%': 'mean',
            '交易方向': lambda x: x.iloc[0]
        }).round(2)
        quadrant_analysis.columns = ['交易次数', '总盈亏', '平均盈亏', '平均收益率%', '交易方向']
        quadrant_analysis = quadrant_analysis.reset_index()

        # 创建汇总统计
        summary_data = {
            '项目': [
                '股票代码', '股票名称', '策略版本', '初始资金(港币)', '最终资金(港币)', '总盈亏(港币)',
                '总收益率(%)', '总交易次数', '盈利次数', '亏损次数', '胜率(%)',
                '平均每笔盈亏(港币)', '最大单笔盈利(港币)', '最大单笔亏损(港币)',
                '做多次数', '做空次数', '赔率设置', '止盈次数', '止损次数', '到期平仓次数'
            ],
            '数值': [
                'HK00023', '东亚银行', '四象限策略(赔率1:2)', self.initial_capital, final_capital, total_profit,
                round(total_return, 2), total_trades, winning_trades, losing_trades, round(win_rate, 1),
                round(total_profit/total_trades, 0) if total_trades > 0 else 0,
                df_trades['净利润'].max(), df_trades['净利润'].min(),
                len(df_trades[df_trades['交易方向'] == '做多']),
                len(df_trades[df_trades['交易方向'] == '做空']),
                '止损1%:止盈2%',
                len(df_trades[df_trades['平仓原因'] == '止盈']),
                len(df_trades[df_trades['平仓原因'] == '止损']),
                len(df_trades[df_trades['平仓原因'] == '到期平仓'])
            ]
        }
        summary_df = pd.DataFrame(summary_data)

        # 创建四象限策略说明
        strategy_explanation = {
            '四象限': [
                '第一象限(Y>0.5,X>0.5)',
                '第二象限(Y<0.5,X>0.5)',
                '第三象限(Y>0.5,X<0.5)',
                '第四象限(Y<0.4,X<0.4)',
                '观望区域'
            ],
            '条件': [
                'Y>0.5 且 X>0.5',
                'Y<0.5 且 X>0.5',
                'Y>0.5 且 X<0.5',
                'Y<0.4 且 X<0.4',
                '其他情况'
            ],
            '操作': ['买升(做多)', '买跌(做空)', '买跌(做空)', '买跌(做空)', '观望'],
            '止盈': ['2%', '2%', '2%', '2%', '-'],
            '止损': ['1%', '1%', '1%', '1%', '-'],
            '理论依据': [
                '控股商托价且资金流入，双重利好，价格上涨概率高',
                '控股商不托价但资金流入，资金推动有限，可能回调',
                '控股商托价但资金流出，托价效果有限，可能下跌',
                '控股商不托价且资金流出，双重利空，价格下跌概率高',
                '信号不明确，避免交易'
            ]
        }
        strategy_df = pd.DataFrame(strategy_explanation)

        # 创建均值回归分析说明
        mean_reversion_explanation = {
            '指标名称': ['均值回归中值', '价格偏离度%', '价格中值比', 'Y值', 'X值', 'E值'],
            '计算方法': [
                '(MA20 + MA60 + MA120) / 3',
                '(当前价格 - 均值回归中值) / 均值回归中值 × 100',
                '当前价格 / 均值回归中值',
                '控股商托价概率 (基于价格vs均线、趋势、成交量)',
                '资金流入比例 (基于资金流向、RSI调整)',
                '博弈论期望值 = 8XY - 3X - 3Y + 1'
            ],
            '意义': [
                '多周期均线的综合中值，代表长期均衡价格',
                '当前价格相对于均值的偏离程度，正值表示高估，负值表示低估',
                '价格相对于中值的倍数关系，>1表示高于均值，<1表示低于均值',
                'Y>0.5表示控股商托价概率高，Y<0.5表示控股商不托价',
                'X>0.5表示资金净流入，X<0.5表示资金净流出',
                'E>0表示博弈论期望为正，E<0表示期望为负'
            ],
            '四象限应用': [
                '结合四象限分析，判断价格是否偏离合理区间',
                '偏离度配合四象限，提高交易精确度',
                '价格中值比验证四象限策略的有效性',
                'Y值是四象限分类的核心指标之一',
                'X值是四象限分类的核心指标之一',
                'E值验证四象限策略的理论基础'
            ]
        }
        mean_reversion_df = pd.DataFrame(mean_reversion_explanation)

        # 创建Excel文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"HK00023四象限策略回测_{timestamp}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 写入各个工作表
            df_trades.to_excel(writer, sheet_name='四象限交易记录', index=False)
            summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
            quadrant_analysis.to_excel(writer, sheet_name='四象限分析', index=False)
            strategy_df.to_excel(writer, sheet_name='四象限策略说明', index=False)
            mean_reversion_df.to_excel(writer, sheet_name='指标说明', index=False)

            # 设置格式
            workbook = writer.book

            # 交易记录表格式
            worksheet1 = writer.sheets['四象限交易记录']
            column_widths = {
                'A': 8, 'B': 10, 'C': 10, 'D': 12, 'E': 12, 'F': 8, 'G': 8, 'H': 10, 'I': 10, 'J': 12, 'K': 12, 'L': 12,
                'M': 10, 'N': 12, 'O': 10, 'P': 10, 'Q': 10, 'R': 10, 'S': 8, 'T': 8, 'U': 8, 'V': 20, 'W': 80, 'X': 12, 'Y': 12, 'Z': 8
            }
            for col, width in column_widths.items():
                worksheet1.column_dimensions[col].width = width

            # 汇总统计表格式
            worksheet2 = writer.sheets['汇总统计']
            worksheet2.column_dimensions['A'].width = 25
            worksheet2.column_dimensions['B'].width = 20

            # 四象限分析表格式
            worksheet3 = writer.sheets['四象限分析']
            worksheet3.column_dimensions['A'].width = 25
            worksheet3.column_dimensions['B'].width = 12
            worksheet3.column_dimensions['C'].width = 15
            worksheet3.column_dimensions['D'].width = 15
            worksheet3.column_dimensions['E'].width = 15
            worksheet3.column_dimensions['F'].width = 12

            # 策略说明表格式
            worksheet4 = writer.sheets['四象限策略说明']
            worksheet4.column_dimensions['A'].width = 25
            worksheet4.column_dimensions['B'].width = 20
            worksheet4.column_dimensions['C'].width = 15
            worksheet4.column_dimensions['D'].width = 8
            worksheet4.column_dimensions['E'].width = 8
            worksheet4.column_dimensions['F'].width = 60

            # 指标说明表格式
            worksheet5 = writer.sheets['指标说明']
            worksheet5.column_dimensions['A'].width = 15
            worksheet5.column_dimensions['B'].width = 40
            worksheet5.column_dimensions['C'].width = 60
            worksheet5.column_dimensions['D'].width = 60

        print(f"✅ 四象限策略Excel文件已创建: {filename}")
        return filename

def main():
    """主函数"""
    print("🏦 HK00023东亚银行四象限策略回测")
    print("="*70)
    print("💰 初始资金: 30,000港币")
    print("🎯 四象限策略:")
    print("   • Y>0.5且X>0.5: 买升 (做多)")
    print("   • Y<0.5且X>0.5: 买跌 (做空)")
    print("   • Y>0.5且X<0.5: 买跌 (做空)")
    print("   • Y<0.4且X<0.4: 买跌 (做空)")
    print("   • 其他区域: 观望")
    print("📊 赔率设置: 1:2 (止损1%，止盈2%)")

    # 创建四象限策略
    strategy = HK00023QuadrantStrategy()

    # 获取数据
    if not strategy.fetch_hk00023_data():
        return

    # 计算指标
    strategy.calculate_indicators()

    # 运行四象限回测
    trades = strategy.run_quadrant_backtest()

    if not trades:
        print("❌ 未能生成交易记录")
        return

    # 创建Excel文件
    filename = strategy.create_quadrant_excel(trades)

    if filename:
        print(f"\n🎉 HK00023四象限策略回测完成!")
        print(f"📄 文件名: {filename}")
        print(f"📊 包含 {len(trades)} 条四象限交易记录")
        print(f"💡 请打开Excel文件查看四象限策略结果")

if __name__ == "__main__":
    main()
