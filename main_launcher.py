#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
博弈论投资策略系统 - 主启动器
============================

这是一个完整的系统启动器，整合了：
1. MT5交易监控
2. 博弈论策略分析
3. 数据库管理
4. 系统控制

作者: 博弈论投资策略团队
日期: 2025年7月
"""

import os
import sys
import subprocess
import json
from datetime import datetime
import MetaTrader5 as mt5
import pymysql

def print_header():
    """打印系统标题"""
    print("=" * 80)
    print("🎯 博弈论投资策略系统 - 主控制台")
    print("=" * 80)
    print("📅 当前时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("📂 工作目录:", os.getcwd())
    print("=" * 80)

def print_main_menu():
    """打印主菜单"""
    print("\n🚀 主功能菜单:")
    print("-" * 60)
    
    print("\n📊 MT5交易监控:")
    print("1. 🔍 启动MT5交易监控系统")
    print("2. 📈 启动完整系统控制器")
    print("3. 🎯 快速MT5连接测试")
    
    print("\n💰 博弈论策略分析:")
    print("4. 🏆 Cosmoon凯利公式策略 (最新完整版)")
    print("5. 🎯 Cosmoon博弈论策略回测")
    print("6. 🏆 改进版网格+凯利策略")
    print("7. 📊 优化版Y>0.4且X>0.4策略")
    print("8. 🎯 HK2800 ETF策略测试")
    print("9. 📈 恒生指数25年历史回测")
    print("10. 💰 8000港币20年复利回测")
    print("11. ⚡ 快速回测 (含模拟数据)")
    
    print("\n🔧 系统管理:")
    print("12. 🔢 重新计算Y和X值")
    print("13. 🗄️ 数据库状态检查")
    print("14. ⚙️ 系统配置管理")
    print("15. 📋 查看重要文件清单")
    print("16. 🔄 数据转换工具")
    
    print("\n📖 帮助信息:")
    print("17. 📄 查看系统说明")
    print("18. 🛠️ 安装依赖包")
    print("19. 🚪 退出系统")
    
    print("-" * 60)

def check_mt5_connection():
    """检查MT5连接"""
    print("\n🔍 检查MT5连接状态...")
    print("-" * 40)
    
    try:
        if not mt5.initialize():
            print("❌ MT5初始化失败")
            print("💡 请确保MT5已安装并正在运行")
            return False
        
        # 获取账户信息
        account_info = mt5.account_info()
        if account_info is None:
            print("❌ 无法获取账户信息")
            print("💡 请确保MT5已登录账户")
            mt5.shutdown()
            return False
        
        print("✅ MT5连接成功!")
        print(f"📊 账户: {account_info.login}")
        print(f"💰 余额: {account_info.balance:.2f}")
        print(f"💎 净值: {account_info.equity:.2f}")
        print(f"📈 保证金: {account_info.margin:.2f}")
        print(f"🆓 可用保证金: {account_info.margin_free:.2f}")
        
        # 检查品种
        symbol = "HSI50"
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info:
            print(f"✅ 交易品种 {symbol} 可用")
            print(f"📊 当前价格: {symbol_info.bid:.2f}")
        else:
            print(f"⚠️ 交易品种 {symbol} 不可用")
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ MT5连接检查失败: {e}")
        return False

def check_database_connection():
    """检查数据库连接"""
    print("\n🗄️ 检查数据库连接状态...")
    print("-" * 40)
    
    try:
        # 加载配置
        config_file = 'system_config.json'
        if not os.path.exists(config_file):
            print("❌ 配置文件不存在")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        db_config = config.get('database', {})
        
        # 连接数据库
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("✅ 数据库连接成功!")
        print(f"📊 主机: {db_config['host']}")
        print(f"🗄️ 数据库: {db_config['database']}")
        
        # 检查重要表
        tables_to_check = ['trades', 'account_status', 'market_data', 'hk2800']
        
        for table in tables_to_check:
            cursor.execute(f"SHOW TABLES LIKE '{table}'")
            if cursor.fetchone():
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ 表 {table}: {count} 条记录")
            else:
                print(f"⚠️ 表 {table}: 不存在")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def run_script(script_name, description):
    """运行指定的Python脚本"""
    print(f"\n🚀 正在启动: {description}")
    print(f"📄 执行文件: {script_name}")
    print("-" * 50)
    
    if not os.path.exists(script_name):
        print(f"❌ 错误: 文件 {script_name} 不存在!")
        return False
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=False, 
                              text=True)
        
        if result.returncode == 0:
            print(f"\n✅ {description} 执行完成!")
        else:
            print(f"\n❌ {description} 执行出错!")
            
    except Exception as e:
        print(f"❌ 执行错误: {str(e)}")
        return False
    
    return True

def show_system_info():
    """显示系统信息"""
    print("\n📖 博弈论投资策略系统说明:")
    print("-" * 50)
    
    print("🎯 系统功能:")
    print("   • MT5交易实时监控")
    print("   • 博弈论策略自动分析")
    print("   • 数据库交易记录管理")
    print("   • 风险监控和报警")
    
    print("\n🏆 核心策略:")
    print("   • 买入条件: X>0.4 且 Y>0.4")
    print("   • 卖出条件: X<0.4")
    print("   • X: 资金流入比例")
    print("   • Y: 博弈论概率")
    
    print("\n📊 最佳策略推荐:")
    print("   1. 改进版网格+凯利策略 (年化30.60%)")
    print("   2. HK2800 ETF策略 (年化46.67%)")
    print("   3. 优化版Y>X>0.4策略 (年化9.71%)")
    
    print("\n🔧 系统要求:")
    print("   • MT5终端已安装并运行")
    print("   • MySQL数据库已配置")
    print("   • Python 3.7+ 环境")
    print("   • 必要的Python包已安装")
    
    print("\n⚠️ 重要提醒:")
    print("   • 投资有风险，入市需谨慎")
    print("   • 建议先小资金测试")
    print("   • 严格执行买卖条件")
    print("   • 定期检查系统状态")

def show_file_status():
    """显示文件状态"""
    print("\n📋 重要文件状态:")
    print("-" * 50)
    
    important_files = [
        ("cosmoon_strategy_backtest.py", "🎯 Cosmoon博弈论策略回测 (核心)"),
        ("cosmoon_e_heatmap.png", "📊 E值热力图"),
        ("MY_STRATEGY_MEMO.md", "📋 Cosmoon策略备忘录"),
        ("mt5_trading_launcher.py", "🔍 MT5交易监控启动器"),
        ("system_controller.py", "📈 完整系统控制器"),
        ("improved_grid_kelly.py", "🏆 改进版网格+凯利策略"),
        ("optimized_y_x_04_backtest.py", "📊 优化版Y>0.4且X>0.4策略"),
        ("test_hk2800_strategy.py", "🎯 HK2800 ETF策略"),
        ("hsi_25year_backtest.py", "📈 恒生指数25年历史回测"),
        ("hkd8000_20year_backtest.py", "💰 8000港币20年复利回测"),
        ("quick_hsi_backtest.py", "⚡ 快速回测 (含模拟数据)"),
        ("check_database_status.py", "🗄️ 数据库状态检查"),
        ("system_config.json", "⚙️ 系统配置文件"),
    ]
    
    for filename, description in important_files:
        status = "✅" if os.path.exists(filename) else "❌"
        print(f"{status} {description}")
        print(f"   📄 {filename}")

def main():
    """主函数"""
    while True:
        print_header()
        print_main_menu()
        
        try:
            choice = input("\n请选择功能 (1-19): ").strip()
            
            if choice == "1":
                run_script("mt5_trading_launcher.py", "MT5交易监控系统")

            elif choice == "2":
                run_script("system_controller.py", "完整系统控制器")

            elif choice == "3":
                check_mt5_connection()

            elif choice == "4":
                run_script("cosmoon_kelly_strategy.py", "Cosmoon凯利公式策略")

            elif choice == "5":
                run_script("cosmoon_strategy_backtest.py", "Cosmoon博弈论策略回测")

            elif choice == "6":
                run_script("improved_grid_kelly.py", "改进版网格+凯利策略")

            elif choice == "7":
                run_script("optimized_y_x_04_backtest.py", "优化版Y>0.4且X>0.4策略")

            elif choice == "8":
                run_script("test_hk2800_strategy.py", "HK2800 ETF策略测试")

            elif choice == "9":
                run_script("hsi_25year_backtest.py", "恒生指数25年历史回测")

            elif choice == "10":
                run_script("hkd8000_20year_backtest.py", "8000港币20年复利回测")

            elif choice == "11":
                run_script("quick_hsi_backtest.py", "快速回测 (含模拟数据)")

            elif choice == "12":
                run_script("recalculate_y_x_values.py", "重新计算Y和X值")

            elif choice == "13":
                if os.path.exists("check_database_status.py"):
                    run_script("check_database_status.py", "数据库状态检查")
                else:
                    check_database_connection()

            elif choice == "14":
                print("\n⚙️ 系统配置:")
                if os.path.exists("system_config.json"):
                    with open("system_config.json", 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        print(json.dumps(config, indent=2, ensure_ascii=False))
                else:
                    print("❌ 配置文件不存在")

            elif choice == "15":
                show_file_status()

            elif choice == "16":
                run_script("convert_to_hk2800.py", "数据转换工具")

            elif choice == "17":
                show_system_info()

            elif choice == "18":
                run_script("install_dependencies.py", "安装依赖包")

            elif choice == "19":
                print("\n👋 感谢使用博弈论投资策略系统!")
                print("💡 投资有风险，入市需谨慎!")
                break
                
            else:
                print("\n❌ 无效选择，请输入1-19之间的数字")

            if choice not in ["19"]:
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"\n❌ 系统错误: {str(e)}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
