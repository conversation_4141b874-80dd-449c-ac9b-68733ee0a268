#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3万港币投资3年回报计算
基于Y>0.4且X>0.4策略
"""

import numpy as np

def calculate_30k_3year_returns():
    """计算3万港币3年投资回报"""
    
    print("💰 3万港币投资3年回报计算")
    print("="*60)
    print("📊 策略: Y>0.4 且 X>0.4")
    print("💰 初始资金: 30,000 港币")
    print("📅 投资期限: 3年")
    
    # 基于10年回测验证的策略参数
    strategy_params = {
        'initial_capital': 30000,
        'annual_return': 0.0971,      # 9.71%年化收益
        'win_rate': 0.517,            # 51.7%胜率
        'avg_return_per_trade': 0.0173,  # 单次平均收益1.73%
        'trades_per_year': 24,        # 年均24次交易
        'position_size': 0.25,        # 25%仓位
        'holding_days': 120,          # 120天持有期
        'years': 3
    }
    
    print(f"\n📊 策略参数 (基于10年回测验证):")
    print("="*50)
    print(f"   年化收益率: {strategy_params['annual_return']*100:.2f}%")
    print(f"   胜率: {strategy_params['win_rate']*100:.1f}%")
    print(f"   年均交易: {strategy_params['trades_per_year']}次")
    print(f"   推荐仓位: {strategy_params['position_size']*100:.0f}%")
    print(f"   持有期: {strategy_params['holding_days']}天")
    
    # 1. 复利计算方法
    def compound_growth_calculation():
        """复利增长计算"""
        
        annual_rate = strategy_params['annual_return']
        years = strategy_params['years']
        initial = strategy_params['initial_capital']
        
        final_amount = initial * ((1 + annual_rate) ** years)
        total_profit = final_amount - initial
        
        return {
            'method': '复利增长',
            'final_amount': final_amount,
            'total_profit': total_profit,
            'total_return_rate': total_profit / initial
        }
    
    # 2. 交易模拟方法
    def trading_simulation():
        """交易模拟计算"""
        
        np.random.seed(42)  # 确保结果可重复
        
        # 模拟参数
        total_trades = strategy_params['trades_per_year'] * strategy_params['years']
        win_rate = strategy_params['win_rate']
        avg_return = strategy_params['avg_return_per_trade']
        position_size = strategy_params['position_size']
        
        # 蒙特卡洛模拟
        num_simulations = 1000
        final_amounts = []
        
        for sim in range(num_simulations):
            capital = strategy_params['initial_capital']
            
            for trade in range(total_trades):
                # 计算投资金额
                investment = capital * position_size
                
                # 随机生成交易结果
                if np.random.random() < win_rate:
                    # 盈利交易 - 使用正态分布模拟收益
                    trade_return = max(np.random.normal(avg_return, avg_return*0.5), -0.1)
                else:
                    # 亏损交易 - 使用负收益
                    trade_return = min(np.random.normal(-avg_return*0.6, avg_return*0.3), 0.05)
                
                # 计算盈亏
                pnl = investment * trade_return
                capital += pnl
                
                # 防止资金为负
                capital = max(capital, 0)
            
            final_amounts.append(capital)
        
        return {
            'method': '交易模拟',
            'mean_final': np.mean(final_amounts),
            'median_final': np.median(final_amounts),
            'percentile_5': np.percentile(final_amounts, 5),
            'percentile_25': np.percentile(final_amounts, 25),
            'percentile_75': np.percentile(final_amounts, 75),
            'percentile_95': np.percentile(final_amounts, 95),
            'std_final': np.std(final_amounts),
            'success_rate': sum(1 for x in final_amounts if x > strategy_params['initial_capital']) / len(final_amounts)
        }
    
    # 3. 执行计算
    print(f"\n💰 方法一：复利增长计算")
    print("="*40)
    
    compound_result = compound_growth_calculation()
    
    print(f"   计算方法: {compound_result['method']}")
    print(f"   最终金额: {compound_result['final_amount']:,.0f} 港币")
    print(f"   总收益: {compound_result['total_profit']:+,.0f} 港币")
    print(f"   收益率: {compound_result['total_return_rate']*100:+.2f}%")
    
    print(f"\n💰 方法二：蒙特卡洛交易模拟")
    print("="*40)
    
    simulation_result = trading_simulation()
    
    print(f"   模拟次数: 1,000次")
    print(f"   总交易数: {strategy_params['trades_per_year'] * strategy_params['years']}次")
    print(f"   平均结果: {simulation_result['mean_final']:,.0f} 港币")
    print(f"   中位数: {simulation_result['median_final']:,.0f} 港币")
    print(f"   成功概率: {simulation_result['success_rate']*100:.1f}%")
    
    # 4. 概率分布分析
    print(f"\n📊 收益概率分布:")
    print("="*40)
    print("概率     最终金额     总收益")
    print("-" * 30)
    
    percentiles = [5, 25, 50, 75, 95]
    amounts = [
        simulation_result['percentile_5'],
        simulation_result['percentile_25'], 
        simulation_result['median_final'],
        simulation_result['percentile_75'],
        simulation_result['percentile_95']
    ]
    
    for p, amount in zip(percentiles, amounts):
        profit = amount - strategy_params['initial_capital']
        print(f"{p:>2}%     {amount:>9,.0f}    {profit:>+8,.0f}")
    
    # 5. 不同情景分析
    print(f"\n📊 情景分析:")
    print("="*40)
    
    scenarios = [
        ('悲观情景', simulation_result['percentile_25']),
        ('基准情景', simulation_result['median_final']),
        ('乐观情景', simulation_result['percentile_75'])
    ]
    
    for scenario_name, final_amount in scenarios:
        profit = final_amount - strategy_params['initial_capital']
        profit_rate = profit / strategy_params['initial_capital']
        annual_equiv = ((final_amount / strategy_params['initial_capital']) ** (1/3)) - 1
        monthly_profit = profit / (strategy_params['years'] * 12)
        
        print(f"   {scenario_name}:")
        print(f"     最终金额: {final_amount:,.0f} 港币")
        print(f"     总收益: {profit:+,.0f} 港币 ({profit_rate*100:+.1f}%)")
        print(f"     等效年化: {annual_equiv*100:.2f}%")
        print(f"     月均收益: {monthly_profit:+,.0f} 港币")
    
    # 6. 与其他投资对比
    print(f"\n📊 投资对比 (3年):")
    print("="*50)
    
    initial = strategy_params['initial_capital']
    
    # 其他投资选项
    bank_deposit = initial * (1.02 ** 3)  # 2%年化
    bond_fund = initial * (1.035 ** 3)    # 3.5%年化
    index_fund = initial * (1.05 ** 3)    # 5%年化
    
    investments = [
        ('银行定期 (2%)', bank_deposit),
        ('债券基金 (3.5%)', bond_fund),
        ('指数基金 (5%)', index_fund),
        ('Y>0.4且X>0.4策略', simulation_result['median_final'])
    ]
    
    print("投资方式              最终金额    总收益")
    print("-" * 40)
    
    for name, amount in investments:
        profit = amount - initial
        print(f"{name:<18} {amount:>9,.0f}  {profit:>+8,.0f}")
    
    # 7. 详细收益分解
    print(f"\n💰 详细收益分解 (基准情景):")
    print("="*50)
    
    median_final = simulation_result['median_final']
    total_profit = median_final - initial
    
    # 年度收益预测
    annual_rate = ((median_final / initial) ** (1/3)) - 1
    
    print(f"   单次投资金额: {initial * strategy_params['position_size']:,.0f} 港币")
    print(f"   年均交易次数: {strategy_params['trades_per_year']}次")
    print(f"   3年总交易: {strategy_params['trades_per_year'] * 3}次")
    print(f"   平均年化收益: {annual_rate*100:.2f}%")
    print(f"   月均收益: {total_profit / (3*12):+,.0f} 港币")
    
    # 分年度预测
    print(f"\n📅 分年度收益预测:")
    print("="*40)
    
    for year in range(1, 4):
        year_end_amount = initial * ((1 + annual_rate) ** year)
        year_profit = year_end_amount - initial
        year_gain = year_end_amount - (initial * ((1 + annual_rate) ** (year-1)) if year > 1 else initial)
        
        print(f"   第{year}年末: {year_end_amount:,.0f} 港币 (当年收益{year_gain:+,.0f}, 累计{year_profit:+,.0f})")
    
    # 8. 风险分析
    print(f"\n🛡️ 风险分析:")
    print("="*30)
    
    worst_case = simulation_result['percentile_5']
    max_loss = initial - worst_case
    max_loss_rate = max_loss / initial
    
    single_position = initial * strategy_params['position_size']
    max_single_loss = single_position * 0.1  # 假设10%止损
    
    print(f"   单笔投资: {single_position:,.0f} 港币")
    print(f"   单笔最大风险: {max_single_loss:,.0f} 港币")
    print(f"   最坏情况亏损: {max_loss:,.0f} 港币 ({max_loss_rate*100:.1f}%)")
    print(f"   亏损概率: 约5%")
    print(f"   成功概率: {simulation_result['success_rate']*100:.1f}%")
    
    # 9. 实战执行计划
    print(f"\n💡 实战执行计划:")
    print("="*40)
    
    print(f"✅ 初始资金: 30,000港币")
    print(f"✅ 策略条件: Y>0.4 且 X>0.4")
    print(f"✅ 单次仓位: {strategy_params['position_size']*100:.0f}% ({single_position:,.0f}港币)")
    print(f"✅ 持有期: {strategy_params['holding_days']}天")
    print(f"✅ 交易频率: 每月2次")
    print(f"✅ 预期年化: {annual_rate*100:.2f}%")
    print(f"✅ 预期3年收益: {total_profit:+,.0f}港币")
    
    # 10. 成功要素
    print(f"\n🎯 成功要素:")
    print("="*20)
    print("✅ 严格执行Y>0.4且X>0.4信号")
    print("✅ 坚持25%仓位不超仓")
    print("✅ 严格120天持有期")
    print("✅ 每月最多2次交易")
    print("✅ 保持投资纪律")
    print("✅ 定期回顾调整")
    
    # 11. 最终建议
    print(f"\n🎉 最终建议:")
    print("="*20)
    
    if annual_rate > 0.05:
        print("✅ 策略有明显价值，建议执行")
    elif annual_rate > 0.03:
        print("⚠️ 策略有一定价值，谨慎执行")
    else:
        print("❌ 策略价值有限，建议其他投资")
    
    print(f"\n🎯 核心结论:")
    print(f"   3万港币投资3年")
    print(f"   预期收益: {total_profit:+,.0f}港币")
    print(f"   成功概率: {simulation_result['success_rate']*100:.1f}%")
    print(f"   月均收益: {total_profit/(3*12):+,.0f}港币")
    print(f"   风险等级: 中等")
    
    return {
        'initial_capital': initial,
        'expected_final': median_final,
        'expected_profit': total_profit,
        'annual_return': annual_rate,
        'success_rate': simulation_result['success_rate']
    }

if __name__ == "__main__":
    results = calculate_30k_3year_returns()
