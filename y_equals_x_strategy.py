#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
博弈论策略扩展：Y=X条件回测
包含：Y≥X≥0.4, Y=X>0.4, Y=X<0.333
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def y_equals_x_strategy():
    """Y=X策略回测"""
    
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("🎯 博弈论策略扩展：Y=X条件回测")
        print("="*60)
        print("📊 数据源: HK2800 (盈富基金)")
        print("🎯 新增策略: Y=X>0.4 (做多) + Y=X<0.333 (做空)")
        
        # 1. 获取数据
        cursor.execute("""
            SELECT date, close, adj_close, y_probability, inflow_ratio
            FROM hk2800 
            WHERE y_probability IS NOT NULL 
            AND inflow_ratio IS NOT NULL
            ORDER BY date ASC
        """)
        
        data = cursor.fetchall()
        df = pd.DataFrame(data, columns=['date', 'close', 'adj_close', 'y_probability', 'inflow_ratio'])
        
        for col in ['close', 'adj_close', 'y_probability', 'inflow_ratio']:
            df[col] = pd.to_numeric(df[col])
        df['date'] = pd.to_datetime(df['date'])
        
        print(f"📊 数据范围: {len(df)} 条记录")
        print(f"📅 时间跨度: {df['date'].min().strftime('%Y-%m-%d')} 到 {df['date'].max().strftime('%Y-%m-%d')}")
        
        # 2. 定义Y=X条件（允许小幅偏差）
        tolerance = 0.02  # 2%容差，认为Y≈X
        df['y_equals_x'] = abs(df['y_probability'] - df['inflow_ratio']) <= tolerance
        
        # 3. 定义所有策略
        # 原策略：Y≥X≥0.4
        df['strategy_original'] = (df['y_probability'] >= 0.4) & (df['inflow_ratio'] >= 0.4) & (df['y_probability'] >= df['inflow_ratio'])
        
        # 新策略1：Y=X>0.4 (双方都支持，强烈看多)
        df['strategy_y_eq_x_high'] = df['y_equals_x'] & (df['y_probability'] > 0.4) & (df['inflow_ratio'] > 0.4)
        
        # 新策略2：Y=X<0.333 (双方都不支持，强烈看空)
        df['strategy_y_eq_x_low'] = df['y_equals_x'] & (df['y_probability'] < 0.333) & (df['inflow_ratio'] < 0.333)
        
        # 计算Edeta
        df['edeta'] = (8 * df['inflow_ratio'] - 3) * df['y_probability'] - 3 * df['inflow_ratio'] + 1
        
        # 4. 策略信号统计
        total_days = len(df)
        years = (df['date'].max() - df['date'].min()).days / 365.25
        
        strategies = {
            '原策略 Y≥X≥0.4': df['strategy_original'].sum(),
            '新策略 Y=X>0.4': df['strategy_y_eq_x_high'].sum(),
            '新策略 Y=X<0.333': df['strategy_y_eq_x_low'].sum(),
            'Y=X总数': df['y_equals_x'].sum()
        }
        
        print(f"\n📊 策略信号统计:")
        print("="*50)
        print("策略              信号数   频率%   年均次数")
        print("-" * 50)
        
        for name, signals in strategies.items():
            frequency = signals / total_days * 100
            annual_trades = signals / years
            print(f"{name:<15} {signals:>6}   {frequency:>5.2f}   {annual_trades:>7.1f}")
        
        # 5. Y=X分布分析
        print(f"\n📊 Y=X分布分析:")
        print("="*40)
        
        y_eq_x_data = df[df['y_equals_x']]
        if len(y_eq_x_data) > 0:
            print(f"   Y=X总天数: {len(y_eq_x_data)}")
            print(f"   Y=X频率: {len(y_eq_x_data)/total_days*100:.2f}%")
            
            # Y=X时的Y值分布
            y_values = y_eq_x_data['y_probability']
            print(f"   Y值范围: {y_values.min():.3f} - {y_values.max():.3f}")
            print(f"   Y值平均: {y_values.mean():.3f}")
            
            # 分段统计
            high_count = ((y_values > 0.4).sum())
            mid_count = ((y_values >= 0.333) & (y_values <= 0.4)).sum()
            low_count = ((y_values < 0.333).sum())
            
            print(f"   Y=X>0.4: {high_count} 次 ({high_count/len(y_eq_x_data)*100:.1f}%)")
            print(f"   Y=X中性: {mid_count} 次 ({mid_count/len(y_eq_x_data)*100:.1f}%)")
            print(f"   Y=X<0.333: {low_count} 次 ({low_count/len(y_eq_x_data)*100:.1f}%)")
        
        # 6. 回测策略表现
        def backtest_strategy(condition, strategy_name, is_short=False):
            """回测策略表现"""
            
            signal_data = df[condition].copy()
            
            if len(signal_data) == 0:
                print(f"\n❌ {strategy_name}: 无交易信号")
                return None
            
            print(f"\n💰 {strategy_name} 回测结果:")
            print("="*45)
            
            holding_periods = [30, 60, 90, 120]
            results = {}
            
            for holding_days in holding_periods:
                returns = []
                
                for i, row in signal_data.iterrows():
                    entry_date = row['date']
                    entry_price = row['adj_close']
                    
                    # 找到持有期后的价格
                    exit_date = entry_date + timedelta(days=holding_days)
                    future_data = df[df['date'] >= exit_date]
                    
                    if not future_data.empty:
                        exit_price = future_data.iloc[0]['adj_close']
                        
                        if is_short:
                            # 做空：价格下跌为盈利
                            return_rate = (entry_price - exit_price) / entry_price
                        else:
                            # 做多：价格上涨为盈利
                            return_rate = (exit_price - entry_price) / entry_price
                        
                        returns.append(return_rate)
                
                if returns:
                    avg_return = np.mean(returns)
                    win_rate = sum(1 for r in returns if r > 0) / len(returns)
                    
                    # 年化收益估算
                    trades_per_year = len(signal_data) / years
                    annual_return = avg_return * trades_per_year
                    
                    results[holding_days] = {
                        'avg_return': avg_return,
                        'win_rate': win_rate,
                        'annual_return': annual_return,
                        'trades': len(returns)
                    }
                    
                    direction = "做空" if is_short else "做多"
                    print(f"   {holding_days}天{direction}: 收益{avg_return*100:+.2f}%, 胜率{win_rate*100:.1f}%, 年化{annual_return*100:+.2f}%")
            
            return results
        
        # 7. 执行回测
        original_results = backtest_strategy(df['strategy_original'], '原策略 Y≥X≥0.4', is_short=False)
        high_results = backtest_strategy(df['strategy_y_eq_x_high'], 'Y=X>0.4 (做多)', is_short=False)
        low_results = backtest_strategy(df['strategy_y_eq_x_low'], 'Y=X<0.333 (做空)', is_short=True)
        
        # 8. 组合策略分析
        print(f"\n🔄 组合策略分析:")
        print("="*40)
        
        # 做多组合：原策略 + Y=X>0.4
        df['long_combo'] = df['strategy_original'] | df['strategy_y_eq_x_high']
        long_combo_signals = df['long_combo'].sum()
        
        # 全策略组合：做多 + 做空
        df['full_combo'] = df['strategy_original'] | df['strategy_y_eq_x_high'] | df['strategy_y_eq_x_low']
        full_combo_signals = df['full_combo'].sum()
        
        print(f"   做多组合信号: {long_combo_signals} ({long_combo_signals/total_days*100:.2f}%)")
        print(f"   全策略组合: {full_combo_signals} ({full_combo_signals/total_days*100:.2f}%)")
        print(f"   年均交易机会: {full_combo_signals/years:.1f}次")
        
        # 9. 策略对比表
        print(f"\n🏆 策略对比表 (120天持有):")
        print("="*65)
        print("策略           信号数  年化收益   胜率    交易类型   推荐度")
        print("-" * 65)
        
        all_results = [
            ('原策略', original_results, '做多'),
            ('Y=X>0.4', high_results, '做多'),
            ('Y=X<0.333', low_results, '做空')
        ]
        
        best_annual = 0
        best_strategy = None
        
        for name, results, trade_type in all_results:
            if results and 120 in results:
                result = results[120]
                signals = result['trades']
                annual_return = result['annual_return']
                win_rate = result['win_rate']
                
                # 推荐度评分
                score = 0
                if annual_return > 0.05: score += 1
                if annual_return > 0.1: score += 1
                if annual_return > 0.2: score += 1
                if win_rate > 0.5: score += 1
                if win_rate > 0.6: score += 1
                
                recommendation = "⭐" * min(score, 5)
                
                print(f"{name:<12} {signals:>6}  {annual_return*100:>+7.2f}%  {win_rate*100:>5.1f}%    {trade_type:<6}   {recommendation}")
                
                if annual_return > best_annual:
                    best_annual = annual_return
                    best_strategy = name
        
        # 10. 买入持有基准
        start_price = df.iloc[0]['adj_close']
        end_price = df.iloc[-1]['adj_close']
        buy_hold_return = (end_price - start_price) / start_price
        annual_buy_hold = ((1 + buy_hold_return) ** (1/years)) - 1
        
        print(f"\n📊 基准对比:")
        print("="*30)
        print(f"   买入持有年化: {annual_buy_hold*100:+.2f}%")
        print(f"   最佳策略年化: {best_annual*100:+.2f}%")
        print(f"   超额收益: {(best_annual - annual_buy_hold)*100:+.2f}%")
        
        # 11. 当前市场状态
        latest = df.iloc[-1]
        
        print(f"\n📊 当前市场状态 ({latest['date'].strftime('%Y-%m-%d')}):")
        print("="*50)
        print(f"   当前Y值: {latest['y_probability']:.3f}")
        print(f"   当前X值: {latest['inflow_ratio']:.3f}")
        print(f"   Y-X差值: {abs(latest['y_probability'] - latest['inflow_ratio']):.3f}")
        print(f"   是否Y=X: {'✅ 是' if latest['y_equals_x'] else '❌ 否'}")
        
        # 当前信号判断
        current_signals = []
        if latest['strategy_original']:
            current_signals.append("原策略(做多)")
        if latest['strategy_y_eq_x_high']:
            current_signals.append("Y=X>0.4(做多)")
        if latest['strategy_y_eq_x_low']:
            current_signals.append("Y=X<0.333(做空)")
        
        if current_signals:
            print(f"   🎯 当前信号: {', '.join(current_signals)}")
        else:
            print(f"   ❌ 当前无交易信号")
        
        # 12. 策略价值评估
        print(f"\n💡 Y=X策略价值评估:")
        print("="*40)
        
        if high_results and 120 in high_results:
            high_annual = high_results[120]['annual_return']
            if high_annual > annual_buy_hold:
                print(f"✅ Y=X>0.4策略有效，年化{high_annual*100:.2f}%")
            else:
                print(f"⚠️ Y=X>0.4策略收益有限")
        
        if low_results and 120 in low_results:
            low_annual = low_results[120]['annual_return']
            if low_annual > 0.05:
                print(f"✅ Y=X<0.333做空策略有效，年化{low_annual*100:.2f}%")
            else:
                print(f"⚠️ Y=X<0.333做空策略收益有限")
        
        print(f"✅ 扩展策略增加了{full_combo_signals - df['strategy_original'].sum()}个额外交易机会")
        print(f"✅ 年均交易机会从{df['strategy_original'].sum()/years:.1f}次增加到{full_combo_signals/years:.1f}次")
        
        return True
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    y_equals_x_strategy()
