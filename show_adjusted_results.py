#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示调整策略结果
===============

显示HK00023调整策略的回测结果
其他区域止损从2%调整到2.3%

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import glob
import os

def show_adjusted_results():
    """显示调整策略结果"""
    # 查找最新的调整策略文件
    excel_files = glob.glob("HK00023调整策略回测_*.xlsx")
    if not excel_files:
        print("❌ 未找到调整策略文件")
        return
    
    # 获取最新文件
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"🏦 HK00023调整策略回测: {latest_file}")
    
    try:
        # 读取所有工作表
        df_trades = pd.read_excel(latest_file, sheet_name='调整策略交易记录')
        df_summary = pd.read_excel(latest_file, sheet_name='汇总统计')
        df_zone_analysis = pd.read_excel(latest_file, sheet_name='区域分析')
        df_adjustment = pd.read_excel(latest_file, sheet_name='调整说明')
        
        print(f"\n📊 HK00023调整策略汇总 (其他区域止损2%→2.3%):")
        print("="*90)
        
        # 显示关键统计
        for _, row in df_summary.iterrows():
            if pd.notna(row['数值']):
                print(f"{row['项目']}: {row['数值']}")
        
        print(f"\n📊 区域分析:")
        print("="*80)
        print(df_zone_analysis.to_string(index=False))
        
        print(f"\n📈 前15条调整策略交易记录预览:")
        print("="*150)
        
        # 显示关键列
        preview_columns = ['交易序号', '开仓日期', '交易方向', '开仓价格', '均值回归中值', 
                          '价格偏离度%', '净利润', 'Y值', 'X值', 'E值', '策略区域', '平仓原因', '止损参数']
        
        print(df_trades[preview_columns].head(15).to_string(index=False))
        
        print(f"\n📊 调整策略详细分析:")
        print("="*80)
        
        # 按策略区域分析
        print(f"按策略区域分析:")
        for zone in df_trades['策略区域'].unique():
            zone_data = df_trades[df_trades['策略区域'] == zone]
            count = len(zone_data)
            total_profit = zone_data['净利润'].sum()
            avg_profit = zone_data['净利润'].mean()
            win_rate = len(zone_data[zone_data['净利润'] > 0]) / count * 100
            
            # 显示该区域的交易方向
            directions = zone_data['交易方向'].value_counts()
            direction_str = ", ".join([f"{dir}:{cnt}次" for dir, cnt in directions.items()])
            
            print(f"• {zone}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%, 方向({direction_str})")
        
        # 按交易方向分析
        print(f"\n按交易方向分析:")
        for direction in df_trades['交易方向'].unique():
            dir_data = df_trades[df_trades['交易方向'] == direction]
            count = len(dir_data)
            total_profit = dir_data['净利润'].sum()
            avg_profit = dir_data['净利润'].mean()
            win_rate = len(dir_data[dir_data['净利润'] > 0]) / count * 100
            
            print(f"• {direction}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # 其他区域调整效果分析
        print(f"\n🎯 其他区域调整效果分析 (止损2%→2.3%):")
        print("="*70)
        
        other_zone_trades = df_trades[df_trades['策略区域'] == '其他区域']
        if len(other_zone_trades) > 0:
            ot_count = len(other_zone_trades)
            ot_profit = other_zone_trades['净利润'].sum()
            ot_avg = other_zone_trades['净利润'].mean()
            ot_win_rate = len(other_zone_trades[other_zone_trades['净利润'] > 0]) / ot_count * 100
            
            print(f"调整后其他区域表现:")
            print(f"• 交易次数: {ot_count}次")
            print(f"• 总盈亏: {ot_profit:+,.0f}港币")
            print(f"• 平均盈亏: {ot_avg:+.0f}港币")
            print(f"• 胜率: {ot_win_rate:.1f}%")
            
            # 分析止盈止损效果
            ot_take_profit = other_zone_trades[other_zone_trades['平仓原因'] == '止盈']
            ot_stop_loss = other_zone_trades[other_zone_trades['平仓原因'] == '止损']
            ot_expire = other_zone_trades[other_zone_trades['平仓原因'] == '到期平仓']
            
            if len(ot_take_profit) > 0:
                tp_profit = ot_take_profit['净利润'].sum()
                print(f"• 止盈交易: {len(ot_take_profit)}次, 盈利{tp_profit:+,.0f}港币")
            if len(ot_stop_loss) > 0:
                sl_loss = ot_stop_loss['净利润'].sum()
                print(f"• 止损交易: {len(ot_stop_loss)}次, 亏损{sl_loss:+,.0f}港币")
            if len(ot_expire) > 0:
                exp_profit = ot_expire['净利润'].sum()
                print(f"• 到期平仓: {len(ot_expire)}次, 盈亏{exp_profit:+,.0f}港币")
            
            # 计算止盈止损比例
            total_exit_trades = len(ot_take_profit) + len(ot_stop_loss)
            if total_exit_trades > 0:
                tp_ratio = len(ot_take_profit) / total_exit_trades * 100
                sl_ratio = len(ot_stop_loss) / total_exit_trades * 100
                print(f"• 止盈比例: {tp_ratio:.1f}%, 止损比例: {sl_ratio:.1f}%")
                
                print(f"\n调整效果对比:")
                print(f"• 调整前(2%止损): 预期止损更频繁")
                print(f"• 调整后(2.3%止损): 止损比例{sl_ratio:.1f}%，容忍度提高")
        
        # E值分析
        print(f"\n按E值分析:")
        e_positive = df_trades[df_trades['E值'] > 0]
        e_negative = df_trades[df_trades['E值'] < 0]
        
        if len(e_positive) > 0:
            e_pos_profit = e_positive['净利润'].sum()
            e_pos_count = len(e_positive)
            e_pos_win_rate = len(e_positive[e_positive['净利润'] > 0]) / e_pos_count * 100
            print(f"• E>0: {e_pos_count}次, 总盈亏{e_pos_profit:+,.0f}港币, 胜率{e_pos_win_rate:.1f}%")
        
        if len(e_negative) > 0:
            e_neg_profit = e_negative['净利润'].sum()
            e_neg_count = len(e_negative)
            e_neg_win_rate = len(e_negative[e_negative['净利润'] > 0]) / e_neg_count * 100
            print(f"• E<0: {e_neg_count}次, 总盈亏{e_neg_profit:+,.0f}港币, 胜率{e_neg_win_rate:.1f}%")
        
        # 平仓原因分析
        print(f"\n按平仓原因分析:")
        exit_reasons = df_trades['平仓原因'].value_counts()
        for reason, count in exit_reasons.items():
            reason_data = df_trades[df_trades['平仓原因'] == reason]
            total_profit = reason_data['净利润'].sum()
            avg_profit = reason_data['净利润'].mean()
            win_rate = len(reason_data[reason_data['净利润'] > 0]) / count * 100
            print(f"• {reason}: {count}次, 总盈亏{total_profit:+,.0f}港币, "
                  f"平均{avg_profit:+.0f}港币, 胜率{win_rate:.1f}%")
        
        # 调整前后对比分析
        print(f"\n📈 调整前后对比分析:")
        print("="*70)
        
        print(f"参数调整对比:")
        print(f"• 其他区域止损: 2% → 2.3% (+0.3个百分点)")
        print(f"• 其他区域止盈: 1% (不变)")
        print(f"• 高值盈利区: 止盈2%止损1% (不变)")
        print(f"• 强亏损区: 止盈2%止损1% (不变)")
        
        # 理论分析
        print(f"\n理论效果分析:")
        print(f"• 提高止损容忍度: 从2%提高到2.3%")
        print(f"• 预期减少止损次数: 降低无效止损")
        print(f"• 风险收益比调整: 1:2 → 1:1.87")
        print(f"• 预期改善其他区域表现")
        
        # 均值回归分析
        print(f"\n📈 均值回归分析:")
        print("="*60)
        
        # 分析不同偏离度下的策略效果
        deviation_ranges = [
            ('高估区 (>5%)', df_trades[df_trades['价格偏离度%'] > 5]),
            ('合理区 (-5%到5%)', df_trades[(df_trades['价格偏离度%'] >= -5) & (df_trades['价格偏离度%'] <= 5)]),
            ('低估区 (<-5%)', df_trades[df_trades['价格偏离度%'] < -5])
        ]
        
        for range_name, range_data in deviation_ranges:
            if len(range_data) > 0:
                count = len(range_data)
                total_profit = range_data['净利润'].sum()
                win_rate = len(range_data[range_data['净利润'] > 0]) / count * 100
                avg_deviation = range_data['价格偏离度%'].mean()
                
                # 分析该区域的策略分布
                zone_dist = range_data['策略区域'].value_counts()
                
                print(f"• {range_name}: {count}次, 盈亏{total_profit:+,.0f}港币, "
                      f"胜率{win_rate:.1f}%, 平均偏离{avg_deviation:+.1f}%")
                print(f"  主要区域: {zone_dist.index[0] if len(zone_dist) > 0 else 'N/A'}")
        
        # 最佳和最差交易分析
        print(f"\n🎯 最佳和最差交易分析:")
        print("="*60)
        
        best_trade = df_trades.loc[df_trades['净利润'].idxmax()]
        worst_trade = df_trades.loc[df_trades['净利润'].idxmin()]
        
        print(f"最佳交易:")
        print(f"• 交易{best_trade['交易序号']}: {best_trade['开仓日期']}, {best_trade['交易方向']}")
        print(f"  盈利{best_trade['净利润']:+.0f}港币, {best_trade['策略区域']}")
        print(f"  Y={best_trade['Y值']:.3f}, X={best_trade['X值']:.3f}, E={best_trade['E值']:.3f}")
        print(f"  偏离度{best_trade['价格偏离度%']:+.1f}%, 平仓原因: {best_trade['平仓原因']}")
        print(f"  止损参数: {best_trade['止损参数']}")
        
        print(f"\n最差交易:")
        print(f"• 交易{worst_trade['交易序号']}: {worst_trade['开仓日期']}, {worst_trade['交易方向']}")
        print(f"  亏损{worst_trade['净利润']:+.0f}港币, {worst_trade['策略区域']}")
        print(f"  Y={worst_trade['Y值']:.3f}, X={worst_trade['X值']:.3f}, E={worst_trade['E值']:.3f}")
        print(f"  偏离度{worst_trade['价格偏离度%']:+.1f}%, 平仓原因: {worst_trade['平仓原因']}")
        print(f"  止损参数: {worst_trade['止损参数']}")
        
        # 调整策略总结
        print(f"\n📊 调整策略总结:")
        print("="*70)
        
        final_capital = df_trades['账户余额'].iloc[-1]
        total_return = (final_capital / 30000 - 1) * 100
        total_profit = df_trades['净利润'].sum()
        win_rate = len(df_trades[df_trades['净利润'] > 0]) / len(df_trades) * 100
        
        print(f"• 调整策略: 其他区域止损2%→2.3%")
        print(f"• 总收益率: {total_return:+.2f}%")
        print(f"• 总盈亏: {total_profit:+,.0f}港币")
        print(f"• 总交易次数: {len(df_trades)}次")
        print(f"• 胜率: {win_rate:.1f}%")
        print(f"• 最大单笔盈利: {df_trades['净利润'].max():+.0f}港币")
        print(f"• 最大单笔亏损: {df_trades['净利润'].min():+.0f}港币")
        print(f"• 平均每笔盈亏: {total_profit/len(df_trades):+.0f}港币")
        
        print(f"\n💡 调整策略Excel文件结构:")
        print("="*70)
        print(f"📊 工作表1: 调整策略交易记录")
        print(f"   • {len(df_trades)}条详细交易记录")
        print(f"   • 包含止损参数标识")
        
        print(f"\n📈 工作表2: 汇总统计")
        print(f"   • 完整的财务统计")
        print(f"   • 调整前后对比")
        
        print(f"\n📊 工作表3: 区域分析")
        print(f"   • 各策略区域详细表现")
        print(f"   • 区域盈亏统计")
        
        print(f"\n📋 工作表4: 调整说明")
        print(f"   • 详细的调整理由和预期效果")
        print(f"   • 参数调整对比表")
        
        print(f"\n🎉 调整策略关键发现:")
        print("="*60)
        
        # 找出表现最好和最差的区域
        best_zone = df_zone_analysis.loc[df_zone_analysis['总盈亏'].idxmax()]
        worst_zone = df_zone_analysis.loc[df_zone_analysis['总盈亏'].idxmin()]
        
        print(f"✅ 最佳区域: {best_zone['策略区域']}")
        print(f"   {best_zone['交易次数']}次{best_zone['交易方向']}，盈利{best_zone['总盈亏']:+.0f}港币")
        
        print(f"❌ 最差区域: {worst_zone['策略区域']}")
        print(f"   {worst_zone['交易次数']}次{worst_zone['交易方向']}，亏损{worst_zone['总盈亏']:+.0f}港币")
        
        print(f"💡 调整效果: 止损参数微调，理论上应减少止损次数")
        print(f"🎯 整体表现: 仍需进一步优化其他策略参数")
        
        # 显示调整说明
        print(f"\n📋 调整说明详细:")
        print("="*70)
        print(df_adjustment.to_string(index=False))
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")

if __name__ == "__main__":
    show_adjusted_results()
