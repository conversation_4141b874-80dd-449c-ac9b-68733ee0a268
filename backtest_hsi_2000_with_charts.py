#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数策略回测程序 - 每月定投2000港币版本 + 绘图
===============================================

策略说明：
- 初始资金：30,000港元
- 每月定投：2,000港元
- 每次交易：50个恒指点子
- 分析周期：20年
- 使用复利计算
- 包含详细图表分析

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class HSI2000MonthlyBacktest:
    def __init__(self):
        """初始化恒指每月定投2000回测系统"""
        self.symbol = "^HSI"
        self.initial_capital = 30000
        self.monthly_investment = 2000  # 每月定投2000港币
        self.point_value = 50  # 每个恒指点子价值50港元
        self.data = None
        
        # 策略参数
        self.strategy_params = {
            'high_profit_y': 0.4,
            'high_profit_x': 0.4,
            'control_zone_min': 0.333,
            'control_zone_max': 0.4,
            'strong_loss_y': 0.25,
            'strong_loss_x': 0.25,
            'transaction_cost': 30,
            'high_profit_take_profit': 0.016,
            'high_profit_stop_loss': 0.008,
            'strong_loss_take_profit': 0.008,
            'strong_loss_stop_loss': 0.016,
            'other_take_profit': 0.01,
            'other_stop_loss': 0.02,
        }
        
    def fetch_hsi_data(self):
        """获取恒生指数历史数据"""
        print("📈 获取恒生指数20年历史数据...")
        
        try:
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(period="20y", interval="1d")
            
            if self.data.empty:
                print("❌ 数据获取失败：返回空数据")
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取恒生指数数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data):,} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标"""
        print("📊 计算技术指标...")
        
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # Y值计算
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
        
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        # X值计算
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        rsi_adjustment = 0.3 * (self.data['rsi'] / 100 - 0.5)
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        # E值计算
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        # 策略区域分类
        conditions = [
            (self.data['y_probability'] > self.strategy_params['high_profit_y']) & 
            (self.data['inflow_ratio'] > self.strategy_params['high_profit_x']),
            
            (self.data['y_probability'] > self.strategy_params['control_zone_min']) & 
            (self.data['y_probability'] < self.strategy_params['control_zone_max']),
            
            (self.data['y_probability'] < self.strategy_params['strong_loss_y']) | 
            (self.data['inflow_ratio'] < self.strategy_params['strong_loss_x']),
        ]
        
        choices = ['高值盈利区', '控股商控制区', '强亏损区']
        self.data['strategy_zone'] = np.select(conditions, choices, default='其他区域')
        
        print("✅ 指标计算完成")
    
    def backtest_with_monthly_investment(self):
        """执行每月定投2000回测"""
        print("🎯 开始每月定投2000回测...")
        print(f"💰 初始资金: {self.initial_capital:,}港币")
        print(f"📅 每月定投: {self.monthly_investment:,}港币")
        print(f"🔄 使用复利计算")
        
        trades = []
        monthly_investments = []
        daily_portfolio = []
        current_cash = self.initial_capital
        position = None
        entry_price = None
        entry_date = None
        take_profit = 0
        stop_loss = 0
        
        # 记录每月定投
        last_investment_month = None
        total_invested = self.initial_capital
        
        for i in range(60, len(self.data)):
            row = self.data.iloc[i]
            date = row['date']
            price = row['close']
            y_val = row['y_probability']
            x_val = row['inflow_ratio']
            e_val = row['e_value']
            zone = row['strategy_zone']
            
            # 检查是否需要每月定投
            current_month = date.strftime('%Y-%m')
            if last_investment_month != current_month:
                current_cash += self.monthly_investment
                total_invested += self.monthly_investment
                monthly_investments.append({
                    '日期': date.strftime('%Y-%m-%d'),
                    '定投金额': self.monthly_investment,
                    '累计投入': total_invested,
                    '当前现金': current_cash
                })
                last_investment_month = current_month
            
            # 如果有持仓，检查是否需要平仓
            if position:
                profit_points = 0
                if position == '做多':
                    profit_points = price - entry_price
                else:
                    profit_points = entry_price - price
                
                profit_pct = profit_points / entry_price
                
                # 检查止盈止损
                should_exit = False
                exit_reason = ''
                
                if position == '做多':
                    if profit_pct >= take_profit:
                        exit_reason = '止盈'
                        should_exit = True
                    elif profit_pct <= -stop_loss:
                        exit_reason = '止损'
                        should_exit = True
                else:  # 做空
                    if profit_pct >= take_profit:
                        exit_reason = '止盈'
                        should_exit = True
                    elif profit_pct <= -stop_loss:
                        exit_reason = '止损'
                        should_exit = True
                
                if should_exit:
                    # 计算实际盈亏
                    if exit_reason == '止盈':
                        actual_profit_points = take_profit * entry_price
                    else:  # 止损
                        actual_profit_points = -stop_loss * entry_price
                    
                    gross_profit = actual_profit_points * self.point_value
                    net_profit = gross_profit - self.strategy_params['transaction_cost']
                    
                    # 记录交易
                    trade_record = {
                        '开仓日期': entry_date.strftime('%Y-%m-%d'),
                        '平仓日期': date.strftime('%Y-%m-%d'),
                        '交易方向': position,
                        '开仓价格': round(entry_price, 2),
                        '平仓价格': round(price, 2),
                        '点数': round(actual_profit_points, 2),
                        '毛利润': round(gross_profit, 2),
                        '交易成本': self.strategy_params['transaction_cost'],
                        '净利润': round(net_profit, 2),
                        '收益率%': round(profit_pct * 100, 2),
                        'Y值': round(y_val, 3),
                        'X值': round(x_val, 3),
                        'E值': round(e_val, 3),
                        '策略区域': zone,
                        '平仓原因': exit_reason
                    }
                    
                    trades.append(trade_record)
                    current_cash += net_profit
                    position = None
                    
                    continue
            
            # 控股商控制区不开新仓
            if zone == '控股商控制区':
                pass
            # 根据区域决定交易方向
            elif zone == '高值盈利区':
                position = '做多'
                take_profit = self.strategy_params['high_profit_take_profit']
                stop_loss = self.strategy_params['high_profit_stop_loss']
                entry_price = price
                entry_date = date
            elif zone == '强亏损区':
                position = '做空'
                take_profit = self.strategy_params['strong_loss_take_profit']
                stop_loss = self.strategy_params['strong_loss_stop_loss']
                entry_price = price
                entry_date = date
            elif zone == '其他区域':
                position = '做空'
                take_profit = self.strategy_params['other_take_profit']
                stop_loss = self.strategy_params['other_stop_loss']
                entry_price = price
                entry_date = date
            
            # 记录每日组合价值
            daily_portfolio.append({
                '日期': date.strftime('%Y-%m-%d'),  # 转换为字符串避免时区问题
                '恒指价格': price,
                '现金': current_cash,
                '累计投入': total_invested,
                '策略区域': zone,
                'Y值': y_val,
                'X值': x_val,
                'E值': e_val
            })
        
        return pd.DataFrame(trades), pd.DataFrame(monthly_investments), pd.DataFrame(daily_portfolio), total_invested, current_cash
    
    def create_charts(self, trades_df, monthly_df, daily_df, total_invested, final_cash):
        """创建图表"""
        print("📊 生成图表...")
        
        # 计算最终总价值
        total_trading_profit = trades_df['净利润'].sum() if len(trades_df) > 0 else 0
        final_total_value = final_cash + total_trading_profit
        
        # 创建图表
        fig = plt.figure(figsize=(20, 16))
        
        # 1. 投资组合价值增长图
        ax1 = plt.subplot(3, 2, 1)
        daily_df['累计投入_万'] = daily_df['累计投入'] / 10000
        daily_df['现金_万'] = daily_df['现金'] / 10000
        daily_df['日期_dt'] = pd.to_datetime(daily_df['日期'])

        plt.plot(daily_df['日期_dt'], daily_df['累计投入_万'], label='累计投入', linewidth=2, color='blue')
        plt.plot(daily_df['日期_dt'], daily_df['现金_万'], label='现金价值', linewidth=2, color='green')
        plt.title('投资组合价值增长 (每月定投2000港币)', fontsize=14, fontweight='bold')
        plt.xlabel('日期')
        plt.ylabel('价值 (万港币)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
        ax1.xaxis.set_major_locator(mdates.YearLocator(2))
        plt.xticks(rotation=45)
        
        # 2. 恒生指数价格走势
        ax2 = plt.subplot(3, 2, 2)
        plt.plot(daily_df['日期_dt'], daily_df['恒指价格'], linewidth=1, color='red', alpha=0.7)
        plt.title('恒生指数20年走势', fontsize=14, fontweight='bold')
        plt.xlabel('日期')
        plt.ylabel('恒指点数')
        plt.grid(True, alpha=0.3)
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
        ax2.xaxis.set_major_locator(mdates.YearLocator(2))
        plt.xticks(rotation=45)
        
        # 3. 策略区域分布
        ax3 = plt.subplot(3, 2, 3)
        zone_counts = daily_df['策略区域'].value_counts()
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']
        plt.pie(zone_counts.values, labels=zone_counts.index, autopct='%1.1f%%', 
                colors=colors, startangle=90)
        plt.title('策略区域时间分布', fontsize=14, fontweight='bold')
        
        # 4. 交易盈亏分布
        if len(trades_df) > 0:
            ax4 = plt.subplot(3, 2, 4)
            profits = trades_df['净利润']
            plt.hist(profits, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
            plt.axvline(profits.mean(), color='red', linestyle='--', linewidth=2, 
                       label=f'平均: {profits.mean():.0f}港币')
            plt.title('交易盈亏分布', fontsize=14, fontweight='bold')
            plt.xlabel('净利润 (港币)')
            plt.ylabel('交易次数')
            plt.legend()
            plt.grid(True, alpha=0.3)
        
        # 5. X、Y值散点图
        ax5 = plt.subplot(3, 2, 5)
        colors_map = {'高值盈利区': 'red', '强亏损区': 'blue', '其他区域': 'green', '控股商控制区': 'orange'}
        for zone in daily_df['策略区域'].unique():
            zone_data = daily_df[daily_df['策略区域'] == zone]
            plt.scatter(zone_data['X值'], zone_data['Y值'], 
                       c=colors_map.get(zone, 'gray'), label=zone, alpha=0.6, s=1)
        
        plt.axhline(y=0.4, color='red', linestyle='--', alpha=0.5)
        plt.axvline(x=0.4, color='red', linestyle='--', alpha=0.5)
        plt.axhline(y=0.25, color='blue', linestyle='--', alpha=0.5)
        plt.axvline(x=0.25, color='blue', linestyle='--', alpha=0.5)
        plt.axhline(y=0.333, color='orange', linestyle='--', alpha=0.5)
        
        plt.title('X、Y值分布与策略区域', fontsize=14, fontweight='bold')
        plt.xlabel('X值 (资金流比例)')
        plt.ylabel('Y值 (控制系数)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 6. 月度收益统计
        if len(trades_df) > 0:
            ax6 = plt.subplot(3, 2, 6)
            trades_df['平仓日期'] = pd.to_datetime(trades_df['平仓日期'])
            trades_df['年月'] = trades_df['平仓日期'].dt.to_period('M')
            monthly_profits = trades_df.groupby('年月')['净利润'].sum()
            
            colors = ['green' if x >= 0 else 'red' for x in monthly_profits.values]
            plt.bar(range(len(monthly_profits)), monthly_profits.values, color=colors, alpha=0.7)
            plt.title('月度交易盈亏', fontsize=14, fontweight='bold')
            plt.xlabel('时间')
            plt.ylabel('月度净利润 (港币)')
            plt.xticks(range(0, len(monthly_profits), 12), 
                      [str(monthly_profits.index[i]) for i in range(0, len(monthly_profits), 12)], 
                      rotation=45)
            plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_filename = f"恒指定投2000图表_{timestamp}.png"
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        print(f"✅ 图表已保存至: {chart_filename}")
        
        plt.show()
        
        return chart_filename
    
    def analyze_results(self, trades_df, monthly_df, daily_df, total_invested, final_cash):
        """分析结果"""
        print("\n📊 每月定投2000港币回测结果分析")
        print("=" * 60)
        
        # 基本统计
        total_trades = len(trades_df)
        total_trading_profit = trades_df['净利润'].sum() if total_trades > 0 else 0
        final_total_value = final_cash + total_trading_profit
        total_return = final_total_value - total_invested
        total_return_rate = (total_return / total_invested) * 100
        
        # 年化收益率
        years = 20
        annual_return_rate = ((final_total_value / total_invested) ** (1/years) - 1) * 100
        
        if total_trades > 0:
            winning_trades = len(trades_df[trades_df['净利润'] > 0])
            win_rate = winning_trades / total_trades * 100
            max_profit = trades_df['净利润'].max()
            max_loss = trades_df['净利润'].min()
            avg_profit = trades_df['净利润'].mean()
            
            # 按区域分析
            zone_stats = trades_df.groupby('策略区域').agg({
                '净利润': ['count', 'sum', 'mean'],
                '收益率%': 'mean'
            }).round(2)
        else:
            winning_trades = 0
            win_rate = 0
            max_profit = 0
            max_loss = 0
            avg_profit = 0
            zone_stats = pd.DataFrame()
        
        # 输出结果
        print(f"💰 资金统计:")
        print(f"• 初始资金: {self.initial_capital:,} 港元")
        print(f"• 20年定投总额: {total_invested:,} 港元")
        print(f"• 最终现金: {final_cash:,.0f} 港元")
        print(f"• 交易总盈亏: {total_trading_profit:,.0f} 港元")
        print(f"• 最终总价值: {final_total_value:,.0f} 港元")
        print(f"• 总收益: {total_return:,.0f} 港元")
        print(f"• 总收益率: {total_return_rate:.2f}%")
        print(f"• 年化收益率: {annual_return_rate:.2f}%")
        
        if total_trades > 0:
            print(f"\n📈 交易统计:")
            print(f"• 总交易次数: {total_trades}")
            print(f"• 盈利次数: {winning_trades}")
            print(f"• 胜率: {win_rate:.1f}%")
            print(f"• 最大单笔盈利: {max_profit:,.0f} 港元")
            print(f"• 最大单笔亏损: {max_loss:,.0f} 港元")
            print(f"• 平均每笔盈亏: {avg_profit:,.0f} 港元")
            
            print(f"\n📊 区域分析:")
            print(zone_stats)
        
        # 定投统计
        monthly_count = len(monthly_df)
        print(f"\n📅 定投统计:")
        print(f"• 定投月数: {monthly_count} 个月")
        print(f"• 每月定投: {self.monthly_investment:,} 港元")
        print(f"• 定投总金额: {total_invested - self.initial_capital:,} 港元")
        
        # 保存到Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"恒指定投2000回测结果_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            if len(trades_df) > 0:
                trades_df.to_excel(writer, sheet_name='交易记录', index=False)
            monthly_df.to_excel(writer, sheet_name='定投记录', index=False)
            daily_df.to_excel(writer, sheet_name='每日数据', index=False)
            
            # 汇总统计
            summary_data = {
                '项目': ['初始资金(港元)', '20年定投总额(港元)', '最终现金(港元)', 
                        '交易总盈亏(港元)', '最终总价值(港元)', '总收益(港元)',
                        '总收益率(%)', '年化收益率(%)', '总交易次数', '盈利次数', 
                        '胜率(%)', '最大单笔盈利(港元)', '最大单笔亏损(港元)',
                        '平均每笔盈亏(港元)'],
                '数值': [self.initial_capital, total_invested, round(final_cash, 0),
                        round(total_trading_profit, 0), round(final_total_value, 0), 
                        round(total_return, 0), round(total_return_rate, 2), 
                        round(annual_return_rate, 2), total_trades, winning_trades,
                        round(win_rate, 1), round(max_profit, 0) if total_trades > 0 else 0,
                        round(max_loss, 0) if total_trades > 0 else 0, 
                        round(avg_profit, 0) if total_trades > 0 else 0]
            }
            pd.DataFrame(summary_data).to_excel(writer, sheet_name='汇总统计', index=False)
            
            if len(zone_stats) > 0:
                zone_stats.to_excel(writer, sheet_name='区域分析')
        
        print(f"\n✅ 详细结果已保存至: {filename}")
        return filename

def main():
    """主函数"""
    print("🏢 恒生指数每月定投2000港币策略回测系统")
    print("=" * 60)
    print("💰 初始资金: 30,000港元")
    print("📅 每月定投: 2,000港元")
    print("📈 每次交易: 50个恒指点子")
    print("📊 分析周期: 20年")
    print("🔄 使用复利计算")
    print("📊 包含详细图表分析")
    
    # 创建回测器
    backtester = HSI2000MonthlyBacktest()
    
    # 获取数据
    if not backtester.fetch_hsi_data():
        return
    
    # 计算指标
    backtester.calculate_indicators()
    
    # 执行回测
    trades_df, monthly_df, daily_df, total_invested, final_cash = backtester.backtest_with_monthly_investment()
    
    # 分析结果
    backtester.analyze_results(trades_df, monthly_df, daily_df, total_invested, final_cash)
    
    # 创建图表
    backtester.create_charts(trades_df, monthly_df, daily_df, total_invested, final_cash)

if __name__ == "__main__":
    main()
