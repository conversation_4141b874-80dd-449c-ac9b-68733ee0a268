#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HK00023东亚银行专用交易记录Excel
==============================

专门为HK00023东亚银行生成20条买卖交易记录
使用30000港币初始资金，20年历史数据回测

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class HK00023ExcelGenerator:
    def __init__(self):
        """初始化HK00023交易记录生成器"""
        self.symbol = "0023.HK"
        self.initial_capital = 30000
        self.data = None
        
    def fetch_hk00023_data(self):
        """获取HK00023东亚银行数据"""
        print("🏦 获取HK00023东亚银行20年历史数据...")
        
        try:
            ticker = yf.Ticker(self.symbol)
            # 获取20年数据
            self.data = ticker.history(period="20y", interval="1d")
            
            if self.data.empty:
                print("❌ 无法获取HK00023数据")
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取HK00023数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data)} 天")
            print(f"   • 价格范围: {self.data['close'].min():.2f} - {self.data['close'].max():.2f} 港币")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取HK00023数据失败: {e}")
            return False
    
    def calculate_cosmoon_indicators(self):
        """计算Cosmoon博弈论指标"""
        print("🎯 计算Cosmoon博弈论Y、X、E值...")
        
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        self.data['ma_120'] = self.data['close'].rolling(window=120).mean()
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # Y值计算 - 控股商托价概率
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        price_vs_ma60 = self.data['close'] / self.data['ma_60']
        
        # 基础Y值：基于均值回归理论
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
        
        # 趋势调整
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        
        # 成交量调整
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))
        
        # 波动率调整
        self.data['volatility'] = self.data['close'].pct_change().rolling(window=20).std()
        volatility_adjustment = 0.05 * np.tanh((self.data['volatility'] - 0.02) * 50)
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment + volatility_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        # X值计算 - 资金流入比例
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        
        # RSI调整
        rsi_adjustment = 0.3 * (self.data['rsi'] / 100 - 0.5)
        
        # 价格动量调整
        momentum = self.data['close'].pct_change(5).fillna(0)
        momentum_adjustment = 0.2 * np.tanh(momentum * 10)
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment + momentum_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        # E值计算 - Cosmoon博弈论核心公式
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        # 策略区域分类
        conditions = [
            (self.data['y_probability'] > 0.5) & (self.data['inflow_ratio'] > 0.5),  # 高值盈利区
            (self.data['y_probability'] > 0.333) & (self.data['y_probability'] < 0.4),  # 控股商控制区
            (self.data['y_probability'] < 0.25) | (self.data['inflow_ratio'] < 0.25),  # 强亏损区
        ]
        
        choices = ['高值盈利区', '控股商控制区', '强亏损区']
        self.data['strategy_zone'] = np.select(conditions, choices, default='其他区域')
        
        print(f"✅ Cosmoon指标计算完成:")
        print(f"   • Y值范围: {self.data['y_probability'].min():.3f} - {self.data['y_probability'].max():.3f}")
        print(f"   • X值范围: {self.data['inflow_ratio'].min():.3f} - {self.data['inflow_ratio'].max():.3f}")
        print(f"   • E值范围: {self.data['e_value'].min():.3f} - {self.data['e_value'].max():.3f}")
        
        # 统计各区域分布
        zone_counts = self.data['strategy_zone'].value_counts()
        total = len(self.data)
        print(f"📊 HK00023策略区域分布:")
        for zone, count in zone_counts.items():
            print(f"   • {zone}: {count} 天 ({count/total*100:.1f}%)")
    
    def generate_20_trades(self):
        """生成20条HK00023交易记录"""
        print("🎯 生成HK00023的20条交易记录...")
        
        trades = []
        current_cash = self.initial_capital
        
        # 选择有代表性的交易时期
        # 从2020年开始选择，确保有足够的历史数据计算指标
        start_date = pd.to_datetime('2020-01-01', utc=True).tz_convert('Asia/Hong_Kong')
        trading_data = self.data[self.data['date'] >= start_date].copy()
        
        if len(trading_data) < 100:
            print("❌ 可用交易数据不足")
            return []
        
        trade_count = 0
        i = 60  # 从第60天开始，确保指标计算完整
        
        while trade_count < 20 and i < len(trading_data) - 10:
            row = trading_data.iloc[i]
            
            date = row['date']
            price = row['close']
            y_val = row['y_probability']
            x_val = row['inflow_ratio']
            e_val = row['e_value']
            zone = row['strategy_zone']
            rsi = row['rsi']
            
            # 跳过控股商控制区
            if zone == '控股商控制区':
                i += 1
                continue
            
            # 确定交易策略
            action = None
            direction = None
            reason = None
            
            if zone == '高值盈利区':
                action = '买入'
                direction = '做多'
                reason = f'高值盈利区：Y={y_val:.3f}>0.5且X={x_val:.3f}>0.5，E={e_val:.3f}>0，买涨策略'
            elif zone == '强亏损区':
                action = '买入'
                direction = '做多'
                reason = f'强亏损区：Y={y_val:.3f}<0.25或X={x_val:.3f}<0.25，低位反弹机会，买涨策略'
            elif zone == '其他区域':
                action = '卖出'
                direction = '做空'
                reason = f'其他区域：Y={y_val:.3f}，X={x_val:.3f}，E={e_val:.3f}，买跌策略'
            
            if action and current_cash > 5000:  # 确保有足够资金交易
                # 计算仓位 - 根据凯利公式
                if e_val > 0:
                    kelly_fraction = min(0.15, max(0.05, e_val * 0.1))  # 5%-15%仓位
                else:
                    kelly_fraction = 0.08  # E<0时用8%仓位
                
                position_value = current_cash * kelly_fraction
                shares = int(position_value / price / 100) * 100  # 整手交易
                actual_value = shares * price
                
                if shares >= 100:  # 至少1手
                    # 模拟持仓期
                    if zone == '高值盈利区':
                        holding_days = np.random.randint(3, 8)  # 3-7天
                    elif zone == '强亏损区':
                        holding_days = np.random.randint(2, 6)  # 2-5天
                    else:
                        holding_days = np.random.randint(4, 10)  # 4-9天
                    
                    exit_index = min(i + holding_days, len(trading_data) - 1)
                    exit_row = trading_data.iloc[exit_index]
                    exit_price = exit_row['close']
                    exit_date = exit_row['date']
                    
                    # 计算盈亏
                    if direction == '做多':
                        # 检查止盈止损
                        profit_pct = (exit_price - price) / price
                        if profit_pct >= 0.04:  # 止盈4%
                            exit_reason = '止盈'
                            profit_pct = 0.04
                            exit_price = price * 1.04
                        elif profit_pct <= -0.02:  # 止损2%
                            exit_reason = '止损'
                            profit_pct = -0.02
                            exit_price = price * 0.98
                        else:
                            exit_reason = '到期平仓'
                        
                        profit = profit_pct * actual_value
                    else:  # 做空
                        profit_pct = (price - exit_price) / price
                        if profit_pct >= 0.04:  # 止盈4%
                            exit_reason = '止盈'
                            profit_pct = 0.04
                            exit_price = price * 0.96
                        elif profit_pct <= -0.02:  # 止损2%
                            exit_reason = '止损'
                            profit_pct = -0.02
                            exit_price = price * 1.02
                        else:
                            exit_reason = '到期平仓'
                        
                        profit = profit_pct * actual_value
                    
                    # 交易成本
                    transaction_cost = actual_value * 0.0025 * 2  # 买卖各0.25%
                    net_profit = profit - transaction_cost
                    
                    # 更新现金
                    current_cash += net_profit
                    
                    # 记录交易
                    trade_record = {
                        '交易序号': trade_count + 1,
                        '股票代码': 'HK00023',
                        '股票名称': '东亚银行',
                        '开仓日期': date.strftime('%Y-%m-%d'),
                        '平仓日期': exit_date.strftime('%Y-%m-%d'),
                        '持仓天数': holding_days,
                        '交易方向': direction,
                        '开仓价格': round(price, 2),
                        '平仓价格': round(exit_price, 2),
                        '交易股数': shares,
                        '交易金额': round(actual_value, 0),
                        '毛利润': round(profit, 0),
                        '交易成本': round(transaction_cost, 0),
                        '净利润': round(net_profit, 0),
                        '收益率%': round(profit_pct * 100, 2),
                        'Y值': round(y_val, 3),
                        'X值': round(x_val, 3),
                        'E值': round(e_val, 3),
                        '策略区域': zone,
                        '交易理由': reason,
                        '平仓原因': exit_reason,
                        '账户余额': round(current_cash, 0),
                        'RSI': round(rsi, 1)
                    }
                    
                    trades.append(trade_record)
                    trade_count += 1
                    
                    print(f"交易{trade_count}: {date.strftime('%Y-%m-%d')} {direction} {shares}股 @ {price:.2f}, "
                          f"Y={y_val:.3f}, X={x_val:.3f}, E={e_val:.3f}, 盈亏: {net_profit:+.0f}")
            
            # 随机间隔，避免过于密集的交易
            i += np.random.randint(5, 15)
        
        return trades
    
    def create_hk00023_excel(self, trades):
        """创建HK00023专用Excel文件"""
        print("📄 创建HK00023交易记录Excel文件...")
        
        if not trades:
            print("❌ 没有交易记录可生成")
            return None
        
        # 创建交易记录DataFrame
        df_trades = pd.DataFrame(trades)
        
        # 计算汇总统计
        total_trades = len(df_trades)
        winning_trades = len(df_trades[df_trades['净利润'] > 0])
        losing_trades = len(df_trades[df_trades['净利润'] < 0])
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        total_profit = df_trades['净利润'].sum()
        final_capital = df_trades['账户余额'].iloc[-1] if len(df_trades) > 0 else self.initial_capital
        total_return = (final_capital / self.initial_capital - 1) * 100
        
        # 创建汇总统计
        summary_data = {
            '项目': [
                '股票代码', '股票名称', '初始资金(港币)', '最终资金(港币)', '总盈亏(港币)', 
                '总收益率(%)', '总交易次数', '盈利次数', '亏损次数', '胜率(%)', 
                '平均每笔盈亏(港币)', '最大单笔盈利(港币)', '最大单笔亏损(港币)',
                '做多次数', '做空次数', '高值盈利区交易', '强亏损区交易', '其他区域交易'
            ],
            '数值': [
                'HK00023', '东亚银行', self.initial_capital, final_capital, total_profit,
                round(total_return, 2), total_trades, winning_trades, losing_trades, round(win_rate, 1),
                round(total_profit/total_trades, 0) if total_trades > 0 else 0,
                df_trades['净利润'].max(), df_trades['净利润'].min(),
                len(df_trades[df_trades['交易方向'] == '做多']),
                len(df_trades[df_trades['交易方向'] == '做空']),
                len(df_trades[df_trades['策略区域'] == '高值盈利区']),
                len(df_trades[df_trades['策略区域'] == '强亏损区']),
                len(df_trades[df_trades['策略区域'] == '其他区域'])
            ]
        }
        summary_df = pd.DataFrame(summary_data)
        
        # 创建策略说明
        strategy_explanation = {
            '策略区域': ['高值盈利区', '强亏损区', '其他区域', '控股商控制区'],
            '条件': [
                'Y>0.5 且 X>0.5', 
                'Y<0.25 或 X<0.25', 
                '其他情况', 
                '0.333<Y<0.4'
            ],
            '操作': ['买涨(做多)', '买涨(做多)', '买跌(做空)', '观望'],
            '止盈': ['4%', '4%', '4%', '-'],
            '止损': ['2%', '2%', '2%', '-'],
            '说明': [
                '控股商托价概率高，资金大量流入，E>0强盈利信号',
                '控股商不托价或资金流出，但可能低位反弹机会',
                '在不确定区域保持谨慎，做空获利',
                '控股商控制市场的危险区域，避免交易'
            ]
        }
        strategy_df = pd.DataFrame(strategy_explanation)
        
        # 创建Excel文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"HK00023东亚银行交易记录_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 写入各个工作表
            df_trades.to_excel(writer, sheet_name='交易记录', index=False)
            summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
            strategy_df.to_excel(writer, sheet_name='策略说明', index=False)
            
            # 获取工作簿和工作表
            workbook = writer.book
            
            # 设置交易记录表格式
            worksheet1 = writer.sheets['交易记录']
            column_widths = {
                'A': 8, 'B': 10, 'C': 10, 'D': 12, 'E': 12, 'F': 8, 'G': 8, 'H': 10, 'I': 10, 'J': 10,
                'K': 12, 'L': 10, 'M': 10, 'N': 10, 'O': 10, 'P': 8, 'Q': 8, 'R': 8, 'S': 12, 'T': 40, 'U': 12, 'V': 12, 'W': 8
            }
            for col, width in column_widths.items():
                worksheet1.column_dimensions[col].width = width
            
            # 设置汇总统计表格式
            worksheet2 = writer.sheets['汇总统计']
            worksheet2.column_dimensions['A'].width = 20
            worksheet2.column_dimensions['B'].width = 15
            
            # 设置策略说明表格式
            worksheet3 = writer.sheets['策略说明']
            worksheet3.column_dimensions['A'].width = 15
            worksheet3.column_dimensions['B'].width = 20
            worksheet3.column_dimensions['C'].width = 15
            worksheet3.column_dimensions['D'].width = 8
            worksheet3.column_dimensions['E'].width = 8
            worksheet3.column_dimensions['F'].width = 50
        
        print(f"✅ HK00023 Excel文件已创建: {filename}")
        return filename

def main():
    """主函数"""
    print("🏦 HK00023东亚银行专用交易记录生成器")
    print("="*60)
    print("💰 初始资金: 30,000港币")
    print("📊 期间: 20年历史数据")
    print("🎯 目标: 生成20条买卖交易记录")
    print("📈 策略: Cosmoon博弈论 + 凯利公式")
    
    # 创建生成器
    generator = HK00023ExcelGenerator()
    
    # 获取HK00023数据
    if not generator.fetch_hk00023_data():
        return
    
    # 计算Cosmoon指标
    generator.calculate_cosmoon_indicators()
    
    # 生成20条交易
    trades = generator.generate_20_trades()
    
    if not trades:
        print("❌ 未能生成交易记录")
        return
    
    # 创建Excel文件
    filename = generator.create_hk00023_excel(trades)
    
    if filename:
        print(f"\n🎉 HK00023交易记录生成完成!")
        print(f"📄 文件名: {filename}")
        print(f"📊 包含 {len(trades)} 条HK00023交易记录")
        print(f"💡 请打开Excel文件查看详细的东亚银行交易信息")
        print(f"🎯 包含Y值、X值、E值和完整的博弈论策略分析")

if __name__ == "__main__":
    main()
